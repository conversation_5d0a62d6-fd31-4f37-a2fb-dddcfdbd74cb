﻿[Dictionary]
Version=CSPro 7.7
Label=SOCIAL
Name=SOCIAL_DICT
RecordTypeStart=1
RecordTypeLen=1
Positions=Relative
ZeroFill=No
DecimalChar=Yes
SecurityOptions=725C045E6C289DD395523CD457DAAE577DBA001A3BDCAF3E9F72B2010A91F41F

[Level]
Label=SOCIAL Level
Name=SOCIAL_LEVEL

[IdItems]

[Item]
Label=EA number
Name=A08
Start=2
Len=5
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Household number
Name=A10
Start=7
Len=4
DataType=Numeric
ZeroFill=Yes

[Record]
Label=IDENTIFICATION PANEL
Name=SECTA
RecordTypeValue='1'
RecordLen=573

[Item]
Label=Date of the interview
Name=A01
Start=11
Len=8
DataType=Numeric

[Item]
Label=Interviewer
Name=A02
Start=19
Len=3
DataType=Numeric

[Item]
Label=Supervisor
Name=A03
Start=22
Len=3
DataType=Numeric

[Item]
Label=Zone
Name=A04
Start=25
Len=1
DataType=Numeric

[ValueSet]
Label=Zone
Name=A04_VS1
Value=1;Urbain
Value=2;Rural

[Item]
Label=Region
Name=A05
Start=26
Len=1
DataType=Numeric

[Item]
Label=Wilaya
Name=A06
Start=27
Len=3
DataType=Numeric

[Item]
Label=Commune
Name=A07
Start=30
Len=3
DataType=Numeric

[Item]
Label=Town/ Settlement
Name=A09
Start=33
Len=100
DataType=Alpha

[Item]
Label=Take GPS
Name=A10GPS
Start=133
Len=1
DataType=Numeric

[ValueSet]
Label=Take GPS
Name=A10GPS_VS1
Value=1;PRENDRE LES COORDONNÉES GPS MAINTENANT
Value=2;REPRENDRE LE GPS
Value=3;PRENDRE PLUS TARD

[Item]
Label=Longitude
Name=A10A
Start=134
Len=13
DataType=Numeric
Decimal=6
DecimalChar=Yes

[Item]
Label=Latitude
Name=A10B
Start=147
Len=13
DataType=Numeric
Decimal=6
DecimalChar=Yes

[Item]
Label=Nom complet du répondant
Name=A11
Start=160
Len=50
DataType=Alpha

[Item]
Label=number Telephone
Name=A12
Start=210
Len=9
DataType=Numeric

[Item]
Label=Address
Name=A13
Start=219
Len=150
DataType=Alpha

[Item]
Label=Full name of head of household
Name=A14
Start=369
Len=50
DataType=Alpha

[Item]
Label=Result of the household interview
Name=A15
Start=419
Len=2
DataType=Numeric

[ValueSet]
Label=Result of the household interview
Name=A15_VS1
Value=1;COMPLET
Value=2;AUCUN MEMBRE DU MMAUCUN MEMBRE DU MÉNAGE À LA MAISON OU AUCUN RÉPONDANT COMPÉTENT À LA MAISON AU MOMENT DE LA VISITE
Value=3;TOUT LE MÉNAGE ABSENT PENDANT UNE PÉRIODE PROLONGÉE
Value=4;REFUSE
Value=5;REPORTÉ
Value=6;LOGEMENT VACANT OU ADRESSE NON LOGEMEN
Value=7;LOGEMENT DÉTRUIT
Value=8;LOGEMENT INTROUVABLE
Value=9;PARTIELLEMENT COMPLÉTÉ
Value=96;AUTRE

[Item]
Label=Other result
Name=A15X
Start=421
Len=150
DataType=Alpha

[Item]
Label=Consent
Name=A16
Start=571
Len=1
DataType=Numeric

[ValueSet]
Label=Consent
Name=A16_VS1
Value=1;OUI
Value=2;NON

[Item]
Label=House size
Name=HHSIZE
Start=572
Len=2
DataType=Numeric

[Record]
Label=Liste des membres du ménage
Name=QHSEC01X
RecordTypeValue='X'
Required=No
MaxRecords=90
RecordLen=54

[Item]
Label=Ligne du repondant
Name=QHLINE
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Ligne du repondant
Name=QHLINE_VS1
Value=1:90

[Item]
Label=Prenoms
Name=QHFIRSTN
Start=13
Len=12
DataType=Alpha

[Item]
Label=Nom
Name=QHLASTN
Start=25
Len=12
DataType=Alpha

[Item]
Label=Date de naissance
Name=QHDATE
Start=37
Len=8
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Jour de naissance
Name=QHJOUR
Start=37
Len=2
ItemType=SubItem
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Jour de naissance
Name=QHJOUR_VS1
Value=1:31

[Item]
Label=Mois de naissance
Name=QHMOIS
Start=39
Len=2
ItemType=SubItem
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Mois de naissance
Name=QHMOIS_VS1
Value=1;Janvier
Value=2;Février
Value=3;Mars
Value=4;Avril
Value=5;Mai
Value=6;Juin
Value=7;Juillet
Value=8;Août
Value=9;Septembre
Value=10;Octobre
Value=11;Novembre
Value=12;Décembre
Value=97;Incohérent
Value=98;NSP mois

[Item]
Label=Année de naissance
Name=QHANNEE
Start=41
Len=4
ItemType=SubItem
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Année de naissance
Name=QHANNEE_VS1
Value=1900:2025

[Item]
Label=Age en année
Name=QHAGE
Start=45
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Age en année
Name=QHAGE_VS1
Value=0:94
Value=95;95+
Value=98;Ne sait pas

[Item]
Label=Age en mois
Name=QHAGF
Start=47
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Age en mois
Name=QHAGF_VS1
Value=0:94
Value=95;95+
Value=98;Ne sait pas

[Item]
Label=Sexe
Name=QHSEX
Start=49
Len=1
DataType=Numeric

[ValueSet]
Label=Sexe
Name=QHSEX_VS1
Value=1;Masculin
Value=2;Feminin

[Item]
Label=Numéro de famille et lien de parenté au sein de la famille
Name=QHFAM
Start=50
Len=2
DataType=Numeric

[ValueSet]
Label=Numéro de famille et lien de parenté au sein de la famille
Name=QHFAM_VS1
Value=1;Chef de ménage
Value=2;Epoux / épouse du CM
Value=3;Fils / fille du CM
Value=4;Enfant adoptif du CM
Value=5;Chef de famille CF
Value=6;Epoux / épouse CF
Value=7;Fils / fille CF
Value=8;Enfant adoptif  CF
Value=9;Autre parent du CM
Value=10;Sans lien de parenté avec le CM

[Item]
Label=Lien de parenté avec le chef de menage
Name=QHRELAT
Start=52
Len=1
DataType=Numeric

[ValueSet]
Label=Lien de parenté avec le chef de menage
Name=QHRELAT_VS1
Value=1;Chef de ménage
Value=2;Conjoint(e)
Value=3;Fils/ fille
Value=4;Père / mère
Value=5;Frère / sœur
Value=6;Autre parent du CM/ conjoint
Value=7;Personne non apparentée au CM ni au conjoint
Value=8;Domestique ou parent du domestique

[Item]
Label=Autre membre vivant dans le menage
Name=QHMORE
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=Autre membre vivant dans le menage
Name=QHMORE_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Situation matrimonial
Name=QHSM
Start=54
Len=1
DataType=Numeric

[ValueSet]
Label=Situation matrimonial
Name=QHSM_VS1
Value=1;Célibataire
Value=2;Marié(e)
Value=3;Veuf (ve)
Value=4;Divorcé(e)
Value=5;Séparé(e)

[Record]
Label=Echelle de mesure de l'insécurité alimentaire basée sur les expériences
Name=QHSEC02
RecordTypeValue='4'
Required=No
RecordLen=60

[Item]
Label=Code ID du répondant
Name=EM00
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Votre ménage n’avez pas pu manger une nourriture saine et nutritive
Name=EM02
Start=13
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage n’avez pas pu manger une nourriture saine et nutritive
Name=EM02_VS1
Value=1;Oui
Value=2;Non
Value=8;Ne sait pas
Value=9;Refus

[Item]
Label=Votre ménage avez mangé une nourriture peu variée
Name=EM03
Start=14
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage avez mangé une nourriture peu variée
Name=EM03_VS1
Value=1;Oui
Value=2;Non
Value=8;Ne sait pas
Value=9;Refus

[Item]
Label=votre ménage avez dû sauter un repas
Name=EM04
Start=15
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=votre ménage avez dû sauter un repas
Name=EM04_VS1
Value=1;Oui
Value=2;Non
Value=8;Ne sait pas
Value=9;Refus

[Item]
Label=Votre ménage avez mangé moins que ce que vous pensiez que vous auriez dû manger
Name=EM05
Start=16
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage avez mangé moins que ce que vous pensiez que vous auriez dû manger
Name=EM05_VS1
Value=1;Oui
Value=2;Non
Value=8;Ne sait pas
Value=9;Refus

[Item]
Label=Votre ménage n’avait plus de nourriture
Name=EM06
Start=17
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage n’avait plus de nourriture
Name=EM06_VS1
Value=1;Oui
Value=2;Non
Value=8;Ne sait pas
Value=9;Refus

[Item]
Label=Votre ménage avez eu faim mais vous n’avez pas mangé
Name=EM07
Start=18
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage avez eu faim mais vous n’avez pas mangé
Name=EM07_VS1
Value=1;Oui
Value=2;Non
Value=8;Ne sait pas
Value=9;Refus

[Item]
Label=Votre ménage avez passé toute une journée sans manger
Name=EM08
Start=19
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage avez passé toute une journée sans manger
Name=EM08_VS1
Value=1;Oui
Value=2;Non
Value=8;Ne sait pas
Value=9;Refus

[Item]
Label=ID du principal répondant equipement du ménage
Name=EQUIP_00
Start=20
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=ID du principal répondant equipement du ménage
Name=EQUIP_00_VS1
Value=1:45

[Item]
Label=ID du principal répondant education
Name=EQUIP_01
Start=22
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=ID du principal répondant education
Name=EQUIP_01_VS1
Value=1:45

[Item]
Label=ID du principal répondant sante
Name=EQUIP_02
Start=24
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=ID du principal répondant sante
Name=EQUIP_02_VS1
Value=1:45

[Item]
Label=ID du principal répondant vacance et déplacement
Name=EQUIP_03
Start=26
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=ID du principal répondant vacance et déplacement
Name=EQUIP_03_VS1
Value=1:45

[Item]
Label=ID du principal répondant partie A activité économique
Name=EQUIP_04
Start=28
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=ID du principal répondant partie A activité économique
Name=EQUIP_04_VS1
Value=1:45

[Item]
Label=nombre  d'entités non agricoles possédées par le ménage
Name=NANAG
Start=30
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=nombre  d'entités non agricoles possédées par le ménage
Name=NANAG_VS1
Value=0:90

[Item]
Label=Effectué une dépense ou une acquisition alimentaire destinée a être consommée ou renouvelée pour plus
Name=ALI01
Start=32
Len=1
DataType=Numeric

[ValueSet]
Label=Effectué une dépense ou une acquisition alimentaire destinée a être consommée ou renouvelée pour plus
Name=ALI01_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Durant les 11 derniers mois, avez-vous effectué une dépense ou une acquisition alimentaire destinée a etre consommée ou renouvelée pour plus de 3 mois
Name=ALI02
Start=33
Len=1
DataType=Numeric

[ValueSet]
Label=Durant les 11 derniers mois, avez-vous effectué une dépense ou une acquisition alimentaire destinée a etre consommée ou renouvelée pour plus de 3 mois
Name=ALI02_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Durant les deux derniers mois, avez effectué une dépense ou une acquisition non alimentaire dont la durée de renouvellement ne dépasse pas 3 mois
Name=ALI03
Start=34
Len=1
DataType=Numeric

[ValueSet]
Label=Durant les deux derniers mois, avez effectué une dépense ou une acquisition non alimentaire dont la durée de renouvellement ne dépasse pas 3 mois
Name=ALI03_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Durant les 11 derniers mois, avez-vous effectué une dépense ou une acquisition non alimentaire dont la durée de renouvellement dépasse 3 mois ?
Name=ALI04
Start=35
Len=1
DataType=Numeric

[ValueSet]
Label=Durant les 11 derniers mois, avez-vous effectué une dépense ou une acquisition non alimentaire dont la durée de renouvellement dépasse 3 mois ?
Name=ALI04_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Ménage ou un de ses membres a-t-il envoyé de l'argent ou des biens à une personne
Name=TR12A
Start=36
Len=1
DataType=Numeric

[ValueSet]
Label=Ménage ou un de ses membres a-t-il envoyé de l'argent ou des biens à une personne
Name=TR12A_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Reçu de l'argent ou des biens provenant d'une personne n'habitant pas dans le ménage
Name=TR12
Start=37
Len=1
DataType=Numeric

[ValueSet]
Label=Reçu de l'argent ou des biens provenant d'une personne n'habitant pas dans le ménage
Name=TR12_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=ID du principal répondant
Name=TR12C
Start=38
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=ID du principal répondant
Name=TR12C_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=ID du principal répondant
Name=TR12B
Start=39
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=ID du principal répondant
Name=TR12B_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=ID du principal répondant pansions
Name=TR12D
Start=40
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=ID du principal répondant pansions
Name=TR12D_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Membre de votre ménage,exercé une activité de pêche ou d'aquaculture
Name=TR12E
Start=41
Len=1
DataType=Numeric

[ValueSet]
Label=Membre de votre ménage,exercé une activité de pêche ou d'aquaculture
Name=TR12E_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Membre de votre ménage, collecté du bois, charbon du bois
Name=TR12F
Start=42
Len=1
DataType=Numeric

[ValueSet]
Label=Membre de votre ménage, collecté du bois, charbon du bois
Name=TR12F_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Votre ménage, ou un membre de votre ménage, a possédé des terres agricoles ou exploité
Name=TRIDE
Start=43
Len=1
DataType=Numeric

[ValueSet]
Label=Votre ménage, ou un membre de votre ménage, a possédé des terres agricoles ou exploité
Name=TRIDE_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Nombre de ces  parcelles ou fermes
Name=NPAR
Start=44
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de ces  parcelles ou fermes
Name=NPAR_VS1
Value=0:90

[Item]
Label=Total cultures
Name=TRIDF
Start=46
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Total elevage
Name=TRIDG
Start=48
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de lignes pour la presence aux repas
Name=PRESR
Start=50
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de lignes pour la presence aux repas
Name=PRESR_VS1
Value=0:50

[Item]
Label=Nombre de lignes pour la depense consommation
Name=PRESS
Start=52
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de lignes pour la depense consommation
Name=PRESS_VS1
Value=0:50

[Item]
Label=Nombre de lignes pour la depense du mois à l'exterieur
Name=PREST
Start=54
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de lignes pour la depense du mois à l'exterieur
Name=PREST_VS1
Value=0:50

[Item]
Label=Total Persons in Household
Name=QHMEMBER
Start=56
Len=2
DataType=Numeric

[ValueSet]
Label=Total Persons in Household
Name=QHMEMBER_VS1
Value=1:90

[Item]
Label=Line number of respondent to HH quest
Name=QHRESP
Start=58
Len=2
DataType=Numeric

[ValueSet]
Label=Line number of respondent to HH quest
Name=QHRESP_VS1
Value=1:90

[Item]
Label=Interview notes
Name=QHFINISH
Start=60
Len=1
DataType=Alpha

[Record]
Label=Logement
Name=QHSEC03
RecordTypeValue='5'
Required=No
RecordLen=306

[Item]
Label=Code ID du principal répondant à la section
Name=H00
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code ID du principal répondant à la section
Name=H00_VS1
Value=1:50

[Item]
Label=Type de construction
Name=H01
Start=13
Len=1
DataType=Numeric

[ValueSet]
Label=Type de construction
Name=H01_VS1
Value=1;Appartement dans un immeuble
Value=2;Villa ou étage de villa
Value=3;Maison individuelle
Value=4;Maison traditionnelle (haouch)
Value=5;Établissement à usage professionnel
Value=6;Construction précaire
Value=7;Bidonvielle /gourbi
Value=9;Autre à spécifier

[Item]
Label=Date d'achèvement du logement
Name=H02A
Start=14
Len=1
DataType=Numeric

[ValueSet]
Label=Date d'achèvement du logement
Name=H02A_VS1
Value=1;Avant 1966
Value=2;Après 1966

[Item]
Label=Année d'achèvement du logement
Name=H02
Start=15
Len=4
DataType=Numeric

[ValueSet]
Label=Année d'achèvement du logement
Name=H02_VS1
Value=1966:2025;Annee d'achevement
Value=9999;.

[Item]
Label=Nombre d'année de residence dans le logement
Name=H03
Start=19
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Frais du déménagement
Name=H04
Start=21
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Appartenance du logement
Name=H05
Start=33
Len=1
DataType=Numeric

[ValueSet]
Label=Appartenance du logement
Name=H05_VS1
Value=1;Particulier
Value=2;Organisme d’habitation
Value=3;Entreprise nationale et administration
Value=4;Entreprise privée

[Item]
Label=Titre du logement
Name=H06
Start=34
Len=1
DataType=Numeric

[ValueSet]
Label=Titre du logement
Name=H06_VS1
Value=1;Propriétaire ou co-propriétaire
Value=2;Accession à la propriété
Value=3;Location chez le privé
Value=4;Location chez le public
Value=5;Logé gratuitement
Value=6;Indu-occupant

[Item]
Label=Mode d'acquisition du logement
Name=H07
Start=35
Len=1
DataType=Numeric

[ValueSet]
Label=Mode d'acquisition du logement
Name=H07_VS1
Value=1;Hérité
Value=2;Construit sur fonds propres
Value=3;Construit sur fonds propres et prêts
Value=4;Acheté sur fonds propres
Value=5;Acheté sur fonds propres et prêts
Value=9;Autre à spécifier

[Item]
Label=Montant du loyer que vous pourriez recevoir par mois si vous louiez cette habitation à quelqu'un d'autre
Name=H08
Start=36
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant du loyer que vous pourriez payer par mois si vous deviez louer une habitation
Name=H09
Start=48
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant mensuel du loyer que votre ménage paie pour cette habitation
Name=H10
Start=60
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de ménages habitent ce logement, compris votre ménage
Name=H11
Start=72
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de personnes habitent ce logement, y compris les personnes non membres du ménage enquêté
Name=H12
Start=74
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de pièces est constitué ce logement
Name=H13
Start=76
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de pièces sont occupées par le ménage enquêté
Name=H14
Start=78
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Vous avez une cuisine dans ce logement
Name=H15
Start=80
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Vous avez une cuisine dans ce logement
Name=H15_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Cette cuisine est utilisée aussi comme chambre à coucher
Name=H16
Start=81
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Cette cuisine est utilisée aussi comme chambre à coucher
Name=H16_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Votre logement dispose d'une baignoire
Name=H17A
Start=82
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre logement dispose d'une baignoire
Name=H17A_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Votre logement dispose d'un receveur de douche
Name=H17B
Start=83
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre logement dispose d'un receveur de douche
Name=H17B_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Votre logement dispose d'un Lavabo
Name=H17C
Start=84
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre logement dispose d'un Lavabo
Name=H17C_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Votre logement dispose de toilettes
Name=H17D
Start=85
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre logement dispose de toilettes
Name=H17D_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Emplacement des toilette
Name=H18
Start=86
Len=1
DataType=Numeric

[ValueSet]
Label=Emplacement des toilette
Name=H18_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Nombre de de ménages utilisent ces toilettes, y compris votre ménage
Name=H19
Start=87
Len=1
DataType=Numeric

[Item]
Label=Les membres de votre ménage se lavent-ils les mains
Name=H20
Start=88
Len=1
DataType=Numeric

[ValueSet]
Label=Les membres de votre ménage se lavent-ils les mains
Name=H20_VS1
Value=1;Lieu aménagé (lavabo, robinet, etc..)
Value=2;Dans la cour/jardin/parcelle
Value=3;Ustensile mobile (seau, cuvette, etc..)

[Item]
Label=évacuations de vos eaux usées
Name=H21
Start=89
Len=1
DataType=Numeric

[ValueSet]
Label=évacuations de vos eaux usées
Name=H21_VS1
Value=1;Réseau d'égout
Value=2;Fosse septique
Value=3;Jetées dans la nature
Value=9;Autre (spécifier )

[Item]
Label=débarras de vos ordures
Name=H22
Start=90
Len=1
DataType=Numeric

[ValueSet]
Label=débarras de vos ordures
Name=H22_VS1
Value=1;Ramassage par camion d'ordures
Value=2;Poubelle communale de collecte des ordures
Value=3;Brûlage des ordures
Value=4;Jetées dans la nature
Value=9;Autre (spécifier )

[Item]
Label=Rattaché au réseau d’AEP
Name=H23
Start=91
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Rattaché au réseau d’AEP
Name=H23_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Compteur d'eau
Name=H24
Start=92
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Compteur d'eau
Name=H24_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Fréquence de la disponibilité de l’eau, en moyenne
Name=H25
Start=93
Len=1
DataType=Numeric

[ValueSet]
Label=Fréquence de la disponibilité de l’eau, en moyenne
Name=H25_VS1
Value=1;Tous les jours
Value=2;Un jour sur deux
Value=3;Un jour sur trois
Value=4;Une fois par semaine
Value=5;Irrégulièrement
Value=6;Pas d'eau du tout

[Item]
Label=Nombre d'heures en moyenne par jour
Name=H26
Start=94
Len=1
DataType=Numeric

[ValueSet]
Label=Nombre d'heures en moyenne par jour
Name=H26_VS1
Value=1;Toute la journée (24 heures)
Value=2;De 12 à moins de 24 heures
Value=3;De 2 à moins de 12 heures
Value=4;Moins de 2 heures

[Item]
Label=Sources d’approvisionnement en eau
Name=H27
Start=95
Len=1
DataType=Numeric

[ValueSet]
Label=Sources d’approvisionnement en eau
Name=H27_VS1
Value=1;Eau courante
Value=2;Fontaine publique, source
Value=3;Puits
Value=4;Eau de surface (oued, lac, barrage…)
Value=5;Citerne publique
Value=6;Achat d'eau camion citerne privé
Value=9;Autre à spécifier

[Item]
Label=Distance de la source d'approvisionnement en eau
Name=H28A
Start=96
Len=1
DataType=Numeric

[ValueSet]
Label=Distance de la source d'approvisionnement en eau
Name=H28A_VS1
Value=1;Moins de 350 m
Value=2;De 350 à moins de 1 km
Value=3;De 1 km à 2 km
Value=4;Plus de 2 km

[Item]
Label=Nombre de temps faut-il pour arriver à la source d'approvisionnement à pied
Name=H28B
Start=97
Len=1
DataType=Numeric

[ValueSet]
Label=Nombre de temps faut-il pour arriver à la source d'approvisionnement à pied
Name=H28B_VS1
Value=1;Moins de 5mn
Value=2;5 mns à 15 mns
Value=3;5 mns à 15 mns
Value=3;15 mns à 30 mns
Value=4;Plus de 30 mns

[Item]
Label=Dispose de l’électricité
Name=H29
Start=98
Len=1
DataType=Numeric

[ValueSet]
Label=Dispose de l’électricité
Name=H29_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Princpale source d'électricité
Name=H30
Start=99
Len=1
DataType=Numeric

[ValueSet]
Label=Princpale source d'électricité
Name=H30_VS1
Value=1;Compteur Sonelgaz
Value=2;Groupe électrogène
Value=3;Branché sur le réseau
Value=4;Branché chez les voisins
Value=9;Autre (spécifier)

[Item]
Label=Principal moyen de combustion pour le chauffage
Name=H31
Start=100
Len=1
DataType=Numeric

[ValueSet]
Label=Principal moyen de combustion pour le chauffage
Name=H31_VS1
Value=1;Gaz de ville
Value=2;Gaz butane
Value=3;Mazout
Value=4;Charbon / bois
Value=5;Electricité
Value=9;Autre (spécifier)

[Item]
Label=Principal moyen de combustion pour la cuisson
Name=H32
Start=101
Len=1
DataType=Numeric

[ValueSet]
Label=Principal moyen de combustion pour la cuisson
Name=H32_VS1
Value=1;Gaz de ville
Value=2;Gaz butane
Value=3;Mazout
Value=4;Charbon / bois
Value=5;Electricité
Value=9;Autre (spécifier)

[Item]
Label=Votre ménage dispose de Téléphone-ligne fixe
Name=H33A
Start=102
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage dispose de Téléphone-ligne fixe
Name=H33A_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Votre ménage dispose de téléphone- ligne cellulaire
Name=H33B
Start=103
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage dispose de téléphone- ligne cellulaire
Name=H33B_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Votre ménage dispose de Branchement à la parabole
Name=H33C
Start=104
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage dispose de Branchement à la parabole
Name=H33C_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Votre ménage dispose de connection internet câble
Name=H33D
Start=105
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage dispose de connection internet câble
Name=H33D_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Votre ménage dispose de Connection internet mobile
Name=H33E
Start=106
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Votre ménage dispose de Connection internet mobile
Name=H33E_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Une assurance pour ce logement
Name=H34
Start=107
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Une assurance pour ce logement
Name=H34_VS1
Value=1;Oui
Value=2;Non
Value=8;Ne sait pas

[Item]
Label=Estimez le montant annuel pour l'assurance
Name=H35
Start=108
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Jugement de l'état de votre logement
Name=H36
Start=120
Len=1
DataType=Numeric

[ValueSet]
Label=Jugement de l'état de votre logement
Name=H36_VS1
Value=1;Bon état
Value=2;Moyen
Value=3;Délabré

[Item]
Label=Satisfait de votre logement
Name=H37
Start=121
Len=1
DataType=Numeric

[ValueSet]
Label=Satisfait de votre logement
Name=H37_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Raison de non satisfaction
Name=H38
Start=122
Len=10
DataType=Alpha

[ValueSet]
Label=Raison de non satisfaction
Name=H38_VS1
Value='A         ';Pas confortable
Value='B         ';Étroit / à la taille du ménage
Value='C         ';Mauvais voisinage
Value='D         ';Éloigné du lieu de travail
Value='E         ';Éloigné de la ville
Value='F         ';Problème de sécurité
Value='G         ';Environnement (pollution, bruit)
Value='H         ';Manque d'infrastructures d'éducation
Value='I         ';Manque de services
Value='X         ';Autre (spécifier )

[Item]
Label=Entrain de construire
Name=H39
Start=132
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Entrain de construire
Name=H39_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Distance pour se rendre à l'école primaire
Name=H40A
Start=133
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre à l'école primaire
Name=H40A_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre à l'école primaire
Name=H40B
Start=134
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre à l'école primaire
Name=H40B_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre à l'école coranique
Name=H40C
Start=135
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre à l'école coranique
Name=H40C_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre à l'école coranique
Name=H40D
Start=136
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre à l'école coranique
Name=H40D_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre au CEM
Name=H40E
Start=137
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre au CEM
Name=H40E_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre au CEM
Name=H40F
Start=138
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre au CEM
Name=H40F_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre au Lycée
Name=H40G
Start=139
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre au Lycée
Name=H40G_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre au Lycée
Name=H40I
Start=140
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre au Lycée
Name=H40I_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre à Hopital
Name=H40J
Start=141
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre à Hopital
Name=H40J_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre à Hopital
Name=H40K
Start=142
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre à Hopital
Name=H40K_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre à la salle de soins
Name=H40L
Start=143
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre à la salle de soins
Name=H40L_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre à la salle de soins
Name=H40M
Start=144
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre à la salle de soins
Name=H40M_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre au bureau de poste
Name=H40N
Start=145
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre au bureau de poste
Name=H40N_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre au bureau de poste
Name=H40O
Start=146
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre au bureau de poste
Name=H40O_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre à la poste de police
Name=H40P
Start=147
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre à la poste de police
Name=H40P_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre à la poste de police
Name=H40Q
Start=148
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre à la poste de police
Name=H40Q_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre à la maison de jeunes
Name=H40R
Start=149
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre à la maison de jeunes
Name=H40R_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre à la maison de jeunes
Name=H40S
Start=150
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre à la maison de jeunes
Name=H40S_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre à la salle de sports
Name=H40T
Start=151
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre à la salle de sports
Name=H40T_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre  à la salle de sports
Name=H40U
Start=152
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre  à la salle de sports
Name=H40U_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre à la crèche
Name=H40V
Start=153
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre à la crèche
Name=H40V_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre à la crèche
Name=H40W
Start=154
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre à la crèche
Name=H40W_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Distance pour se rendre à l'arrêt de bus
Name=H40AA
Start=155
Len=1
DataType=Numeric

[ValueSet]
Label=Distance pour se rendre à l'arrêt de bus
Name=H40AA_VS1
Value=1;Moins de 1 km
Value=2;de 1 km à moins de 2 km
Value=3;de 2 km à moins de 4 km
Value=4;plus de 4 km

[Item]
Label=Temps pour se rendre à l'arrêt de bus
Name=H40AB
Start=156
Len=1
DataType=Numeric

[ValueSet]
Label=Temps pour se rendre à l'arrêt de bus
Name=H40AB_VS1
Value=1;Moins de 15 min
Value=2;15 min à moins de 30 min
Value=3;de 30 min à moins d'1 h
Value=4;plus d'1 h

[Item]
Label=Montant de la dernière facture payée au cours des 12 derniers mois pour l'eau
Name=H41A
Start=157
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Périodicité du paiement de l'Eau
Name=H41B
Start=169
Len=1
DataType=Numeric

[ValueSet]
Label=Périodicité du paiement de l'Eau
Name=H41B_VS1
Value=1;Mensuel
Value=2;Bimestriel
Value=3;Trimestriel
Value=4;Semestriel
Value=5;Annuel

[Item]
Label=Montant annuel total des factures d'Eau
Name=H41C
Start=170
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de la dernière facture payée au cours des 12 derniers mois pour gaz de ville
Name=H41D
Start=182
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Périodicité du paiement de gaz de ville
Name=H41E
Start=194
Len=1
DataType=Numeric

[ValueSet]
Label=Périodicité du paiement de gaz de ville
Name=H41E_VS1
Value=1;Mensuel
Value=2;Bimestriel
Value=3;Trimestriel
Value=4;Semestriel
Value=5;Annuel

[Item]
Label=Montant annuel total des factures gaz de ville
Name=H41F
Start=195
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de la dernière facture payée au cours des 12 derniers mois pour électricité
Name=H41G
Start=207
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Périodicité du paiement d'électricité
Name=H41H
Start=219
Len=1
DataType=Numeric

[ValueSet]
Label=Périodicité du paiement d'électricité
Name=H41H_VS1
Value=1;Mensuel
Value=2;Bimestriel
Value=3;Trimestriel
Value=4;Semestriel
Value=5;Annuel

[Item]
Label=Montant annuel total des factures d'électricité
Name=H41I
Start=220
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de la dernière facture payée au cours des 12 derniers mois pour telephone lixe fixe
Name=H41J
Start=232
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Périodicité du paiement de pour telephone lixe fixe
Name=H41K
Start=244
Len=1
DataType=Numeric

[ValueSet]
Label=Périodicité du paiement de pour telephone lixe fixe
Name=H41K_VS1
Value=1;Mensuel
Value=2;Bimestriel
Value=3;Trimestriel
Value=4;Semestriel
Value=5;Annuel

[Item]
Label=Montant annuel total des factures de pour telephone lixe fixe
Name=H41L
Start=245
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de la dernière facture payée au cours des 12 derniers mois pour abonnement télé câble/satellite
Name=H41M
Start=257
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Périodicité du paiement d'abonnement télé câble/satellite
Name=H41N
Start=269
Len=1
DataType=Numeric

[ValueSet]
Label=Périodicité du paiement d'abonnement télé câble/satellite
Name=H41N_VS1
Value=1;Mensuel
Value=2;Bimestriel
Value=3;Trimestriel
Value=4;Semestriel
Value=5;Annuel

[Item]
Label=Montant annuel total des factures d'abonnement télé câble/satellite
Name=H41O
Start=270
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de la dernière facture payée au cours des 12 derniers mois pour internet fixe
Name=H41P
Start=282
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Périodicité du paiement d'internet fixe
Name=H41Q
Start=294
Len=1
DataType=Numeric

[ValueSet]
Label=Périodicité du paiement d'internet fixe
Name=H41Q_VS1
Value=1;Mensuel
Value=2;Bimestriel
Value=3;Trimestriel
Value=4;Semestriel
Value=5;Annuel

[Item]
Label=Montant annuel total des factures d'internet fixe
Name=H41R
Start=295
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Equipement du ménage
Name=QHSEC04
RecordTypeValue='6'
Required=No
MaxRecords=48
RecordLen=90

[Item]
Label=Numéro du produit
Name=EM0
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numéro du produit
Name=EM0_VS1
Value=1:49

[Item]
Label=Description du produit
Name=EM1
Start=13
Len=50
DataType=Alpha

[Item]
Label=Possède
Name=EM2
Start=63
Len=1
DataType=Numeric

[ValueSet]
Label=Possède
Name=EM2_VS1
Value=1;Oui, acheté
Value=2;Oui, acquis avec le logement
Value=3;Non

[Item]
Label=Nombre d'articles
Name=EM3
Start=64
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre années d'acquisition
Name=EM4
Start=66
Len=1
DataType=Numeric

[ValueSet]
Label=Nombre années d'acquisition
Name=EM4_VS1
Value=1;0 à 12 mois
Value=2;+ 1 an à 5 ans
Value=3;+ 5 ans

[Item]
Label=Valeur d'acquisition
Name=EM5
Start=67
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Valeur (ou prix) de revente
Name=EM6
Start=79
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Education
Name=QHSEC05
RecordTypeValue='7'
Required=No
MaxRecords=90
RecordLen=244

[Item]
Label=Numero ligne du membre
Name=ED00A
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero ligne du membre
Name=ED00A_VS1
Value=1:50

[Item]
Label=Household member name
Name=ED00B
Start=13
Len=40
DataType=Alpha

[Item]
Label=Sait lire ou écrire
Name=ED01
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=Sait lire ou écrire
Name=ED01_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Confirmer que l'âge est moins de 6 ans
Name=ED02
Start=54
Len=1
DataType=Numeric

[ValueSet]
Label=Confirmer que l'âge est moins de 6 ans
Name=ED02_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Fréquenté l’un des établissements suivants
Name=ED03
Start=55
Len=1
DataType=Numeric

[ValueSet]
Label=Fréquenté l’un des établissements suivants
Name=ED03_VS1
Value=1;Jardin d'enfants ou Crèche privée
Value=2;Jardin d'enfants ou Crèche publique
Value=3;Ecole coranique
Value=4;ECOLE PRIMAIRE y compris le Préscolaire
Value=5;Autre (spécifier  )
Value=6;Non

[Item]
Label=Niveau d’instruction pendant l'année scolaire/universitaire précédente
Name=ED04A
Start=56
Len=1
DataType=Numeric

[ValueSet]
Label=Niveau d’instruction pendant l'année scolaire/universitaire précédente
Name=ED04A_VS1
Value=1;Sans instruction
Value=2;Alphabétise
Value=3;Primaire
Value=4;Moyen
Value=5;Secondaire
Value=6;Supérieur

[Item]
Label=Classe pendant l'année scolaire/universitaire précédente
Name=ED04B
Start=57
Len=1
DataType=Numeric

[ValueSet]
Label=Classe pendant l'année scolaire/universitaire précédente
Name=ED04B_VS1
Value=0:7;.

[Item]
Label=N’a pas fréquenté un établissement scolaire pendant l'année scolaire
Name=ED05
Start=58
Len=1
DataType=Numeric

[ValueSet]
Label=N’a pas fréquenté un établissement scolaire pendant l'année scolaire
Name=ED05_VS1
Value=1;Pas d'école dans la localité
Value=2;Aide des parents dans leurs activités
Value=3;Empêchement des parents
Value=4;Maladie /handicap
Value=5;Maladie /handicap d'un membre de la famille
Value=6;Problèmes financiers
Value=7;Pas d'intérêt pour les études
Value=8;Trop vieux
Value=9;Autre (spécifier  )

[Item]
Label=Fréquente toujours un établissement scolaire/universitaire
Name=ED06
Start=59
Len=1
DataType=Numeric

[ValueSet]
Label=Fréquente toujours un établissement scolaire/universitaire
Name=ED06_VS1
Value=1;Oui, en cours
Value=2;Non

[Item]
Label=Achevé ses études
Name=ED07
Start=60
Len=1
DataType=Numeric

[ValueSet]
Label=Achevé ses études
Name=ED07_VS1
Value=1;Oui, en cours
Value=2;Non

[Item]
Label=N’a pas pas achevé ses études
Name=ED08
Start=61
Len=2
DataType=Numeric

[ValueSet]
Label=N’a pas pas achevé ses études
Name=ED08_VS1
Value=1;Eloignement de l'école
Value=2;Exclu à cause des Redoublements
Value=3;Exclu pour indiscipline
Value=4;Aide des parents dans Leurs activités
Value=5;Empêchement des parents
Value=6;Maladie / handicap
Value=7;Maladie / handicap d'un Membre de la famille
Value=8;Problèmes financiers
Value=9;Pas d'intérêt pour les études
Value=99;Autre (spécifier)

[Item]
Label=Diplôme le plus élevé obtenu
Name=ED09
Start=63
Len=2
DataType=Numeric

[ValueSet]
Label=Diplôme le plus élevé obtenu
Name=ED09_VS1
Value=0;Aucun
Value=1;Cep (certificat d'études primaires)
Value=2;Bem ou bef (brevet d’enseignement moyen ou fondamental)
Value=3;Baccalauréat
Value=4;Technicien supérieur (diplôme d'enseignement supérieur) (cycle court)
Value=5;Deua (diplôme d’études universitaires appliquées)
Value=6;Licence
Value=7;Des (diplôme d’études supérieures)
Value=8;Ingéniorat
Value=9;Master
Value=10;Dea (diplôme d’études approfondies)
Value=11;Magistère
Value=12;Diplôme en médecine
Value=13;Doctorats et assimilés

[Item]
Label=A déjà doublé
Name=ED10
Start=65
Len=1
DataType=Numeric

[ValueSet]
Label=A déjà doublé
Name=ED10_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Fréquente ou a déjà fréquenté une école ou un établissement privé durant son cursus scolaire/universitaire
Name=ED11
Start=66
Len=1
DataType=Numeric

[ValueSet]
Label=Fréquente ou a déjà fréquenté une école ou un établissement privé durant son cursus scolaire/universitaire
Name=ED11_VS1
Value=1;Oui, pour toutes les années
Value=2;Oui, pourquelques années
Value=3;Non

[Item]
Label=fréquente ou a déjà fréquenté une école de formation professionnelle
Name=ED12
Start=67
Len=1
DataType=Numeric

[ValueSet]
Label=fréquente ou a déjà fréquenté une école de formation professionnelle
Name=ED12_VS1
Value=1;Oui, en cours
Value=2;Oui, a achevé
Value=3;Oui, a interrompu
Value=4;Non

[Item]
Label=N'a pas achevé cette formation
Name=ED13
Start=68
Len=2
DataType=Numeric

[ValueSet]
Label=N'a pas achevé cette formation
Name=ED13_VS1
Value=1;Eloignement de l'école
Value=2;Exclu à cause des redoublements
Value=3;Aide des parents dans leurs activités
Value=4;Empêchement des parents
Value=5;Maladie /handicap
Value=6;Maladie / handicap d'un membre de la famille
Value=7;Problèmes financiers
Value=8;Pas d'intérêt pour la formation
Value=9;A trouvé du travail
Value=99;Autre (spécifier  )

[Item]
Label=Type de la formation suivie
Name=ED14
Start=70
Len=1
DataType=Numeric

[ValueSet]
Label=Type de la formation suivie
Name=ED14_VS1
Value=1;Présentiel
Value=2;Formation par apprentissage
Value=3;Formation en cours du soir
Value=4;Formation à distance

[Item]
Label=Spécialité de la formation suivie
Name=ED15
Start=71
Len=3
DataType=Numeric

[ValueSet]
Label=Spécialité de la formation suivie
Name=ED15_VS1
Value=10;METIERS DE L'AGRICULTURE ET DE LA PECHE
Value=11;Paysagiste
Value=12;Jardinier aménagiste
Value=13;Apiculteur
Value=14;Viticulteur
Value=15;Technicien en santé animale
Value=16;Pêcheur
Value=17;Aquaculteur
Value=18;Ostréiculteur
Value=20;METIERS DE L'INDUSTRIE
Value=21;Soudeur
Value=22;Tôlier peintre en carrosserie
Value=23;Soudeur hydraulique
Value=24;Chaudronnerie tuyauterie
Value=25;Ferronnerie
Value=26;Menuiserie métallique-aluminium
Value=27;Tourneur fraiseur mécanicien
Value=28;Maintenance des machines agricoles
Value=29;Mécanique agricole
Value=30;Conducteur engins de chantier
Value=31;Mécanicien
Value=32;Agent en froid et climatisation
Value=33;Maintenance des équipements informatiques
Value=34;Maintenance des équipements audio-visuels
Value=35;Electricité
Value=36;Maintenance matériel biomédical
Value=37;Maintenance équipements froid et climatisation
Value=38;Electronique auto
Value=39;Electromécanicien
Value=40;Entretien et maintenance informatique
Value=41;Tailleur prêt-à-porter
Value=42;Styliste/modéliste
Value=43;Maintenance des équipements textiles
Value=44;Couture dames
Value=45;Piqueur polyvalent
Value=46;Tailleur
Value=47;Maintenance des équipements de confection
Value=48;Menuiserie/ébénisterie
Value=49;Menuiserie
Value=50;Ebénisterie
Value=60;METIERS DU BTPH
Value=61;Plomberie
Value=62;Maçonnerie
Value=63;Peinture
Value=64;Ferraillage
Value=65;Coffreur
Value=66;Carreleur
Value=67;Chauffagiste
Value=68;Vitrier
Value=69;Aide topographe
Value=70;Métreur-vérificateur
Value=71;Plâtrier
Value=72;Tailleur poseur de pierre et marbre
Value=73;Etanchéité
Value=74;Conducteur de travaux
Value=80;METIERS DE L'ARTISANAT
Value=81;Broderie
Value=81;Céramiste
Value=82;Sculpture sur bois
Value=83;Tissage traditionnel
Value=84;Souffleur sur verre
Value=85;Horlogerie
Value=86;Cordonnerie
Value=87;Montage réparation lunetterie
Value=88;Tissage de tapis
Value=89;Vannerie
Value=90;Bijouterie
Value=91;Sculpture sur plâtre
Value=92;Céramiques poteries
Value=93;Peinture sur tissus
Value=94;Bijouterie joaillerie orfèvrerie
Value=95;Tissage traditionnel et tapisserie d’art
Value=96;Coiffure option homme
Value=97;Coiffure option dame
Value=98;Esthétique
Value=100;METIERS DES SERVICES
Value=101;Guide touristique
Value=102;Hôtellerie
Value=103;Informatique et bureautique
Value=104;Exploitant informatique
Value=105;Traitement cartographique
Value=106;Magasinier
Value=107;Agent de transit et dédouanement
Value=108;Aide comptable
Value=109;Commerce international
Value=110;Agent de banque

[Item]
Label=Durée de la formatiion
Name=ED16
Start=74
Len=3
DataType=Numeric

[Item]
Label=Suit ou a suivi des cours d’apprentissage d’un métier ou des cours spéciaux
Name=ED17
Start=77
Len=1
DataType=Numeric

[ValueSet]
Label=Suit ou a suivi des cours d’apprentissage d’un métier ou des cours spéciaux
Name=ED17_VS1
Value=1;Oui, dans une entreprise
Value=2;Oui, chez un particulier
Value=3;Oui, dans une association
Value=4;Oui, cours d'alphabétisation
Value=5;Oui, cours de soutien
Value=6;Oui, en ligne
Value=7;Non, aucun

[Item]
Label=A suivi des études durant l'année scolaire/universitaire précédente
Name=ED18
Start=78
Len=1
DataType=Numeric

[ValueSet]
Label=A suivi des études durant l'année scolaire/universitaire précédente
Name=ED18_VS1
Value=1;Oui, établissement scolaire/universitaire
Value=2;Oui, formation professionnelle
Value=3;Oui, cours d'apprentissage d'un metier ou cours speciaux
Value=4;Non

[Item]
Label=Niveau d’instruction pendant l'année scolaire/universitaire précédente
Name=ED19A
Start=79
Len=1
DataType=Numeric

[ValueSet]
Label=Niveau d’instruction pendant l'année scolaire/universitaire précédente
Name=ED19A_VS1
Value=1;Préscolaire
Value=2;Primaire
Value=3;Moyen
Value=4;Professionnel 1
Value=5;Professionnel 2
Value=6;Secondaire
Value=9;Autre (spécifier  )

[Item]
Label=Classe pendant l'année scolaire/universitaire précédente
Name=ED19B
Start=80
Len=1
DataType=Numeric

[ValueSet]
Label=Classe pendant l'année scolaire/universitaire précédente
Name=ED19B_VS1
Value=0:7;.

[Item]
Label=Type d'établissement a été [NOM] pendant l'année scolaire/universitaire précédente
Name=ED20
Start=81
Len=1
DataType=Numeric

[ValueSet]
Label=Type d'établissement a été [NOM] pendant l'année scolaire/universitaire précédente
Name=ED20_VS1
Value=1;Public
Value=2;Privé
Value=9;Autre (spécifier)

[Item]
Label=Distance en km se trouve l'établissement ou le lieu d'apprentissage par rapport à votre résidence
Name=ED21
Start=82
Len=2
DataType=Numeric

[Item]
Label=Montant des frais d'inscription, assurances cotisations, associations des parents d'élèves
Name=ED22
Start=84
Len=12
DataType=Numeric

[Item]
Label=A reçu un soutien matériel ou de l’argent pour acheter des livres scolaires /cahier…etc (ou reçu d'un tiers) pendant l'année scolaire/universitaire précédente
Name=ED23
Start=96
Len=1
DataType=Numeric

[ValueSet]
Label=A reçu un soutien matériel ou de l’argent pour acheter des livres scolaires /cahier…etc (ou reçu d'un tiers) pendant l'année scolaire/universitaire précédente
Name=ED23_VS1
Value=1;Oui, soutien total de l'etat
Value=2;Oui, soutien total d'un particulier
Value=3;Oui, soutien total de l'etat et d'un particulier
Value=4;Oui, soutien partiel de l'etat avec un apport personnel
Value=5;Oui, soutien partiel d'un particulier avec un apport personnel
Value=6;Non

[Item]
Label=Montant de ce soutien pendant l'année scolaire/universitaire précédente
Name=ED24
Start=97
Len=12
DataType=Numeric

[Item]
Label=Montant des frais de fournitures pendant l'année scolaire/universitaire  précédente
Name=ED25
Start=109
Len=12
DataType=Numeric

[Item]
Label=A reçu un soutien matériel ou de l’argent pour l'achat d'uniformes scolaires (tabliers) pendant l'année scolaire/universitaire précédente
Name=ED26
Start=121
Len=1
DataType=Numeric

[ValueSet]
Label=A reçu un soutien matériel ou de l’argent pour l'achat d'uniformes scolaires (tabliers) pendant l'année scolaire/universitaire précédente
Name=ED26_VS1
Value=1;Oui, soutien total de l'Etat
Value=2;Oui, soutien total d'un particulier
Value=3;Oui, soutien total de l'Etat et d'un particulier
Value=4;Oui, soutien partiel de l'Etat avec un apport personnel
Value=5;Oui, soutien partiel d'un particulier avec un apport Personnel
Value=6;Non

[Item]
Label=Montant de ce soutien pendant l'année scolaire/universitaire précédente
Name=ED27
Start=122
Len=12
DataType=Numeric

[Item]
Label=Montant des frais d'uniformes d'étude pendant l'année scolaire/universitaire précédente
Name=ED28
Start=134
Len=12
DataType=Numeric

[Item]
Label=A bénéficié d'un soutien pour la cantine scolaire ou universitaire pendant l'année scolaire/universitaire précédente
Name=ED29
Start=146
Len=1
DataType=Numeric

[ValueSet]
Label=A bénéficié d'un soutien pour la cantine scolaire ou universitaire pendant l'année scolaire/universitaire précédente
Name=ED29_VS1
Value=1;Oui, soutien total de l'Etat
Value=2;Oui, soutien total d'un particulier
Value=3;Oui, soutien total de l'Etat et d'un particulier
Value=4;Oui, soutien partiel de l'Etat avec un apport personnel
Value=5;Oui, soutien partiel d'un particulier avec un apport Personnel
Value=6;Non

[Item]
Label=Estimez le montant de ce soutien pendant l'année scolaire/ universitaire précédente ETAT
Name=ED30A
Start=147
Len=12
DataType=Numeric

[Item]
Label=Estimez le montant de ce soutien pendant l'année scolaire/ universitaire précédente PARTICULIER
Name=ED30B
Start=159
Len=12
DataType=Numeric

[Item]
Label=Montant des frais de cantine scolaire ou universitaire de [NOM] pendant l'année scolaire/universitaire précédente
Name=ED31
Start=171
Len=12
DataType=Numeric

[Item]
Label=A bénéficié d'un soutien pour le transport scolaire ou universitaire pendant l'année scolaire/universitaire précédente
Name=ED32
Start=183
Len=1
DataType=Numeric

[ValueSet]
Label=A bénéficié d'un soutien pour le transport scolaire ou universitaire pendant l'année scolaire/universitaire précédente
Name=ED32_VS1
Value=1;Oui, soutien total de l'état
Value=2;Oui, soutien total d'un particulier
Value=3;Oui, soutien total de l'état et d'un particulier
Value=4;Oui, soutien partiel de l'état avec un apport personnel
Value=5;Oui, soutien partiel d'un particulier avec un apport Personnel
Value=6;Non

[Item]
Label=Estimez le montant de ce soutien pendant l'année scolaire/universitaire précédente ETAT
Name=ED33A
Start=184
Len=12
DataType=Numeric

[Item]
Label=Estimez le montant de ce soutien pendant l'année scolaire/universitaire précédente PARTICULIER
Name=ED33B
Start=196
Len=12
DataType=Numeric

[Item]
Label=Montant des frais de transport scolaire ou universitaire de [NOM] pendant l'année scolaire/ universitaire précédente
Name=ED34
Start=208
Len=12
DataType=Numeric

[Item]
Label=Montant des autres dépenses d'étude (cours de soutien , etc.) pendant l'année scolaire/universitaire précédente
Name=ED35
Start=220
Len=12
DataType=Numeric

[Item]
Label=A bénéficié d'un soutien aux frais d’étude (prime de scolarité, bourse d’étude) pendant l'année scolaire/universitaire précédente
Name=ED36
Start=232
Len=1
DataType=Numeric

[ValueSet]
Label=A bénéficié d'un soutien aux frais d’étude (prime de scolarité, bourse d’étude) pendant l'année scolaire/universitaire précédente
Name=ED36_VS1
Value=1;Oui, une allocation de scolarite (3000 da)
Value=2;Oui, une bourse étudiant
Value=3;Oui, une prime de solidarité scolaire (5000 da)
Value=4;Oui, une bourse professionnelle
Value=5;Autre soutien (ex : particulier ou
Value=6;Non

[Item]
Label=Montant total des bourses / allocations que [NOM] a reçu pendant l'année scolaire/universitaire précédente
Name=ED37
Start=233
Len=12
DataType=Numeric

[Record]
Label=Santé générale
Name=QHSEC06
RecordTypeValue='8'
Required=No
MaxRecords=90
RecordLen=257

[Item]
Label=Numero ligne du membre sante
Name=SA00
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero ligne du membre sante
Name=SA00_VS1
Value=1:50

[Item]
Label=Nom du membre ménage
Name=QHNOO
Start=13
Len=40
DataType=Alpha

[Item]
Label=souffre d'une maladie chronique
Name=SA01
Start=53
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=souffre d'une maladie chronique
Name=SA01_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=maladie chronique
Name=SA02
Start=54
Len=11
DataType=Alpha

[ValueSet]
Label=maladie chronique
Name=SA02_VS1
Value='A          ';Diabète
Value='B          ';Hypertension artérielle
Value='C          ';Cancer
Value='D          ';Maladie cardiovasculaire
Value='E          ';Maladie neurologique
Value='F          ';Maladie respiratoire
Value='G          ';Insuffisance rénale
Value='H          ';Maladie articulaire
Value='I          ';Troubles neuropsychiatriques
Value='J          ';Thyroide
Value='X          ';Autre (spécifier)

[Item]
Label=Suit-il (elle) un traitement
Name=SA03
Start=65
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Suit-il (elle) un traitement
Name=SA03_VS1
Value=1;Oui, de manière regulière
Value=2;Oui, parfois
Value=3;Non

[Item]
Label=Pas de traitement, ou seulement de manière occasionnelle
Name=SA04
Start=66
Len=1
DataType=Numeric

[ValueSet]
Label=Pas de traitement, ou seulement de manière occasionnelle
Name=SA04_VS1
Value=1;Problème financier
Value=2;Pénurie de mdicaments
Value=3;Manque d'accès aux soins
Value=4;Amélioration perçue
Value=5;Préférence pour les traitements alternatif
Value=9;Autre (spécifier  )

[Item]
Label=A été contraint (e) d’arrêter ses activités physiques et /ou professionnelles régulières
Name=SA05
Start=67
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=A été contraint (e) d’arrêter ses activités physiques et /ou professionnelles régulières
Name=SA05_VS1
Value=1;Oui, activite physique
Value=2;Oui, activite professionnelle
Value=3;Oui, les deux
Value=4;Non

[Item]
Label=Nombre de jours au cours des 30 derniers jours contraint(e) d’arrêter ses activités physiques et /ou
Name=SA06
Start=68
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Un problème de santé (exemple: toux, diarrhée rhume, mal de dos, mal de dents, fièvre, blessure,
Name=SA07
Start=71
Len=1
DataType=Numeric

[ValueSet]
Label=Un problème de santé (exemple: toux, diarrhée rhume, mal de dos, mal de dents, fièvre, blessure,
Name=SA07_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=principaux problème(s) de santé que eu
Name=SA08
Start=72
Len=12
DataType=Alpha

[ValueSet]
Label=principaux problème(s) de santé que eu
Name=SA08_VS1
Value='A           ';Problème d'estomac
Value='B           ';Rhume/toux
Value='C           ';Mal de dos
Value='D           ';Diarrhée/mal de ventre
Value='E           ';Mal de tête
Value='F           ';Mal de dent
Value='G           ';Mal d'oreille
Value='H           ';Problème de peau
Value='I           ';Rhumatisme
Value='J           ';Douleur musculaire / articulaire
Value='K           ';Accident/blessure
Value='X           ';Autre (spécifier  )

[Item]
Label=A consulté dans un service de santé
Name=SA09
Start=84
Len=1
DataType=Numeric

[ValueSet]
Label=A consulté dans un service de santé
Name=SA09_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Qui a consulté dans les 30 derniers jours
Name=SA10
Start=85
Len=6
DataType=Alpha

[ValueSet]
Label=Qui a consulté dans les 30 derniers jours
Name=SA10_VS1
Value='A     ';Médecin Spécialiste
Value='B     ';Médecin Généraliste
Value='C     ';Pharmacien
Value='D     ';Infirmier(ère)
Value='E     ';Guérisseur, cheikh, raki, kabla traditionnelle
Value='X     ';Autre personnel de santé à spécifier

[Item]
Label=Moyen de transport pour aller à  la consultation
Name=SA11
Start=91
Len=1
DataType=Numeric

[ValueSet]
Label=Moyen de transport pour aller à  la consultation
Name=SA11_VS1
Value=1;Â pied
Value=2;Véhicule personnel
Value=3;Taxi
Value=4;Transport en commun
Value=5;Ambulance
Value=6;Animal
Value=7;A domicile
Value=9;Autre (spécifier)

[Item]
Label=A consulté dans un service de santé (y compris pharmacie), ou un guérisseur traditionnel
Name=SA12
Start=92
Len=1
DataType=Numeric

[ValueSet]
Label=A consulté dans un service de santé (y compris pharmacie), ou un guérisseur traditionnel
Name=SA12_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Montant dépensé en totalité pour es frais de consultation d'un médecin
Name=SA13
Start=93
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=A prescrit ou recommandé des médicaments dans les 30 derniers jours
Name=SA14
Start=105
Len=1
DataType=Numeric

[ValueSet]
Label=A prescrit ou recommandé des médicaments dans les 30 derniers jours
Name=SA14_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=A acheté ou acquis tous les médicaments prescrits ou recommandé
Name=SA15
Start=106
Len=1
DataType=Numeric

[ValueSet]
Label=A acheté ou acquis tous les médicaments prescrits ou recommandé
Name=SA15_VS1
Value=1;Oui, en totalité
Value=2;Oui, en partie
Value=3;Non

[Item]
Label=N'a pas acheté ou acquis la totalité des médicaments ou juste une partie
Name=SA16
Start=107
Len=1
DataType=Numeric

[ValueSet]
Label=N'a pas acheté ou acquis la totalité des médicaments ou juste une partie
Name=SA16_VS1
Value=1;Trop chers
Value=2;Non disponibles
Value=3;Procurés autrement
Value=9;Autre (spécifier  )

[Item]
Label=Montant des frais de médicaments acheté ou acquis par [NOM] au cours des 30 derniers jours
Name=SA17
Start=108
Len=12
DataType=Numeric

[Item]
Label=A acheté ou acquis des médicaments pour lui- même (elle-même) sans ordonnance
Name=SA18
Start=120
Len=1
DataType=Numeric

[ValueSet]
Label=A acheté ou acquis des médicaments pour lui- même (elle-même) sans ordonnance
Name=SA18_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Montant dépensé en totalité pour ces médicaments non prescrits achetés dans les 30 derniers jours
Name=SA19
Start=121
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Est couvert par la sécurité sociale
Name=SA20
Start=133
Len=1
DataType=Numeric

[ValueSet]
Label=Est couvert par la sécurité sociale
Name=SA20_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Eu des difficultés à voir, même en portant des lunettes
Name=SA21
Start=134
Len=1
DataType=Numeric

[ValueSet]
Label=Eu des difficultés à voir, même en portant des lunettes
Name=SA21_VS1
Value=1;Non pas de difficultés
Value=2;Oui, quelques difficultés
Value=3;Oui, beaucoup de difficultés
Value=4;Ne peut pas du tout voir

[Item]
Label=Eu difficultés à entendre, même en utilisant un appareil auditif
Name=SA22
Start=135
Len=1
DataType=Numeric

[ValueSet]
Label=Eu difficultés à entendre, même en utilisant un appareil auditif
Name=SA22_VS1
Value=1;Non pas de difficultés
Value=2;Oui, quelques difficultés
Value=3;Oui, beaucoup de difficultés
Value=4;Ne peut pas du tout entendre

[Item]
Label=Eu difficultés à marcher ou à monter des escaliers
Name=SA23
Start=136
Len=1
DataType=Numeric

[ValueSet]
Label=Eu difficultés à marcher ou à monter des escaliers
Name=SA23_VS1
Value=1;Non pas de difficultés
Value=2;Oui, quelques difficultés
Value=3;Oui, beaucoup de difficultés
Value=4;Ne peut pas du tout marcher

[Item]
Label=Eu difficultés à se rappeler ou à se concentrer
Name=SA24
Start=137
Len=1
DataType=Numeric

[ValueSet]
Label=Eu difficultés à se rappeler ou à se concentrer
Name=SA24_VS1
Value=1;Non pas de difficultés
Value=2;Oui, quelques difficultés
Value=3;Oui, beaucoup de difficultés
Value=4;Ne peut pas du tout se rappeler

[Item]
Label=Eu difficultés à se laver ou à s'habiller (pour ses soins personnels)
Name=SA25
Start=138
Len=1
DataType=Numeric

[ValueSet]
Label=Eu difficultés à se laver ou à s'habiller (pour ses soins personnels)
Name=SA25_VS1
Value=1;Non pas de difficultés
Value=2;Oui, quelques difficultés
Value=3;Oui, beaucoup de difficultés
Value=4;Ne peut pas du tout prendre soin de lui (elle)

[Item]
Label=Eu difficultés à communiquer, par exemple pour comprendre ou être compris par sa langue habituelle
Name=SA26
Start=139
Len=1
DataType=Numeric

[ValueSet]
Label=Eu difficultés à communiquer, par exemple pour comprendre ou être compris par sa langue habituelle
Name=SA26_VS1
Value=1;Non pas de difficultés
Value=2;Oui, quelques difficultés
Value=3;Oui, beaucoup de difficultés
Value=4;Ne peut pas du tout être compris

[Item]
Label=Pendant les 12 derniers mois, [NOM] a été hospitalisé(e)
Name=SA27
Start=140
Len=1
DataType=Numeric

[ValueSet]
Label=Pendant les 12 derniers mois, [NOM] a été hospitalisé(e)
Name=SA27_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Montant des frais d'hospitalisation que [NOM] a  dépensé au cours des 12 derniers mois
Name=SA28
Start=141
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=A reçu une aide en espèces ou en nature pour le transport liée à sa santé
Name=SA29
Start=153
Len=1
DataType=Numeric

[ValueSet]
Label=A reçu une aide en espèces ou en nature pour le transport liée à sa santé
Name=SA29_VS1
Value=1;Oui, aide totale de l'Etat
Value=2;Oui, aide totale d'un particulier
Value=3;Oui, aide totale de l'Etat et d'un particulier
Value=4;Oui, aide partielle de l'Etat avec un apport personnel
Value=5;Oui, aide partielle d'un particulier avec un apport personnel
Value=6;Non

[Item]
Label=Montant de de cette aide au cours des 12 derniers mois
Name=SA30
Start=154
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de ces dépenses de transport au cours des 12 derniers mois
Name=SA31
Start=166
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=a reçu une aide en espèces ou en nature pour des appareils médicaux thérapeutiques
Name=SA32
Start=178
Len=1
DataType=Numeric

[ValueSet]
Label=a reçu une aide en espèces ou en nature pour des appareils médicaux thérapeutiques
Name=SA32_VS1
Value=1;Oui, aide totale de l'Etat
Value=2;Oui, aide totale d'un particulier
Value=3;Oui, aide totale de l'Etat et d'un particulier
Value=4;Oui, aide partielle de l'Etat avec un apport personnel
Value=5;Oui, aide partielle d'un particulier avec un apport personnel
Value=6;Non

[Item]
Label=Montant de de cette aide au cours des 12 derniers mois
Name=SA33A
Start=179
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de de cette aide au cours des 12 derniers mois
Name=SA33B
Start=191
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant des dépenses pour ces appareils médicaux thérapeutiques au cours des 12 derniers mois
Name=SA34
Start=203
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=A reçu une aide en espèces ou en nature pour des verres correcteurs
Name=SA35
Start=215
Len=1
DataType=Numeric

[ValueSet]
Label=A reçu une aide en espèces ou en nature pour des verres correcteurs
Name=SA35_VS1
Value=1;Oui, aide totale de l'Etat
Value=2;Oui, aide totale d'un particulier
Value=3;Oui, aide totale de l'Etat et d'un particulier
Value=4;Oui, aide partielle de l'Etat avec un apport personnel
Value=5;Oui, aide partielle d'un particulier avec un apport personnel
Value=6;Non

[Item]
Label=Montant de de cette aide au cours des 12 derniers mois
Name=SA36
Start=216
Len=12
DataType=Numeric

[Item]
Label=Montant des frais pour des verres correcteurs, monture de lunettes au cours des 12 derniers mois
Name=SA37
Start=228
Len=12
DataType=Numeric

[Item]
Label=Au cours des 12 derniers mois, [NOM] a accouché ou est-elle actuellement enceinte
Name=SA38
Start=240
Len=5
DataType=Alpha

[ValueSet]
Label=Au cours des 12 derniers mois, [NOM] a accouché ou est-elle actuellement enceinte
Name=SA38_VS1
Value='A    ';Accouchement normal
Value='B    ';Mort né
Value='C    ';Fausse couche
Value='D    ';Actuellement enceinte
Value='Y    ';Non

[Item]
Label=Montant global des dépenses liées à l'accouchement ou à la grossesse au cours des 12 derniers mois
Name=SA39
Start=245
Len=12
DataType=Numeric

[Item]
Label=A un enfant de moins de 5 ans décédé au cours des 12 derniers mois
Name=SA40
Start=257
Len=1
DataType=Numeric

[ValueSet]
Label=A un enfant de moins de 5 ans décédé au cours des 12 derniers mois
Name=SA40_VS1
Value=1;Oui
Value=2;Non

[Record]
Label=Vacances et déplacement
Name=QHSEC07
RecordTypeValue='9'
Required=No
RecordLen=14

[Item]
Label=Au cours des 12 derniers mois, un ou plusieurs membres de votre ménage est ou sont partis
Name=VD01
Start=11
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 12 derniers mois, un ou plusieurs membres de votre ménage est ou sont partis
Name=VD01_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Nombre de séjours
Name=VD02
Start=12
Len=3
DataType=Numeric

[Record]
Label=Vacances et déplacement suite
Name=QHSEC7A
RecordTypeValue='A'
Required=No
MaxRecords=90
RecordLen=28

[Item]
Label=Numero du sejour
Name=VD00
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero du sejour
Name=VD00_VS1
Value=1:50

[Item]
Label=Ce séjour a  été fait par une seule  personne du ménage
Name=VD03
Start=13
Len=1
DataType=Numeric

[ValueSet]
Label=Ce séjour a  été fait par une seule  personne du ménage
Name=VD03_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Mettre le code d'identification de ce membre
Name=VD04
Start=14
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de personnes du ménage ayant effectué le séjour
Name=VD05
Start=16
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de personnes du ménage ayant effectué le séjour
Name=VD05_VS1
Value=0:8;,

[Item]
Label=Durée du séjour
Name=VD06
Start=18
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Principal lieu d’hébergement
Name=VD07
Start=21
Len=1
DataType=Numeric

[ValueSet]
Label=Principal lieu d’hébergement
Name=VD07_VS1
Value=1;Hotel
Value=2;Camping
Value=3;Logement secondaire
Value=4;Chez les parents
Value=5;Auberge de jeunes
Value=6;Location de maisons Bungalow
Value=7;Centre de vacances
Value=8;Amis
Value=9;Autre à specifier

[Item]
Label=Lieu de ce séjour
Name=VD08
Start=22
Len=1
DataType=Numeric

[ValueSet]
Label=Lieu de ce séjour
Name=VD08_VS1
Value=1;À l'interieur du  Pays
Value=2;À l'etranger

[Item]
Label=Code WILAYA de séjour
Name=VD09
Start=23
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code WILAYA de séjour
Name=VD09_VS1
Value=1;ADRAR
Value=2;CHLEF
Value=3;LAGHOUAT

[Item]
Label=Code PAYS de séjour
Name=VD10
Start=25
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Moyen principal de transport utilisé pour vous rendre sur le lieu de séjour
Name=VD11
Start=27
Len=1
DataType=Numeric

[ValueSet]
Label=Moyen principal de transport utilisé pour vous rendre sur le lieu de séjour
Name=VD11_VS1
Value=1;Voiture personnelle
Value=2;Car
Value=3;Taxi
Value=4;Train
Value=5;Avion
Value=6;Bateau
Value=9;Autre à specifier

[Item]
Label=Motif de séjour
Name=VD12
Start=28
Len=1
DataType=Numeric

[ValueSet]
Label=Motif de séjour
Name=VD12_VS1
Value=1;Vacances, repos, detente
Value=2;Visite a la famille
Value=3;Cure
Value=4;Pelerinage
Value=5;Omra
Value=6;Regler des affaires Personnelles
Value=7;Colonie de vacances
Value=8;Conge de convalescence
Value=9;Autre à specifier

[Record]
Label=Situation actuelle de l'activité économique
Name=QHSEC08A
RecordTypeValue='B'
Required=No
MaxRecords=90
RecordLen=64

[Item]
Label=Numero ligne du membre
Name=ACTA0
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero ligne du membre
Name=ACTA0_VS1
Value=1:50

[Item]
Label=Nom du membre du ménage
Name=QHNOP
Start=13
Len=40
DataType=Alpha

[Item]
Label=A répondu pour lui/elle-même
Name=ACTA1
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=A répondu pour lui/elle-même
Name=ACTA1_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Personne repondant pour nom
Name=ACTA2
Start=54
Len=2
DataType=Numeric

[Item]
Label=Situation individuelle
Name=ACTA3
Start=56
Len=1
DataType=Numeric

[ValueSet]
Label=Situation individuelle
Name=ACTA3_VS1
Value=1;Occupé
Value=2;Service national
Value=3;Chômeur
Value=4;Femme au foyer
Value=5;Enfant âgé de moins de 10 ans
Value=6;Écolier / étudiant âgé de 10 ans ou plus (y compris stagiaire)
Value=7;Retraité
Value=8;Pensionné
Value=9;Autre (spécifier)

[Item]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué Travaux agricoles, elevages
Name=ACTA4A
Start=57
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué Travaux agricoles, elevages
Name=ACTA4A_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué commerce de tout genre
Name=ACTA4B
Start=58
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué commerce de tout genre
Name=ACTA4B_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué prepparation de produits alimentaires
Name=ACTA4C
Start=59
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué prepparation de produits alimentaires
Name=ACTA4C_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué activité de tissage
Name=ACTA4D
Start=60
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué activité de tissage
Name=ACTA4D_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué travaux de construction
Name=ACTA4E
Start=61
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué travaux de construction
Name=ACTA4E_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué services
Name=ACTA4F
Start=62
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué services
Name=ACTA4F_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué transport
Name=ACTA4G
Start=63
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué transport
Name=ACTA4G_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué Autre
Name=ACTA4H
Start=64
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 7 derniers jours , est ce que [nom] a effectué Autre
Name=ACTA4H_VS1
Value=1;Oui
Value=2;Non

[Record]
Label=Caractéristiques de l'emploi principal
Name=QHSEC08B
RecordTypeValue='C'
Required=No
MaxRecords=90
RecordLen=235

[Item]
Label=Numero ligne du membre
Name=ACTB0
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero ligne du membre
Name=ACTB0_VS1
Value=1:50

[Item]
Label=Nom du membre du ménage
Name=QHNOQ
Start=13
Len=20
DataType=Alpha

[Item]
Label=Libellé occupation principale ISCO
Name=ACTB1L
Start=33
Len=40
DataType=Alpha

[Item]
Label=Code Occupation principale ISCO
Name=ACTB1
Start=73
Len=3
DataType=Numeric

[ValueSet]
Label=Code Occupation principale ISCO
Name=ACTB1_VS1
Value=0;ATTENTE
Value=111;Membres des corps législatifs et cadres supérieurs de ladministration publique
Value=112;Directeurs généraux dentreprise
Value=121;Directeurs de services administratifs
Value=122;Directeurs et cadres de direction, ventes commercialisation et développement
Value=131;Directeurs et cadres de direction, agriculture, sylviculture et pêche
Value=132;Directeurs et cadres de direction, industries, manufacturières, mines, bâtiment et distribution
Value=133;Directeurs et cadres de direction, technologies de linformatique et des communications
Value=134;Cadres de direction, services spécialisés
Value=141;Directeurs et gérants, hôtellerie et restauration
Value=142;Directeurs et gérants, commerce de détail et de gros
Value=143;Autres directeurs et gérants
Value=211;Physiciens, chimistes assimilés
Value=212;Mathématiciens, actuaires et statisticiens
Value=213;Spécialistes des sciences de la vie
Value=214;Spécialistes, sciences techniques (sauf électrotechniques)
Value=215;Ingénieurs de lélectrotechnique
Value=216;Architectes, urbanistes, géomètres et concepteurs
Value=221;Médecins
Value=222;Cadres infirmiers et sage femme
Value=223;Spécialistes des médecines traditionnelles et des médecines complémentaires
Value=224;Praticiens  paramédicaux
Value=225;Vétérinaires
Value=226;Autres spécialistes des professions de la santé
Value=231;Professeurs duniversité et détablissement denseignement supérieur
Value=232;Professeurs, enseignement technique et professionnel
Value=233;Professeurs, enseignement secondaire
Value=234;Instituteurs, enseignement primaire et éducateurs de la petite enfance
Value=235;Autre spécialistes de lenseignement
Value=241;Spécialistes en finances
Value=242;Spécialistes des fonctions administratives
Value=243;Spécialistes des ventes, de la commercialisation et de relations publiques
Value=251;Concepteurs et analystes de logiciels et de multimédia
Value=252;Spécialistes des bases des données et des réseaux dordinateurs
Value=261;Juristes
Value=262;Archivistes, bibliothécaires, documentaliste et assimilés
Value=263;Spécialistes des sciences sociales et en théologie
Value=264;Auteurs, journalistes et linguistes
Value=265;Artiste créateurs et exécutants
Value=311;Techniciens des sciences physiques  et techniques
Value=312;Superviseurs, mines, industries manufacturières et bâtiment
Value=313; Techniciens, contrôle de processus industriels
Value=314;Techniciens et travailleurs assimilés des sciences de la vie
Value=315;Contrôleurs et techniciens des moyens de transport maritime et aérien
Value=321;Techniciens de la médecine et de la pharmacie
Value=322;Personnel infirmier et sage femme (niveau intermédiaire)
Value=323;Praticiens des médecines traditionnelles et des médecines complémentaires
Value=324;Techniciens et assistants vétérinaires
Value=325;Autres professions intermédiaires de la santé
Value=331;Professions intermédiaires de la finance et des mathématiques
Value=332;Agents de vente et dachat, courtiers
Value=333;Agent de services commerciaux
Value=334;Secrétaire dadministration et secrétaire spécialiste
Value=335;Professions intermédiaires de lapplication de la loi et assimilées
Value=341;Professions intermédiaires des services juridiques, des services sociaux et des religions
Value=342;Travailleurs du secteur des sports et des activités de remise en forme
Value=343;Professions intermédiaires de la culture, de la création artistique et des activités culinaires
Value=351;Techniciens, opérations et soutien aux utilisateurs des technologies de linformation et des communications
Value=352;Techniciens des télécommunications et de radio diffusion
Value=411;Employés de bureau, fonctions générales
Value=412;Secrétaires (Fonctions générales)
Value=413;Opérateurs sur clavier
Value=421;Guichetiers, encaisseurs et assimilés
Value=422;Employés chargés dinformer la clientèle
Value=431;Employés des services comptables et financiers
Value=432;Employés dapprovisionnement, dordonnancement et des transports
Value=441;Autres employés de type administratif
Value=511;Agent daccompagnement et assimilés (transports et tourisme)
Value=512;Cuisiniers
Value=513;Serveurs et barmen
Value=514;Coiffeurs, esthéticiens et assimilés
Value=515;Intendants, gouvernantes et concierges
Value=516;Autre personnel des services directs aux particuliers
Value=521;Vendeurs sur les marchés et vendeurs ambulants de comestibles
Value=522;Commerçants et vendeur, magasins
Value=523;Caissiers et billettistes
Value=524;Autre vendeurs
Value=531;Gardes denfants et aides-enseignants
Value=532;Aides-soignants
Value=541;Personnel des services de protection et de sécurité
Value=611;Agricultures et ouvriers qualifies, cultures commerciales
Value=612;Eleveurs et ouvriers qualifie de lélevage commercial et assimilé
Value=613;Agriculture et ouvrier qualifiés des culture et de lélevage à but commercial
Value=621;Professions de la sylviculture et assimilées
Value=622;Pécheurs, chasseurs et trappeurs
Value=631;Agriculture, subsistance
Value=632;Eleveurs de bétail, subsistance
Value=633;Agriculture et éleveurs subsistance
Value=634;Pêcheurs, chasseurs, trappeurs et cueilleurs, subsistance
Value=711;Métiers qualifiés du bâtiment (gros ouvre) et assimilés
Value=712;Métiers qualifiés du bâtiment (finitions) et assimilés
Value=713;Ouvriers peintres, ravaleurs de façades et assimilés
Value=721;Mouleurs de fonderie, soudeurs tôliers chaudronniers, monteurs et de charpentes métalliques et assimilés
Value=722;Forgerons, outilleurs et assimilés
Value=723;Mécaniciens et réparateurs de machines
Value=731;Métiers de lartisanat
Value=732;Métiers de limprimerie
Value=741;Installateurs et réparateurs, équipements électriques
Value=742;Monteurs et réparateurs, électronique et télécommunications
Value=751;Métiers qualifiés de l'alimentation et assimilés
Value=752;Métiers qualifiés du traitement du bois, ébénistes et assimilés
Value=753;Métiers qualifiés de l'habillement et assimilés
Value=754;Autres métiers qualifiés de l'industrie et de l'artisanat
Value=811;Conducteurs d'installations dexploitation minière et dextraction des minéraux
Value=812;Conducteurs d'installations de transformation et de traitement superficiel des métaux
Value=813;Conducteurs d'installations et des machines pour la fabrication des produits chimiques et photographiques
Value=814;Conducteurs machines pour la fabrication de produits en caoutchouc, en matières plastique et en papeterie
Value=815;Conducteurs de machines pour la fabrication de produits textiles et darticles en fourrure et en cuir
Value=816;Conducteurs de machines pour la fabrication de denrées alimentaires et de produits connexes
Value=817;Conducteurs d'installations pour la fabrication du papier et pour le travail de bois
Value=818;Autres conducteurs de machines et dinstallations fixes
Value=821;Ouvriers de lassemblage
Value=831;Conducteurs de locomotives et assimilés
Value=832;Conducteurs dautomobiles, de camionnettes et de motocycles
Value=833;Conducteurs de poids lourds et dautobus
Value=834;Conducteurs de matériels et engins mobiles
Value=835;Matelots de pont et assimilés
Value=911;Aide de manage et agents dentretien à domicile et dans les hôtels et bureaux
Value=912;Laveurs de véhicules et de vitres, laveurs de linge et autres nettoyeurs manuels
Value=921;Manuvres de lagriculture, de la pêche et de la sylviculture
Value=931;Manuvres des mines, du bâtiment et des travaux publics
Value=932;Manuvres des industries manufacturières
Value=933;Manuvres des transports et de lentreposage
Value=941;Assistants de fabrication de lalimentation
Value=951;Travailleurs des petits métiers des rues et assimilés
Value=952;Vendeurs ambulants (à lexception de lalimentation)
Value=961;Eboueurs
Value=962;Autres professions élémentaires
Value=997;Les appelés du contingent (service national)
Value=999;Non déclarés

[Item]
Label=Situation de nom dans la profession
Name=ACTB2
Start=76
Len=1
DataType=Numeric

[ValueSet]
Label=Situation de nom dans la profession
Name=ACTB2_VS1
Value=1;Employeur
Value=2;Indépendant/ artisan individuel
Value=3;Salarié
Value=4;Apprenti
Value=5;Aide familiale

[Item]
Label=Libellé occupation principale ISIC
Name=ACTB3M
Start=77
Len=40
DataType=Alpha

[Item]
Label=Code Occupation principale ISIC
Name=ACTB3
Start=117
Len=2
DataType=Numeric

[ValueSet]
Label=Code Occupation principale ISIC
Name=ACTB3_VS1
Value=0;EN ATTENTE
Value=1;Agriculture, chasse, services annexes
Value=2;Sylviculture, exploitation forestière, services annexes
Value=5;Pèche, aquaculture
Value=10;Extraction de houille, de lignite et de tourbe
Value=11;Extraction d'hydrocarbures; services annexes
Value=12;Extraction de minerais d'uranium
Value=13;Extraction de minerais métalliques
Value=14;Autres industries extractives
Value=15;Industries alimentaires
Value=16;Industrie du tabac
Value=17;Industrie textile
Value=18;Industrie de l'habillement et des fourrures
Value=19;Industrie du cuir et de la chaussure
Value=20;Travail du bois et fabrication d'articles en bois
Value=21;Industrie du papier et du carton
Value=22;Edition, imprimerie, reproduction
Value=23;Cokéfaction, raffinage, industries nucléaires
Value=24;Industrie chimique
Value=25;Industrie du caoutchouc et des plastiques
Value=26;Fabrication d'autres produits minéraux non métallique
Value=27;Métallurgie
Value=28;Travail des métaux
Value=29;Fabrication de machines et équipements
Value=30;Fabrication de machines de bureau et de matériel informatique
Value=31;Fabrication de machines et appareils électriques
Value=32;Fabrication d'équipements de radio, télévision et communication
Value=33;Fabrication d'instruments médicaux, de précision, d'optique, d'horlogerie
Value=34;Industrie automobile
Value=35;Fabrication d'autres matériels de transport
Value=36;Fabrication de meubles; industries diverses
Value=37;Récupération
Value=40;Production et distribution d'électricité, de gaz et de chaleur
Value=41;Captage, traitement et distribution d'eau
Value=45;Construction
Value=50;Commerce et reparation automobile
Value=51;Commerce de gros et intermédiaires du commerce
Value=52;Commerce de détails et réparation d'articles domestiques
Value=55;Hôtels et restaurants
Value=60;Transports terrestres
Value=61;Transports par eau
Value=62;Transports aériens
Value=63;Services auxiliaires des transports
Value=64;Postes et télécommunications
Value=65;Intermédiation financière
Value=66;Assurance
Value=67;Auxiliaires financiers et d'assurance
Value=70;Activités immobilières
Value=71;Location sans operateur
Value=72;Activités informatiques
Value=73;Recherche et développement
Value=74;Services fournis principalement aux entreprises
Value=75;Administration publique
Value=80;Education
Value=85;Sante et action sociale
Value=90;Assainissement, voirie et gestion des déchets
Value=91;Activités associatives
Value=92;Activités récréatives, culturelles et sportives
Value=93;Services personnels
Value=95;Services domestiques
Value=99;Activités extraterritoriales

[Item]
Label=Secteur nom a travaille principalement
Name=ACTB4
Start=119
Len=1
DataType=Numeric

[ValueSet]
Label=Secteur nom a travaille principalement
Name=ACTB4_VS1
Value=1;Public national
Value=2;Privé national
Value=3;Etranger
Value=4;Mixte

[Item]
Label=Emploi actuel rentre dans le cadre d'une aide etatique
Name=ACTB5
Start=120
Len=1
DataType=Numeric

[ValueSet]
Label=Emploi actuel rentre dans le cadre d'une aide etatique
Name=ACTB5_VS1
Value=1;TUP-HIMO
Value=2;DAIP (ANEM)
Value=3;DAIS (ADS)
Value=4;ANAD (ex ANSEJ)
Value=5;CNAC
Value=6;ANGEM
Value=7;Autre
Value=8;Non

[Item]
Label=Affilié à la securité sociale (CNAS, CASNOS) dans le cadre de cet emploi
Name=ACTB6
Start=121
Len=1
DataType=Numeric

[ValueSet]
Label=Affilié à la securité sociale (CNAS, CASNOS) dans le cadre de cet emploi
Name=ACTB6_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=A un contrat / accord ecrit ou verbal pour cet emploi
Name=ACTB7
Start=122
Len=1
DataType=Numeric

[ValueSet]
Label=A un contrat / accord ecrit ou verbal pour cet emploi
Name=ACTB7_VS1
Value=1;Oui, contrat
Value=2;Oui, accord
Value=3;Non

[Item]
Label=Nombre de mois [NOM] a occupé cet emploi
Name=ACTB8
Start=123
Len=1
DataType=Numeric

[Item]
Label=Nombe de jours par mois [NOM] a habituellement travaillé dans cet emploi
Name=ACTB9
Start=124
Len=1
DataType=Numeric

[Item]
Label=Nombre d'heures [NOM] travaille-t- il/elle habituellement par jour à cet emploi ?
Name=ACTB10
Start=125
Len=1
DataType=Numeric

[Item]
Label=Paiement
Name=ACTB11A
Start=126
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Unité deTemps
Name=ACTB11B
Start=138
Len=1
DataType=Numeric

[ValueSet]
Label=Unité deTemps
Name=ACTB11B_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=Recevoir d'autres paiements
Name=ACTB12
Start=139
Len=1
DataType=Numeric

[ValueSet]
Label=Recevoir d'autres paiements
Name=ACTB12_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=La valeur de ces paiements en espèces au cours des 12 derniers mois
Name=ACTB13A
Start=140
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre d'unité de temps
Name=ACTB13B
Start=152
Len=2
DataType=Numeric

[Item]
Label=Unité de temps
Name=ACTB13C
Start=154
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de temps
Name=ACTB13C_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=Dans le cadre de son emploi, beneficie LOGEMENT au cours des 12 derniers mois
Name=ACTB14A
Start=155
Len=1
DataType=Numeric

[ValueSet]
Label=Dans le cadre de son emploi, beneficie LOGEMENT au cours des 12 derniers mois
Name=ACTB14A_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Monrtant Logement
Name=ACTB14AA
Start=156
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre d'unité de temps
Name=ACTB14AB
Start=168
Len=2
DataType=Numeric

[Item]
Label=Unité de temps
Name=ACTB14AC
Start=170
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de temps
Name=ACTB14AC_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=Dans le cadre de son emploi, beneficie TRANSPORT au cours des 12 derniers mois
Name=ACTB14B
Start=171
Len=1
DataType=Numeric

[ValueSet]
Label=Dans le cadre de son emploi, beneficie TRANSPORT au cours des 12 derniers mois
Name=ACTB14B_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Montant Transport
Name=ACTB14BA
Start=172
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre d'unité de temps
Name=ACTB14BB
Start=184
Len=2
DataType=Numeric

[Item]
Label=Unité de temps
Name=ACTB14BC
Start=186
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de temps
Name=ACTB14BC_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=Dans le cadre de son emploi, beneficie RECOLTES DES CULTURES OU DES PLANTATIONS au cours des 12 derniers mois
Name=ACTB14C
Start=187
Len=1
DataType=Numeric

[ValueSet]
Label=Dans le cadre de son emploi, beneficie RECOLTES DES CULTURES OU DES PLANTATIONS au cours des 12 derniers mois
Name=ACTB14C_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Montant  Cultures et plantations
Name=ACTB14CA
Start=188
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre d'unité de temps
Name=ACTB14CB
Start=200
Len=2
DataType=Numeric

[Item]
Label=Unité de temps
Name=ACTB14CC
Start=202
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de temps
Name=ACTB14CC_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=Dans le cadre de son emploi, beneficie NOURRITURE au cours des 12 derniers mois
Name=ACTB14D
Start=203
Len=1
DataType=Numeric

[ValueSet]
Label=Dans le cadre de son emploi, beneficie NOURRITURE au cours des 12 derniers mois
Name=ACTB14D_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Montant  Nourritures
Name=ACTB14DA
Start=204
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre d'unité de temps
Name=ACTB14DB
Start=216
Len=2
DataType=Numeric

[Item]
Label=Unité de temps
Name=ACTB14DC
Start=218
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de temps
Name=ACTB14DC_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=Dans le cadre de son emploi, beneficie AUTRES AVANTAGES au cours des 12 derniers mois
Name=ACTB14E
Start=219
Len=1
DataType=Numeric

[ValueSet]
Label=Dans le cadre de son emploi, beneficie AUTRES AVANTAGES au cours des 12 derniers mois
Name=ACTB14E_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Montant  Autres avantages
Name=ACTB14EA
Start=220
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre d'unité de temps
Name=ACTB14EB
Start=232
Len=2
DataType=Numeric

[Item]
Label=Unité de temps
Name=ACTB14EC
Start=234
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de temps
Name=ACTB14EC_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=Le travail que [NOM] a fait était permanent ou temporaire
Name=ACTB15
Start=235
Len=1
DataType=Numeric

[ValueSet]
Label=Le travail que [NOM] a fait était permanent ou temporaire
Name=ACTB15_VS1
Value=1;Permanent à temps plein
Value=2;Permanent à  temps partiel
Value=3;Temporaire à plein temps
Value=4;Temporaire à temps partiel

[Record]
Label=Caractéristiques de l'emploi secondaire
Name=QHSEC08C
RecordTypeValue='D'
Required=No
MaxRecords=90
RecordLen=158

[Item]
Label=Numero ligne du membre
Name=ACTC_0
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero ligne du membre
Name=ACTC_0_VS1
Value=1:50

[Item]
Label=Household member name
Name=QHNOR
Start=13
Len=20
DataType=Alpha

[Item]
Label=Au cours des 7 derniers jours, [NOM] a exercé une activité secondaire
Name=ACTC1
Start=33
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 7 derniers jours, [NOM] a exercé une activité secondaire
Name=ACTC1_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Libellé occupation principale ISCO
Name=ACTC1N
Start=34
Len=40
DataType=Alpha

[Item]
Label=Code Occupation principale ISCO
Name=ACTC2
Start=74
Len=3
DataType=Numeric

[ValueSet]
Label=Code Occupation principale ISCO
Name=ACTC2_VS1
Value=0;ATTENTE
Value=111;Membres des corps législatifs et cadres supérieurs de ladministration publique
Value=112;Directeurs généraux dentreprise
Value=121;Directeurs de services administratifs
Value=122;Directeurs et cadres de direction, ventes commercialisation et développement
Value=131;Directeurs et cadres de direction, agriculture, sylviculture et pêche
Value=132;Directeurs et cadres de direction, industries, manufacturières, mines, bâtiment et distribution
Value=133;Directeurs et cadres de direction, technologies de linformatique et des communications
Value=134;Cadres de direction, services spécialisés
Value=141;Directeurs et gérants, hôtellerie et restauration
Value=142;Directeurs et gérants, commerce de détail et de gros
Value=143;Autres directeurs et gérants
Value=211;Physiciens, chimistes assimilés
Value=212;Mathématiciens, actuaires et statisticiens
Value=213;Spécialistes des sciences de la vie
Value=214;Spécialistes, sciences techniques (sauf électrotechniques)
Value=215;Ingénieurs de lélectrotechnique
Value=216;Architectes, urbanistes, géomètres et concepteurs
Value=221;Médecins
Value=222;Cadres infirmiers et sage femme
Value=223;Spécialistes des médecines traditionnelles et des médecines complémentaires
Value=224;Praticiens  paramédicaux
Value=225;Vétérinaires
Value=226;Autres spécialistes des professions de la santé
Value=231;Professeurs duniversité et détablissement denseignement supérieur
Value=232;Professeurs, enseignement technique et professionnel
Value=233;Professeurs, enseignement secondaire
Value=234;Instituteurs, enseignement primaire et éducateurs de la petite enfance
Value=235;Autre spécialistes de lenseignement
Value=241;Spécialistes en finances
Value=242;Spécialistes des fonctions administratives
Value=243;Spécialistes des ventes, de la commercialisation et de relations publiques
Value=251;Concepteurs et analystes de logiciels et de multimédia
Value=252;Spécialistes des bases des données et des réseaux dordinateurs
Value=261;Juristes
Value=262;Archivistes, bibliothécaires, documentaliste et assimilés
Value=263;Spécialistes des sciences sociales et en théologie
Value=264;Auteurs, journalistes et linguistes
Value=265;Artiste créateurs et exécutants
Value=311;Techniciens des sciences physiques  et techniques
Value=312;Superviseurs, mines, industries manufacturières et bâtiment
Value=313; Techniciens, contrôle de processus industriels
Value=314;Techniciens et travailleurs assimilés des sciences de la vie
Value=315;Contrôleurs et techniciens des moyens de transport maritime et aérien
Value=321;Techniciens de la médecine et de la pharmacie
Value=322;Personnel infirmier et sage femme (niveau intermédiaire)
Value=323;Praticiens des médecines traditionnelles et des médecines complémentaires
Value=324;Techniciens et assistants vétérinaires
Value=325;Autres professions intermédiaires de la santé
Value=331;Professions intermédiaires de la finance et des mathématiques
Value=332;Agents de vente et dachat, courtiers
Value=333;Agent de services commerciaux
Value=334;Secrétaire dadministration et secrétaire spécialiste
Value=335;Professions intermédiaires de lapplication de la loi et assimilées
Value=341;Professions intermédiaires des services juridiques, des services sociaux et des religions
Value=342;Travailleurs du secteur des sports et des activités de remise en forme
Value=343;Professions intermédiaires de la culture, de la création artistique et des activités culinaires
Value=351;Techniciens, opérations et soutien aux utilisateurs des technologies de linformation et des communications
Value=352;Techniciens des télécommunications et de radio diffusion
Value=411;Employés de bureau, fonctions générales
Value=412;Secrétaires (Fonctions générales)
Value=413;Opérateurs sur clavier
Value=421;Guichetiers, encaisseurs et assimilés
Value=422;Employés chargés dinformer la clientèle
Value=431;Employés des services comptables et financiers
Value=432;Employés dapprovisionnement, dordonnancement et des transports
Value=441;Autres employés de type administratif
Value=511;Agent daccompagnement et assimilés (transports et tourisme)
Value=512;Cuisiniers
Value=513;Serveurs et barmen
Value=514;Coiffeurs, esthéticiens et assimilés
Value=515;Intendants, gouvernantes et concierges
Value=516;Autre personnel des services directs aux particuliers
Value=521;Vendeurs sur les marchés et vendeurs ambulants de comestibles
Value=522;Commerçants et vendeur, magasins
Value=523;Caissiers et billettistes
Value=524;Autre vendeurs
Value=531;Gardes denfants et aides-enseignants
Value=532;Aides-soignants
Value=541;Personnel des services de protection et de sécurité
Value=611;Agricultures et ouvriers qualifies, cultures commerciales
Value=612;Eleveurs et ouvriers qualifie de lélevage commercial et assimilé
Value=613;Agriculture et ouvrier qualifiés des culture et de lélevage à but commercial
Value=621;Professions de la sylviculture et assimilées
Value=622;Pécheurs, chasseurs et trappeurs
Value=631;Agriculture, subsistance
Value=632;Eleveurs de bétail, subsistance
Value=633;Agriculture et éleveurs subsistance
Value=634;Pêcheurs, chasseurs, trappeurs et cueilleurs, subsistance
Value=711;Métiers qualifiés du bâtiment (gros ouvre) et assimilés
Value=712;Métiers qualifiés du bâtiment (finitions) et assimilés
Value=713;Ouvriers peintres, ravaleurs de façades et assimilés
Value=721;Mouleurs de fonderie, soudeurs tôliers chaudronniers, monteurs et de charpentes métalliques et assimilés
Value=722;Forgerons, outilleurs et assimilés
Value=723;Mécaniciens et réparateurs de machines
Value=731;Métiers de lartisanat
Value=732;Métiers de limprimerie
Value=741;Installateurs et réparateurs, équipements électriques
Value=742;Monteurs et réparateurs, électronique et télécommunications
Value=751;Métiers qualifiés de l'alimentation et assimilés
Value=752;Métiers qualifiés du traitement du bois, ébénistes et assimilés
Value=753;Métiers qualifiés de l'habillement et assimilés
Value=754;Autres métiers qualifiés de l'industrie et de l'artisanat
Value=811;Conducteurs d'installations dexploitation minière et dextraction des minéraux
Value=812;Conducteurs d'installations de transformation et de traitement superficiel des métaux
Value=813;Conducteurs d'installations et des machines pour la fabrication des produits chimiques et photographiques
Value=814;Conducteurs machines pour la fabrication de produits en caoutchouc, en matières plastique et en papeterie
Value=815;Conducteurs de machines pour la fabrication de produits textiles et darticles en fourrure et en cuir
Value=816;Conducteurs de machines pour la fabrication de denrées alimentaires et de produits connexes
Value=817;Conducteurs d'installations pour la fabrication du papier et pour le travail de bois
Value=818;Autres conducteurs de machines et dinstallations fixes
Value=821;Ouvriers de lassemblage
Value=831;Conducteurs de locomotives et assimilés
Value=832;Conducteurs dautomobiles, de camionnettes et de motocycles
Value=833;Conducteurs de poids lourds et dautobus
Value=834;Conducteurs de matériels et engins mobiles
Value=835;Matelots de pont et assimilés
Value=911;Aide de manage et agents dentretien à domicile et dans les hôtels et bureaux
Value=912;Laveurs de véhicules et de vitres, laveurs de linge et autres nettoyeurs manuels
Value=921;Manuvres de lagriculture, de la pêche et de la sylviculture
Value=931;Manuvres des mines, du bâtiment et des travaux publics
Value=932;Manuvres des industries manufacturières
Value=933;Manuvres des transports et de lentreposage
Value=941;Assistants de fabrication de lalimentation
Value=951;Travailleurs des petits métiers des rues et assimilés
Value=952;Vendeurs ambulants (à lexception de lalimentation)
Value=961;Eboueurs
Value=962;Autres professions élémentaires
Value=997;Les appelés du contingent (service national)
Value=999;Non déclarés

[Item]
Label=Situation de nom dans la profession
Name=ACTC3
Start=77
Len=1
DataType=Numeric

[ValueSet]
Label=Situation de nom dans la profession
Name=ACTC3_VS1
Value=1;Employeur
Value=2;Indépendant/ artisan individuel
Value=3;Salarié
Value=4;Apprenti
Value=5;Aide familiale

[Item]
Label=Libellé occupation principale ISIC
Name=ACTC4N
Start=78
Len=40
DataType=Alpha

[Item]
Label=Code Occupation principale ISIC
Name=ACTC4
Start=118
Len=2
DataType=Numeric

[ValueSet]
Label=Code Occupation principale ISIC
Name=ACTC4_VS1
Value=0;EN ATTENTE
Value=1;Agriculture, chasse, services annexes
Value=2;Sylviculture, exploitation forestière, services annexes
Value=5;Pèche, aquaculture
Value=10;Extraction de houille, de lignite et de tourbe
Value=11;Extraction d'hydrocarbures; services annexes
Value=12;Extraction de minerais d'uranium
Value=13;Extraction de minerais métalliques
Value=14;Autres industries extractives
Value=15;Industries alimentaires
Value=16;Industrie du tabac
Value=17;Industrie textile
Value=18;Industrie de l'habillement et des fourrures
Value=19;Industrie du cuir et de la chaussure
Value=20;Travail du bois et fabrication d'articles en bois
Value=21;Industrie du papier et du carton
Value=22;Edition, imprimerie, reproduction
Value=23;Cokéfaction, raffinage, industries nucléaires
Value=24;Industrie chimique
Value=25;Industrie du caoutchouc et des plastiques
Value=26;Fabrication d'autres produits minéraux non métallique
Value=27;Métallurgie
Value=28;Travail des métaux
Value=29;Fabrication de machines et équipements
Value=30;Fabrication de machines de bureau et de matériel informatique
Value=31;Fabrication de machines et appareils électriques
Value=32;Fabrication d'équipements de radio, télévision et communication
Value=33;Fabrication d'instruments médicaux, de précision, d'optique, d'horlogerie
Value=34;Industrie automobile
Value=35;Fabrication d'autres matériels de transport
Value=36;Fabrication de meubles; industries diverses
Value=37;Récupération
Value=40;Production et distribution d'électricité, de gaz et de chaleur
Value=41;Captage, traitement et distribution d'eau
Value=45;Construction
Value=50;Commerce et reparation automobile
Value=51;Commerce de gros et intermédiaires du commerce
Value=52;Commerce de détails et réparation d'articles domestiques
Value=55;Hôtels et restaurants
Value=60;Transports terrestres
Value=61;Transports par eau
Value=62;Transports aériens
Value=63;Services auxiliaires des transports
Value=64;Postes et télécommunications
Value=65;Intermédiation financière
Value=66;Assurance
Value=67;Auxiliaires financiers et d'assurance
Value=70;Activités immobilières
Value=71;Location sans operateur
Value=72;Activités informatiques
Value=73;Recherche et développement
Value=74;Services fournis principalement aux entreprises
Value=75;Administration publique
Value=80;Education
Value=85;Sante et action sociale
Value=90;Assainissement, voirie et gestion des déchets
Value=91;Activités associatives
Value=92;Activités récréatives, culturelles et sportives
Value=93;Services personnels
Value=95;Services domestiques
Value=99;Activités extraterritoriales

[Item]
Label=Secteur [nom] exerce son emploi secondaire
Name=ACTC5
Start=120
Len=1
DataType=Numeric

[ValueSet]
Label=Secteur [nom] exerce son emploi secondaire
Name=ACTC5_VS1
Value=1;Public national
Value=2;Privé national
Value=3;Etranger
Value=4;Mixte

[Item]
Label=Affilié à la securité sociale (CNAS, CASNOS) dans le cadre de cet emploi
Name=ACTC6
Start=121
Len=1
DataType=Numeric

[ValueSet]
Label=Affilié à la securité sociale (CNAS, CASNOS) dans le cadre de cet emploi
Name=ACTC6_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=A un contrat / accord ecrit ou verbal pour cet emploi
Name=ACTC7
Start=122
Len=1
DataType=Numeric

[ValueSet]
Label=A un contrat / accord ecrit ou verbal pour cet emploi
Name=ACTC7_VS1
Value=1;Oui, contrat
Value=2;Oui, accord
Value=3;Non

[Item]
Label=Nombre de mois [NOM] a occupé cet emploi
Name=ACTC8
Start=123
Len=2
DataType=Numeric

[Item]
Label=Nombe de jours par mois [NOM] a habituellement travaillé dans cet emploi
Name=ACTC9
Start=125
Len=1
DataType=Numeric

[Item]
Label=Nombre d'heures [NOM] travaille-t- il/elle habituellement par jour à cet emploi ?
Name=ACTC10
Start=126
Len=2
DataType=Numeric

[Item]
Label=Dernier paiement
Name=ACTC11A
Start=128
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Unité deTemps
Name=ACTC11B
Start=140
Len=1
DataType=Numeric

[ValueSet]
Label=Unité deTemps
Name=ACTC11B_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=Recevoir d'autres paiements
Name=ACTC12
Start=141
Len=1
DataType=Numeric

[ValueSet]
Label=Recevoir d'autres paiements
Name=ACTC12_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=La valeur de ces paiements en espèces au cours des 12 derniers mois
Name=ACTC13A
Start=142
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre d'unité de temps
Name=ACTC13B
Start=154
Len=2
DataType=Numeric

[Item]
Label=Unité de temps
Name=ACTC13C
Start=156
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de temps
Name=ACTC13C_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=Le travail que [NOM] a fait était permanent ou temporaire
Name=ACTC14
Start=157
Len=1
DataType=Numeric

[ValueSet]
Label=Le travail que [NOM] a fait était permanent ou temporaire
Name=ACTC14_VS1
Value=1;Permanent à  plein temps
Value=2;Permanent à  temps partiel
Value=3;Temporaire à plein temps
Value=4;Temporaire à temps partiel

[Item]
Label=e [NOM] est disponible à travailler davantage
Name=ACTC15
Start=158
Len=1
DataType=Numeric

[ValueSet]
Label=e [NOM] est disponible à travailler davantage
Name=ACTC15_VS1
Value=1;Oui
Value=2;Non

[Record]
Label=Caractéristiques des occupés au cours des 12 derniers mois
Name=QHSEC08D
RecordTypeValue='F'
Required=No
MaxRecords=90
RecordLen=163

[Item]
Label=Numero ligne du membre
Name=ACTD_0
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero ligne du membre
Name=ACTD_0_VS1
Value=1:50

[Item]
Label=Household member name
Name=QHNOT
Start=13
Len=20
DataType=Alpha

[Item]
Label=Au cours des 12 derniers mois, [NOM] a travaillé pour un salaire, un profit ou un gain familial
Name=ACTD1
Start=33
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 12 derniers mois, [NOM] a travaillé pour un salaire, un profit ou un gain familial
Name=ACTD1_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Au cours des 12 derniers mois, [NOM] exerce un emploi principal différent de son emploi principal actuel
Name=ACTD2
Start=34
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 12 derniers mois, [NOM] exerce un emploi principal différent de son emploi principal actuel
Name=ACTD2_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Libellé occupation principale ISCO
Name=ACTD1P
Start=35
Len=40
DataType=Alpha

[Item]
Label=Code Occupation principale ISCO
Name=ACTD3
Start=75
Len=2
DataType=Numeric

[ValueSet]
Label=Code Occupation principale ISCO
Name=ACTD3_VS1
Value=0;EN ATTENTE
Value=11;Chefs d'entreprise, hauts fonctionnaires et législateurs
Value=12;Directeurs administratifs et commerciaux
Value=13;Directeurs de production et de services spécialisés
Value=14;Directeurs de l'hôtellerie, du commerce de détail et des autres services
Value=21;Professionnels des sciences et de l'ingénierie
Value=22;Professionnels de la santé
Value=23;Professionnels de l'enseignement
Value=24;Professionnels des affaires et de l'administration
Value=25;Professionnels des technologies de l'information et des communications
Value=26;Professionnels du droit, du social et de la culture
Value=31;Professionnels associés en sciences et ingénierie
Value=32;Professionnels de la santé associés
Value=33;Professionnels associés des affaires et de l'administration
Value=34;Professionnels associés dans les domaines juridique, social, culturel et connexes
Value=35;Techniciens de l'information et des communications
Value=41;Commissaires généraux et claviers
Value=42;Commis aux services à la clientèle
Value=43;Commis à l'enregistrement numérique et matériel
Value=44;Autres employés de bureau
Value=51;Travailleurs des services personnels
Value=52;Travailleurs de la vente
Value=53;Travailleurs de l'industrie des soins personnels
Value=54;Travailleurs des services de protection
Value=61;Travailleurs agricoles qualifiés axés sur le marché
Value=62;Travailleurs qualifiés de la sylviculture, de la pêche et de la chasse axés sur le marché
Value=63;Agriculteurs, pêcheurs, chasseurs et cueilleurs pratiquant une agriculture de subsistance
Value=71;Ouvriers du bâtiment et des métiers connexes (à l'exclusion des électriciens)
Value=72;Ouvriers de la métallurgie, de la machinerie et des métiers connexes
Value=73;Travailleurs de l'artisanat et de l'imprimerie
Value=74;Travailleurs du secteur de l'électricité et de l'électronique
Value=75;Travailleurs de l'industrie alimentaire, du travail du bois, de l'habillement et des autres métiers artisanaux et connexes
Value=81;Opérateurs de machines et d'installations fixes
Value=82;Assembleurs
Value=83;Conducteurs et opérateurs d'installations mobiles
Value=91;Nettoyeurs et aides
Value=92;Travailleurs de l'agriculture, de la sylviculture et de la pêche
Value=93;Travailleurs des mines, de la construction, de l'industrie manufacturière et des transports
Value=94;Assistants de préparation des aliments
Value=95;Travailleurs de la rue et du secteur de la vente et des services connexes
Value=96;Travailleurs des ordures ménagères et autres travailleurs élémentaires
Value=1;Officiers des forces armées
Value=2;Sous-officiers des forces armées
Value=3;Professions des forces armées, autres grades

[Item]
Label=Situation de nom dans la profession
Name=ACTD4
Start=77
Len=1
DataType=Numeric

[ValueSet]
Label=Situation de nom dans la profession
Name=ACTD4_VS1
Value=1;Employeur
Value=2;Indépendant/Artisan individuel
Value=3;Salarié
Value=4;Apprenti
Value=5;Aide familiale

[Item]
Label=Libellé occupation principale ISIC
Name=ACTD1Q
Start=78
Len=40
DataType=Alpha

[Item]
Label=Code Occupation principale ISIC
Name=ACTD5
Start=118
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code Occupation principale ISIC
Name=ACTD5_VS1
Value=1;Traitement et conservation de viande
Value=2;Traitement et conservation de poissons, crustacés et mollusques
Value=3;Traitement et conservation de fruits et légumes
Value=4;Fabrication d’huiles et graisses végétales et animales
Value=5;Fabrication de produits laitiers
Value=6;Travail des grains et fabrication de produits amylacés
Value=7;Fabrication d’autres produits alimentaires
Value=8;Fabrication d’aliments pour animaux
Value=10;Fabrication de boissons
Value=11;Culture de céréales (à l’exception du riz), de légumineuses et de graines oléagineuses
Value=12;Culture du riz
Value=13;Culture de légumes et de melons, de racines et tubercules
Value=14;Culture de canne à sucre
Value=15;Culture de tabac
Value=16;Culture de plantes à fibres textiles
Value=19;Autres cultures temporaires
Value=20;Fabrication de produits à base de tabac
Value=21;Culture de raisin
Value=22;Culture de fruits tropicaux et subtropicaux
Value=23;Culture d’agrumes
Value=24;Culture de fruits à pépins et de fruits à noyaux
Value=25;Culture d’autres fruits sur arbres et arbustes, et de fruits à coque
Value=26;Culture de fruits oléagineux
Value=27;Culture de plantes pour boisson
Value=28;Culture de plantes pour épices, de plantes aromatiques et de planres pour médicaments et produits pharmaceutiques
Value=29;Autres cultures permanentes
Value=30;Prolifération végétale
Value=31;Filature, tissage et achèvement des textiles
Value=39;Fabrication d’autres articles textiles
Value=41;Élevage de bovins et de buffles
Value=41;Fabrication d’articles d’habillement autre qu’en fourrure
Value=42;Élevage de chevaux et autres équidés
Value=42;Fabrication d’articles en fourrure
Value=43;Élevage de chameaux et autres camélidés
Value=43;Fabrication d’articles de bonneterie
Value=44;Élevage de moutons et de chèvres
Value=45;Élevage de porcins
Value=46;Élevage de volailles
Value=49;Élevage d’autres animaux
Value=50;Exploitation mixte
Value=51;Apprêt et tannage des cuirs; fabrication d’articles de voyage et de maroquinerie, d’articles de sellerie et de bourrellerie; préparation et teinture des fourrures
Value=52;Fabrication de chaussures
Value=61;Activités d’appui à la culture
Value=61;Sciage et rabotage du bois
Value=62;Activités d’appui à la production animale
Value=62;Fabrication d’articles en bois, liège, vannerie et sparterie
Value=63;Activités consécutives à la récolte
Value=64;Préparation des semences aux fins de propagation
Value=70;Chasse, piégeage et activités de services connexes
Value=70;Fabrication de papier et d’articles en papier
Value=81;Imprimerie et activités annexes
Value=82;Reproduction de supports enregistrés
Value=91;Cokéfaction
Value=92;Production de produits pétroliers raffinés
Value=1;Fabrication de produits chimiques de base, d’engrais et de produits azotés, de matières plastiques et de caoutchouc synthétique sous formes primaires
Value=2;Fabrication d’autres produits chimiques
Value=3;Fabrication de fibres synthétiques ou artificielles
Value=10;Sylviculture et autres activités d’exploitation forestière
Value=10;Fabrication de préparations pharmaceutiques, de produits chimiques à usage médicinal et de produits d’herboristerie
Value=20;Exploitation forestière
Value=21;Fabrication de produits en caoutchouc
Value=22;Fabrication d’articles en matières plastiques
Value=30;Récolte de produits forestiers autres que le bois
Value=31;Fabrication de verre et d’articles en verre
Value=39;Fabrication de produits minéraux non métalliques, n.c.a.
Value=40;Services d’appui à la sylviculture
Value=41;Sidérurgie et première transformation de l’acier
Value=42;Métallurgie et première transformation des métaux précieux et des métaux non ferreux
Value=43;Fonderie
Value=51;Construction et menuiserie métalliques; fabrication de citernes, réservoirs et générateurs de vapeur
Value=52;Fabrication d’armes et de munitions
Value=59;Fabrication d’autres ouvrages en métaux; activités de services du travail des métaux
Value=61;Fabrication de composants électroniques et de dispositifs d’affichage
Value=62;Fabrication d’ordinateurs et de matériel périphérique
Value=63;Fabrication de matériel de communication
Value=64;Fabrication de matériel électronique grand public
Value=65;Fabrication de matériel pour la mesure, la vérification, la navigation et le contrôle; horlogerie
Value=66;Fabrication de matériel d’irradiation, électromédical et électrothérapeutique.
Value=67;Fabrication d’instruments d’optique et de matériel photographique
Value=68;Fabrication de supports magnétiques et optiques
Value=71;Fabrication de moteurs, génératrices et transformateurs électriques, de matériel électrique de distribution et de commandes
Value=72;Fabrication de batteries et d’accumulateurs
Value=73;Fabrication de câbles et dispositifs de câblage
Value=74;Fabrication d’appareils électriques d’éclairage
Value=75;Fabrication d’appareils ménagers
Value=79;Fabrication d’autres matériels électriques
Value=81;Fabrication de machines d’usage général
Value=82;Fabrication de machines d’usage spécifique
Value=91;Construction de véhicules automobiles
Value=92;Fabrication de carrosseries pour véhicules automobiles; fabrication de remorques et de semi-remorques
Value=93;Fabrication de pièces et accessoires pour véhicules automobiles et leurs moteurs
Value=1;Construction de navires et de bateaux
Value=2;Fabrication de matériel ferroviaire roulant
Value=3;Construction aéronautique et spatiale et de matériel connexe
Value=4;Fabrication de véhicules militaires de combat
Value=9;Fabrication de matériels de transport, n.c.a.
Value=10;Fabrication de meubles
Value=11;Pêche en mer
Value=12;Pêche en eau douce
Value=21;Aquaculture en mer
Value=21;Fabrication de bijouterie et d’articles similaires
Value=22;Aquaculture en eau douce
Value=22;Fabrication d’instruments de musique
Value=23;Fabrication d’articles de sport
Value=24;Fabrication de jeux et jouets
Value=25;Fabrication d’instruments et appareils médicaux et dentaires
Value=29;Autres activités de fabrication, n.c.a.
Value=31;Réparation d’ouvrages en métaux, de machines et matériel
Value=32;Installation de machines et de matériel pour l’industrie
Value=51;Production, transport et distribution d’électricité
Value=52;Fabrication de gaz, distribution par conduites de combustibles gazeux
Value=53;Production et distribution de vapeur et climatisation
Value=60;Collecte et traitement des eaux, distribution d’eau
Value=70;Réseau d’assainissement
Value=81;Collecte des déchets
Value=82;Traitement et évacuation des déchets
Value=83;Récupération des matières
Value=90;Activités de remise en état et autres services de traitement des déchets
Value=10;Construction de bâtiments
Value=21;Construction de routes et de voies ferrées
Value=22;Projets d’installation d’équipements collectifs
Value=29;Autres projets de génie civil
Value=31;Démolition et préparation des sites
Value=32;Travaux d’installations d’électricité et de plomberie et autres travaux d’installation
Value=33;Travaux de finition
Value=39;Autres activités de construction spécialisées
Value=51;Vente de véhicules automobiles
Value=52;Entretien et réparation de véhicules automobiles
Value=53;Commerce de pièces et accessoires de véhicules automobiles
Value=54;Commerce, entretien et réparation de motocycles et de leurs pièces et accessoires
Value=61;Activités d’intermédiaires de commerce de gros
Value=62;Commerce de gros de produits agricoles bruts et d’animaux vivants
Value=63;Commerce de gros de produits alimentaires, boissons et tabac
Value=64;Commerce de gros d’articles de ménage
Value=65;Commerce de gros de machines, équipements et fournitures
Value=66;Autres commerces de gros spécialisés
Value=69;Commerce de gros non spécialisé
Value=71;Commerce de détail en magasins non spécialisés
Value=72;Commerce de détail de produits alimentaires, boissons et tabacs en magasins spécialisés
Value=73;Commerce de détail de carburants automobiles en magasins spécialisés
Value=74;Commerce de détail d’équipement d’information et de communication en magasins spécialisés
Value=75;Commerce de détail d’autres équipements ménagers en magasins spécialisés
Value=76;Commerce de détail d’articles pour la culture et les loisirs, en magasins spécialisés
Value=77;Commerce de détail d’autres articles en magasins spécialisés
Value=78;Vente de détail sur éventaires et marchés
Value=79;Vente de détail autre qu’en magasins, éventaires ou marchés
Value=91;Transports par chemin de fer
Value=92;Autres transports terrestres
Value=93;Transports par conduites
Value=1;Transports maritimes et côtiers
Value=2;Transports par voies navigables intérieures
Value=10;Extraction de houille
Value=11;Transport aérien de voyageurs
Value=12;Transport aérien de marchandises
Value=20;Extraction de lignite
Value=21;Magasinage et entreposage
Value=22;Activités annexes des transports
Value=31;Activités de poste
Value=32;Activités de courrier
Value=51;Activités d’hébergement temporaire
Value=52;Terrains de camping, parcs pour véhicules de loisirs et caravanes
Value=59;Autres activités d’hébergement
Value=61;Activités de restaurants et de services de restauration mobiles
Value=62;Activités de restauration en kiosque et autres activités de services de restauration
Value=63;Activités de consommation de boissons
Value=81;Édition de livres, revues et autres activités d’édition
Value=82;Édition de logiciels
Value=91;Activités de production de films cinématographiques et vidéo, et de programmes de télévision
Value=92;Activités d’enregistrement du son et d’édition musicale.
Value=1;Radiodiffusion
Value=2;Activités de production et de diffusion de programmes de télévision
Value=10;Extraction de pétrole brut
Value=11;Activités de télécommunications par câble
Value=12;Activités de télécommunications sans fil
Value=13;Activités de télécommunications par satellite
Value=19;Autres activités de télécommunications
Value=20;Extraction de gaz naturel
Value=20;Programmation informatique; conseils et activités connexes
Value=31;Activités de traitement des données, d’hébergement et activités connexes; portails d’entrée sur le Web
Value=39;Autres activités de services d’information
Value=41;Intermédiation monétaire
Value=42;Activités des sociétés de portefeuille
Value=43;Fonds fiduciaires, fonds et entités financières analogues
Value=49;Autres activités de services financiers, à l’exception des activités d’assurances et de caisses de retraite
Value=51;Activités d’assurances
Value=52;Activités de réassurance
Value=53;Activités de caisses de retraite
Value=61;Activités auxiliaires des services financiers à l’exception des assurances et des caisses de retraite
Value=62;Activités auxiliaires de financement des assurances et caisses de retraite
Value=63;Activités de gestion de fonds
Value=81;Activités immobilières sur biens propres ou loués
Value=82;Activités immobilières à forfait ou sous contrat
Value=91;Activités juridiques
Value=92;Activités comptables et d’audit; conseil fiscal
Value=1;Activités de bureaux principaux
Value=2;Activités de conseils en matière de gestion
Value=10;Extraction de minerais métalliques
Value=11;Activités d’architecture et d’ingénierie et de conseils techniques connexes
Value=12;Activités d’essais et d’analyses techniques
Value=21;Extraction de minerais d’uranium et de thorium
Value=21;Recherche-développement expérimental en sciences physiques et naturelles et en ingénierie
Value=22;Recherche-développement expérimental en sciences sociales et humaines
Value=29;Extraction de minerais et d’autres métaux non ferreux
Value=31;Publicité
Value=32;Activités d’études de marché et de sondage
Value=41;Activités de conception de modèles
Value=42;Activités photographiques
Value=49;Autres activités professionnelles, scientifiques et techniques, n.c.a.
Value=50;Activités de services vétérinaires
Value=71;Location de véhicules automobiles
Value=72;Location d’articles personnels ou ménagers
Value=73;Location d’autres machines, équipement et biens corporels
Value=74;Location de produits de la propriété intellectuelle et d’autres produits similaires, à l’exception des droits d’auteur
Value=81;Activités des agences de placement
Value=82;Activités des agences d’emploi temporaire
Value=83;Autres activités de fourniture de personnel
Value=91;Activités des agences de voyages et des voyagistes
Value=99;Autres activités des services de réservation et activités connexes
Value=1;Activités de services de sécurité privés
Value=2;Activités des services de systèmes de sécurité
Value=3;Activités d’enquête
Value=10;Extraction de pierres, de sables et d’argiles
Value=11;Activités d’appui aux installations intégrées
Value=12;Activités de nettoyage
Value=13;Activités des services d’entretien des espaces verts
Value=21;Activités d’appui administratif
Value=22;Activités des centres d’appel
Value=23;Organisation de congrès et de foires commerciales
Value=29;Activités de services d’appui aux entreprises, n.c.a.
Value=41;Administration générale; administration de la politique économique et sociale
Value=42;Services fournis à l’ensemble de la collectivité
Value=43;Activités de sécurité sociale obligatoire
Value=51;Activités d’enseignement préprimaire et primaire
Value=52;Activités d’enseignement secondaire
Value=53;Activités d’enseignement supérieur
Value=54;Autres activités d’enseignement
Value=55;Activités d’appui pédagogique
Value=61;Activités hospitalières
Value=62;Activités de pratique médicale et dentaire
Value=69;Autres activités relatives à la santé
Value=71;Installations de soins de santé en établissement hospitalier
Value=72;Activités de soins de santé pour retardés mentaux, malades mentaux et toxicomanes
Value=73;Activités de soins de santé en établissement hospitalier pour les personnes âgées et les handicapés
Value=79;Autres activités de soins de santé en établissement hospitalier
Value=81;Activités d’action sociale sans hébergement pour les personnes âgées et les handicapés
Value=89;Autres activités d’action sociale sans hébergement
Value=91;Extraction de minerais pour l’industrie chimique et d’engrais naturels
Value=92;Extraction de tourbe
Value=93;Extraction de sel
Value=99;Autres activités extractives, n.c.a.
Value=0;Activités créatives, arts et spectacles
Value=10;Activités annexes à l’extraction depétrole et de gaz naturel
Value=10;Activités des bibliothèques, archives, musées et autres activités culturelles
Value=20;Activités de jeux de hasard et de pari
Value=31;Activités sportives
Value=32;Autres activités récréatives et de loisirs
Value=41;Activités d’organisations associatives économiques, patronales et professionnelles
Value=42;Activités de syndicats de salariés
Value=49;Activités d’autres organisations associatives
Value=51;Réparation d’ordinateurs et de matériel de communication
Value=52;Réparation d’articles personnels et ménagers
Value=60;Autres activités de services personnels
Value=70;Activités des ménages privés employant du personnel domestique
Value=81;Activités non différenciées de production de biens des ménages privés pour usage propre
Value=82;Activités non différenciées de production de services des ménages privés pour usage propre
Value=90;Activités annexes d’autres activités extractives
Value=90;Activités des organisations et organismes extra-territoriaux

[Item]
Label=Secteur nom a travaille principalement
Name=ACTD6
Start=120
Len=1
DataType=Numeric

[ValueSet]
Label=Secteur nom a travaille principalement
Name=ACTD6_VS1
Value=1;Public national
Value=2;Privé national
Value=3;Etranger
Value=4;Mixte

[Item]
Label=Emploi  rentrait dans le cadre d'une aide etatique
Name=ACTD7
Start=121
Len=1
DataType=Numeric

[ValueSet]
Label=Emploi  rentrait dans le cadre d'une aide etatique
Name=ACTD7_VS1
Value=1;TUP-HIMO
Value=2;DAIP (ANEM)
Value=3;DAIS (ADS)
Value=4;ANAD (ex ANSEJ)
Value=5;CNAC
Value=6;ANGEM
Value=7;Autre
Value=8;Non

[Item]
Label=Affilié à la securité sociale (CNAS, CASNOS) dans le cadre de cet emploi
Name=ACTD8
Start=122
Len=1
DataType=Numeric

[ValueSet]
Label=Affilié à la securité sociale (CNAS, CASNOS) dans le cadre de cet emploi
Name=ACTD8_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=A un contrat / accord (ecrit ou verbal) pour cet emploi
Name=ACTD9
Start=123
Len=1
DataType=Numeric

[ValueSet]
Label=A un contrat / accord (ecrit ou verbal) pour cet emploi
Name=ACTD9_VS1
Value=1;Oui, contrat
Value=2;Oui, accord
Value=3;Non

[Item]
Label=Au cours des 12 derniers mois, pendant combien de mois [NOM] a-t-il/elle occupé cet emploi
Name=ACTD13
Start=124
Len=2
DataType=Numeric

[Item]
Label=Pendant ces mois , combien de jours par mois nom a habituellement travaillé dans cet emploi
Name=ACTD14
Start=126
Len=3
DataType=Numeric

[Item]
Label=Combien d'heures nom travaille habituellement par jour à cet emploi
Name=ACTD15
Start=129
Len=3
DataType=Numeric

[Item]
Label=Quel a été  le dernier paiement de [NOM] en espèces pour cet emploi (hors paiements en nature et indemnités)
Name=ACTD16A
Start=132
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=quelle période de temps ce paiement couvre-t-il
Name=ACTD16B
Start=144
Len=2
DataType=Numeric

[Item]
Label=Unité de temps de ce paiement
Name=ACTD16C
Start=146
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de temps de ce paiement
Name=ACTD16C_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=[NOM] a-t-il reçu d'autres paiements (y compris des primes, des encouragements, des commissions, des allocations, des pourboires, etc.)
Name=ACTD17
Start=147
Len=1
DataType=Numeric

[ValueSet]
Label=[NOM] a-t-il reçu d'autres paiements (y compris des primes, des encouragements, des commissions, des allocations, des pourboires, etc.)
Name=ACTD17_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=La valeur de ces paiements en espèces au cours des 12 derniers mois
Name=ACTD18A
Start=148
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de l'unité de temps
Name=ACTD18B
Start=160
Len=2
DataType=Numeric

[Item]
Label=Unité de temps de ce paiement
Name=ACTD18C
Start=162
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de temps de ce paiement
Name=ACTD18C_VS1
Value=1;Heure
Value=2;Journée
Value=3;Semaine
Value=4;Quinzaine
Value=5;Mois
Value=6;Trimestre
Value=7;Semestre
Value=8;Année

[Item]
Label=Le travail que [NOM] a fait était permanent ou temporaire
Name=ACTD19
Start=163
Len=1
DataType=Numeric

[ValueSet]
Label=Le travail que [NOM] a fait était permanent ou temporaire
Name=ACTD19_VS1
Value=1;Permanent à pleintemps
Value=2;Permanent à  temps partiel
Value=3;Temporaire à plein temps
Value=4;Temporaire à temps partiel

[Record]
Label=Caractéristiques de non occupés
Name=QHSEC08E
RecordTypeValue='G'
Required=No
MaxRecords=90
RecordLen=112

[Item]
Label=Numero ligne du membre
Name=ACTE_0
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero ligne du membre
Name=ACTE_0_VS1
Value=1:50

[Item]
Label=Household member name
Name=QHNOU
Start=13
Len=20
DataType=Alpha

[Item]
Label=disponible à travailler au cours des 4 dernières semaines, s'il y avait eu une opportunité de travailler
Name=ACTE_1
Start=33
Len=1
DataType=Numeric

[ValueSet]
Label=disponible à travailler au cours des 4 dernières semaines, s'il y avait eu une opportunité de travailler
Name=ACTE_1_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Faire des démarches au cours des 4 dernières semaines pour trouver du travail ou démarrer sa propre entreprise
Name=ACTE_2
Start=34
Len=1
DataType=Numeric

[ValueSet]
Label=Faire des démarches au cours des 4 dernières semaines pour trouver du travail ou démarrer sa propre entreprise
Name=ACTE_2_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Raison de ne faire aucun effort pour trouver du travail ou démarrer une entrepris
Name=ACTE_3
Start=35
Len=15
DataType=Alpha

[ValueSet]
Label=Raison de ne faire aucun effort pour trouver du travail ou démarrer une entrepris
Name=ACTE_3_VS1
Value='A              ';Aucun emploi disponible
Value='B              ';Impossible de trouver du travail nécessitant les compétences de [NOM]
Value='C              ';Manque de compétences (formation/scolarité) ou d'expérience nécessaires
Value='D              ';Les employeurs pensent trop vieux / trop jeune pour travailler
Value='E              ';En attente d'être rappelé à l'ancien emploi /saisons pour le travail
Value='F              ';Attendre le résultat pour le recrutement dans la fonction publique / le privé
Value='G              ';Raison familiale / Problèmes de garde d'enfants
Value='H              ';Suit un enseignement /formation en cours
Value='I              ';Problèmes de transport
Value='J              ';Grossesse /Maladie / blessure handicap
Value='K              ';Restriction légale (condamné et autrement limités par la loi)
Value='L              ';Retraite
Value='M              ';Je ne veux pas travailler
Value='X              ';Autre (précisez)

[Item]
Label=Faire quoi pendant cette période pour trouver du travail ou démarrer une entreprise
Name=ACTE_4
Start=50
Len=11
DataType=Alpha

[ValueSet]
Label=Faire quoi pendant cette période pour trouver du travail ou démarrer une entreprise
Name=ACTE_4_VS1
Value='A          ';Postuler à un emploi
Value='B          ';Inscription auprés du bureau de l'emploi
Value='C          ';Relations personnelles ou familiales
Value='D          ';Rechercher un emploi sur internet / dans les
Value='E          ';Pris des mesures pour démarrer une entreprise/ une activité agricole
Value='F          ';Mis à niveau des compétences
Value='G          ';Se déplacer sur les lieux de travail
Value='H          ';Recherché du terrain, bâtiment, équipement
Value='I          ';Passé un test ou un entretien
Value='J          ';Attendu dans la rue pour être recruté pour un travail occasionnel
Value='X          ';Autre (préciser)

[Item]
Label=Prêt à travailler à temps plein ou partiel
Name=ACTE_5
Start=61
Len=1
DataType=Numeric

[ValueSet]
Label=Prêt à travailler à temps plein ou partiel
Name=ACTE_5_VS1
Value=1;Oui, immédiatement
Value=2;Oui, dans les 15 jours
Value=3;Oui, entre 15 jours et un mois
Value=4;Oui, dans plus d'un mois
Value=5;Non

[Item]
Label=Type d'emploi recherché principalement
Name=ACTE_6
Start=62
Len=10
DataType=Alpha

[ValueSet]
Label=Type d'emploi recherché principalement
Name=ACTE_6_VS1
Value='A         ';N'importe quel travail
Value='B         ';Administration ou entreprise d'État
Value='C         ';Grande entreprise privée
Value='D         ';Petite / moyenne entreprise privé ou publique
Value='E         ';Travail indépendant
Value='F         ';Dispositifs d'aide à l'emploi
Value='G         ';ANADE (ex )ANSEJ
Value='H         ';CNAC
Value='I         ';ANGEM
Value='X         ';Autre (précisez)

[Item]
Label=La durée  de rchercher un travailler
Name=ACTE_7
Start=72
Len=1
DataType=Numeric

[ValueSet]
Label=La durée  de rchercher un travailler
Name=ACTE_7_VS1
Value=1;Moins de 1 mois
Value=2;De 1 à 3 mois
Value=3;De 3 à 6 mois
Value=4;De 6 mois à 1 année
Value=5;De 1  à 2 années
Value=6;Plus de 2 années

[Item]
Label=Raison principale de chercher un travail
Name=ACTE_8
Start=73
Len=1
DataType=Numeric

[ValueSet]
Label=Raison principale de chercher un travail
Name=ACTE_8_VS1
Value=1;A perdu l'emploi précédent
Value=2;A quitté l'emplois
Value=3;Est en quête d'un premier emploi

[Item]
Label=Code isco du métier ou la profession exerer dans l'ancien emploi
Name=ACTE_10A
Start=74
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code isco du métier ou la profession exerer dans l'ancien emploi
Name=ACTE_10A_VS1
Value=11;Chefs d'entreprise, hauts fonctionnaires et législateurs
Value=12;Directeurs administratifs et commerciaux
Value=13;Directeurs de production et de services spécialisés
Value=14;Directeurs de l'hôtellerie, du commerce de détail et des autres services
Value=21;Professionnels des sciences et de l'ingénierie
Value=22;Professionnels de la santé
Value=23;Professionnels de l'enseignement
Value=24;Professionnels des affaires et de l'administration
Value=25;Professionnels des technologies de l'information et des communications
Value=26;Professionnels du droit, du social et de la culture
Value=31;Professionnels associés en sciences et ingénierie
Value=32;Professionnels de la santé associés
Value=33;Professionnels associés des affaires et de l'administration
Value=34;Professionnels associés dans les domaines juridique, social, culturel et connexes
Value=35;Techniciens de l'information et des communications
Value=41;Commissaires généraux et claviers
Value=42;Commis aux services à la clientèle
Value=43;Commis à l'enregistrement numérique et matériel
Value=44;Autres employés de bureau
Value=51;Travailleurs des services personnels
Value=52;Travailleurs de la vente
Value=53;Travailleurs de l'industrie des soins personnels
Value=54;Travailleurs des services de protection
Value=61;Travailleurs agricoles qualifiés axés sur le marché
Value=62;Travailleurs qualifiés de la sylviculture, de la pêche et de la chasse axés sur le marché
Value=63;Agriculteurs, pêcheurs, chasseurs et cueilleurs pratiquant une agriculture de subsistance
Value=71;Ouvriers du bâtiment et des métiers connexes (à l'exclusion des électriciens)
Value=72;Ouvriers de la métallurgie, de la machinerie et des métiers connexes
Value=73;Travailleurs de l'artisanat et de l'imprimerie
Value=74;Travailleurs du secteur de l'électricité et de l'électronique
Value=75;Travailleurs de l'industrie alimentaire, du travail du bois, de l'habillement et des autres métiers artisanaux et connexes
Value=81;Opérateurs de machines et d'installations fixes
Value=82;Assembleurs
Value=83;Conducteurs et opérateurs d'installations mobiles
Value=91;Nettoyeurs et aides
Value=92;Travailleurs de l'agriculture, de la sylviculture et de la pêche
Value=93;Travailleurs des mines, de la construction, de l'industrie manufacturière et des transports
Value=94;Assistants de préparation des aliments
Value=95;Travailleurs de la rue et du secteur de la vente et des services connexes
Value=96;Travailleurs des ordures ménagères et autres travailleurs élémentaires
Value=1;Officiers des forces armées
Value=2;Sous-officiers des forces armées
Value=3;Professions des forces armées, autres grades

[Item]
Label=Libellé NAA du métier ou la profession exerer dans l'ancien emploi
Name=ACTE_10B
Start=76
Len=2
DataType=Numeric

[Item]
Label=Raison de non disponibilité de travailler au cours des 7 derniers jours
Name=ACTE_11
Start=78
Len=12
DataType=Alpha

[ValueSet]
Label=Raison de non disponibilité de travailler au cours des 7 derniers jours
Name=ACTE_11_VS1
Value='A           ';En cours d'etudes
Value='B           ';Tâches ménagères
Value='C           ';Trop vieux / trop vieux
Value='D           ';Malade
Value='E           ';Inactif/ve
Value='F           ';Retraité(e)/  pensionné(e)
Value='G           ';Grossesse
Value='H           ';Aucune envie de travailler
Value='I           ';Hors saison
Value='J           ';Salaire pas attractif
Value='X           ';Autre (précisez)

[Item]
Label=Conditions,pour la disponibilité au travail
Name=ACTE_12
Start=90
Len=9
DataType=Alpha

[ValueSet]
Label=Conditions,pour la disponibilité au travail
Name=ACTE_12_VS1
Value='A        ';Potentiel de revenu élevé
Value='B        ';Disponibilité des possibilités de formation
Value='C        ';Bénéfices bien définis garantis
Value='D        ';À proximité de la résidence
Value='E        ';Après avoir terminé les études
Value='F        ';Stabilité de l'emploi
Value='G        ';Travail non penible
Value='Y        ';Aucune
Value='X        ';Autre (précisez)

[Item]
Label=Refusé un emploi qui lui avait été proposé
Name=ACTE_13
Start=99
Len=1
DataType=Numeric

[ValueSet]
Label=Refusé un emploi qui lui avait été proposé
Name=ACTE_13_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Raison du refu le travail
Name=ACTE_14
Start=100
Len=10
DataType=Alpha

[ValueSet]
Label=Raison du refu le travail
Name=ACTE_14_VS1
Value='A         ';Les salaires offerts étaient trop bas
Value='B         ';Le travail n'était pas intéressant
Value='C         ';L'emplacement n'était pas pratique
Value='D         ';Le travail ne correspond pas aux qualifications
Value='E         ';La famille n'a pas approuvé l'emploi proposé
Value='F         ';Exiger des heures de travail plus longues/ plus courtes
Value='G         ';En attente d'une meilleure offre d'emploi
Value='H         ';La durée du contrat était trop courte/non définie
Value='I         ';Travail penible
Value='X         ';Autre (spécifier)

[Item]
Label=Principale raison de  ne pas trouver un emploi
Name=ACTE_15
Start=110
Len=1
DataType=Numeric

[ValueSet]
Label=Principale raison de  ne pas trouver un emploi
Name=ACTE_15_VS1
Value=1;Formation et qualification éducative non demandées
Value=2;Formation / qualification inadéquates au poste
Value=3;Emplois non disponibles
Value=4;En cours d'études
Value=6;Autres(spécifier)

[Item]
Label=Subvenir principalement  les besoins
Name=ACTE_18
Start=111
Len=2
DataType=Numeric

[ValueSet]
Label=Subvenir principalement  les besoins
Name=ACTE_18_VS1
Value=1;Perçoit des loyers/Rentes
Value=2;Perçoit une bourse/Transfert
Value=3;Vit de son épargne
Value=4;Vit du produit de ses récoltes
Value=5;vit des transferts en espèces ou en nature gratuits
Value=6;Vit de sa retraite / pension
Value=7;Vit à la charge du ménage uniquement
Value=96;Autre (spécifier)

[Record]
Label=Existence d'entités non agricoles
Name=QHSEC09A
RecordTypeValue='H'
Required=No
RecordLen=23

[Item]
Label=le code du répondant
Name=RNA00
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=le code du répondant
Name=RNA00_VS1
Value=1:50

[Item]
Label=Préparer ou fabriquer des produits  pour les revendre (Préparer des beignets ; Préparer du pain ou de la pâtisserie; fabriquer des jus de fruits; etc.)
Name=RNA01
Start=13
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Préparer ou fabriquer des produits  pour les revendre (Préparer des beignets ; Préparer du pain ou de la pâtisserie; fabriquer des jus de fruits; etc.)
Name=RNA01_VS1
Value=1;oui
Value=2;non

[Item]
Label=posséder une petite entreprise de confection de vêtements (tailleur), de fabrication de sandales ou autres chaussures
Name=RNA02
Start=14
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=posséder une petite entreprise de confection de vêtements (tailleur), de fabrication de sandales ou autres chaussures
Name=RNA02_VS1
Value=1;oui
Value=2;non

[Item]
Label=Posséder une entreprise de menuiserie (fabrication de meubles, lits, portes, fenêtres) en bois ou en métal tel que le fer ou l'aluminium
Name=RNA03
Start=15
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Posséder une entreprise de menuiserie (fabrication de meubles, lits, portes, fenêtres) en bois ou en métal tel que le fer ou l'aluminium
Name=RNA03_VS1
Value=1;oui
Value=2;non

[Item]
Label=posséder une entreprise de commerce ( boutique, vente de matériaux de construction, de matériel informatique, de cartes téléphoniques, de cigarettes au bord de la route, vente de produits agricoles et d'élevage frais, etc.)
Name=RNA04
Start=16
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=posséder une entreprise de commerce ( boutique, vente de matériaux de construction, de matériel informatique, de cartes téléphoniques, de cigarettes au bord de la route, vente de produits agricoles et d'élevage frais, etc.)
Name=RNA04_VS1
Value=1;oui
Value=2;non

[Item]
Label=A exercé une activité libérale dans le domaine de la construction (maçonnerien, plombrie, éléctricité, etc.)
Name=RNA05
Start=17
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=A exercé une activité libérale dans le domaine de la construction (maçonnerien, plombrie, éléctricité, etc.)
Name=RNA05_VS1
Value=1;oui
Value=2;non

[Item]
Label=A exercé une profession libérale (médecin, tradipraticien, avocat, architecte possédant son cabinet ou étant associé, pharmacien ayant son officine, traducteur, Ingénieur ayant son propre bureau d'études, etc.)
Name=RNA06
Start=18
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=A exercé une profession libérale (médecin, tradipraticien, avocat, architecte possédant son cabinet ou étant associé, pharmacien ayant son officine, traducteur, Ingénieur ayant son propre bureau d'études, etc.)
Name=RNA06_VS1
Value=1;oui
Value=2;non

[Item]
Label=Posséder une entreprise rendant tout autre service: réparation et entretien (voitures, motos, radios, ordinateurs, TV ,climatiseurs); lavage de voitures; cireur de chaussures; coiffure; agent/démarcheur immobilier; cabine téléphonique, traitement de texte
Name=RNA07
Start=19
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Posséder une entreprise rendant tout autre service: réparation et entretien (voitures, motos, radios, ordinateurs, TV ,climatiseurs); lavage de voitures; cireur de chaussures; coiffure; agent/démarcheur immobilier; cabine téléphonique, traitement de texte
Name=RNA07_VS1
Value=1;oui
Value=2;non

[Item]
Label=Posséder une voiture, un taxi, un bus ou tout autre moyen de transport pour une activité commerciale
Name=RNA08
Start=20
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Posséder une voiture, un taxi, un bus ou tout autre moyen de transport pour une activité commerciale
Name=RNA08_VS1
Value=1;oui
Value=2;non

[Item]
Label=A possédé un restaurant ou bar, café ou fast-food
Name=RNA09
Start=21
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=A possédé un restaurant ou bar, café ou fast-food
Name=RNA09_VS1
Value=1;oui
Value=2;non

[Item]
Label=Posséder une entreprise non agricole, même s'il s'agit d'une petite activité s'exerçant à domicile ou dans la rue (exemple : fabrication et la vente d'objets d'artisanat, de tapis, de bijoux, etc.), tressage de cheveux, salon de coiffure, etc.
Name=RNA10
Start=22
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Posséder une entreprise non agricole, même s'il s'agit d'une petite activité s'exerçant à domicile ou dans la rue (exemple : fabrication et la vente d'objets d'artisanat, de tapis, de bijoux, etc.), tressage de cheveux, salon de coiffure, etc.
Name=RNA10_VS1
Value=1;oui
Value=2;non

[Item]
Label=Est-ce que la réponse à une des questions Q2 à Q10 est positive
Name=RNA11
Start=23
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Est-ce que la réponse à une des questions Q2 à Q10 est positive
Name=RNA11_VS1
Value=1;oui
Value=2;non

[Record]
Label=Caractéristiques des entités économiques
Name=QHSEC09B
RecordTypeValue='I'
Required=No
MaxRecords=15
RecordLen=60

[Item]
Label=Numero d'Ordre de l'Entreprise (NE)
Name=RNB0O
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero d'Ordre de l'Entreprise (NE)
Name=RNB00_VSA
Value=0:15

[Item]
Label=Numero d'Ordre du principal répondant pour cette entreprise
Name=RNB01
Start=13
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero d'Ordre du principal répondant pour cette entreprise
Name=RNB01_VS1
Value=0:15

[Item]
Label=Numero d'ordre du 1ere propriétaire  de cette entreprise
Name=RNB03A
Start=15
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Numero d'ordre du 2eme propriétaire  de cette entreprise
Name=RNB03B
Start=17
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Code des biens et/ou service produits (NAA)
Name=RNB04A
Start=19
Len=4
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code des biens et/ou service produits (NAA)
Name=RNB04A_VS1
Value=110;Fabrication de boissons
Value=111;Culture de céréales (à l’exception du riz), de légumineuses et de graines oléagineuses
Value=112;Culture du riz
Value=113;Culture de légumes et de melons, de racines et tubercules
Value=114;Culture de canne à sucre
Value=115;Culture de tabac
Value=116;Culture de plantes à fibres textiles
Value=119;Autres cultures temporaires
Value=121;Culture de raisin
Value=122;Culture de fruits tropicaux et subtropicaux
Value=123;Culture d’agrumes
Value=124;Culture de fruits à pépins et de fruits à noyaux
Value=125;Culture d’autres fruits sur arbres et arbustes, et de fruits à coque
Value=126;Culture de fruits oléagineux
Value=127;Culture de plantes pour boisson
Value=128;Culture de plantes pour épices, de plantes aromatiques et de planres pour médicaments et produits pharmaceutiques
Value=129;Autres cultures permanentes
Value=130;Prolifération végétale
Value=131;Filature, tissage et achèvement des textiles
Value=139;Fabrication d’autres articles textiles
Value=141;Élevage de bovins et de buffles
Value=142;Élevage de chevaux et autres équidés
Value=143;Élevage de chameaux et autres camélidés
Value=144;Élevage de moutons et de chèvres
Value=145;Élevage de porcins
Value=146;Élevage de volailles
Value=149;Élevage d’autres animaux
Value=150;Exploitation mixte
Value=151;Apprêt et tannage des cuirs; fabrication d’articles de voyage et de maroquinerie, d’articles de sellerie et de bourrellerie; préparation et teinture des fourrures
Value=161;Activités d’appui à la culture
Value=162;Activités d’appui à la production animale
Value=163;Activités consécutives à la récolte
Value=164;Préparation des semences aux fins de propagation
Value=170;Chasse, piégeage et activités de services connexes
Value=181;Imprimerie et activités annexes
Value=201;Fabrication de produits chimiques de base, d’engrais et de produits azotés, de matières plastiques et de caoutchouc synthétique sous formes primaires
Value=202;Fabrication d’autres produits chimiques
Value=210;Sylviculture et autres activités d’exploitation forestière
Value=220;Exploitation forestière
Value=221;Fabrication de produits en caoutchouc
Value=230;Récolte de produits forestiers autres que le bois
Value=239;Fabrication de produits minéraux non métalliques, n.c.a.
Value=240;Services d’appui à la sylviculture
Value=243;Fonderie
Value=251;Construction et menuiserie métalliques; fabrication de citernes, réservoirs et générateurs de vapeur
Value=259;Fabrication d’autres ouvrages en métaux; activités de services du travail des métaux
Value=273;Fabrication de câbles et dispositifs de câblage
Value=281;Fabrication de machines d’usage général
Value=282;Fabrication de machines d’usage spécifique
Value=301;Construction de navires et de bateaux
Value=309;Fabrication de matériels de transport, n.c.a.
Value=311;Pêche en mer
Value=312;Pêche en eau douce
Value=321;Aquaculture en mer
Value=322;Aquaculture en eau douce
Value=331;Réparation d’ouvrages en métaux, de machines et matériel
Value=381;Collecte des déchets
Value=382;Traitement et évacuation des déchets
Value=431;Démolition et préparation des sites
Value=432;Travaux d’installations d’électricité et de plomberie et autres travaux d’installation
Value=464;Commerce de gros d’articles de ménage
Value=465;Commerce de gros de machines, équipements et fournitures
Value=466;Autres commerces de gros spécialisés
Value=471;Commerce de détail en magasins non spécialisés
Value=472;Commerce de détail de produits alimentaires, boissons et tabacs en magasins spécialisés
Value=474;Commerce de détail d’équipement d’information et de communication en magasins spécialisés
Value=475;Commerce de détail d’autres équipements ménagers en magasins spécialisés
Value=476;Commerce de détail d’articles pour la culture et les loisirs, en magasins spécialisés
Value=477;Commerce de détail d’autres articles en magasins spécialisés
Value=478;Vente de détail sur éventaires et marchés
Value=479;Vente de détail autre qu’en magasins, éventaires ou marchés
Value=491;Transports par chemin de fer
Value=492;Autres transports terrestres
Value=501;Transports maritimes et côtiers
Value=502;Transports par voies navigables intérieures
Value=510;Extraction de houille
Value=520;Extraction de lignite
Value=522;Activités annexes des transports
Value=562;Activités de restauration en kiosque et autres activités de services de restauration
Value=581;Édition de livres, revues et autres activités d’édition
Value=610;Extraction de pétrole brut
Value=620;Extraction de gaz naturel
Value=631;Activités de traitement des données, d’hébergement et activités connexes; portails d’entrée sur le Web
Value=639;Autres activités de services d’information
Value=641;Intermédiation monétaire
Value=649;Autres activités de services financiers, à l’exception des activités d’assurances et de caisses de retraite
Value=651;Activités d’assurances
Value=661;Activités auxiliaires des services financiers à l’exception des assurances et des caisses de retraite
Value=662;Activités auxiliaires de financement des assurances et caisses de retraite
Value=710;Extraction de minerais métalliques
Value=721;Extraction de minerais d’uranium et de thorium
Value=729;Extraction de minerais et d’autres métaux non ferreux
Value=772;Location d’articles personnels ou ménagers
Value=791;Activités des agences de voyages et des voyagistes
Value=810;Extraction de pierres, de sables et d’argiles
Value=812;Activités de nettoyage
Value=821;Activités d’appui administratif
Value=829;Activités de services d’appui aux entreprises, n.c.a.
Value=841;Administration générale; administration de la politique économique et sociale
Value=842;Services fournis à l’ensemble de la collectivité
Value=852;Activités d’enseignement secondaire
Value=854;Autres activités d’enseignement
Value=891;Extraction de minerais pour l’industrie chimique et d’engrais naturels
Value=892;Extraction de tourbe
Value=893;Extraction de sel
Value=899;Autres activités extractives, n.c.a.
Value=910;Activités annexes à l’extraction depétrole et de gaz naturel
Value=931;Activités sportives
Value=932;Autres activités récréatives et de loisirs
Value=941;Activités d’organisations associatives économiques, patronales et professionnelles
Value=949;Activités d’autres organisations associatives
Value=951;Réparation d’ordinateurs et de matériel de communication
Value=952;Réparation d’articles personnels et ménagers
Value=960;Autres activités de services personnels
Value=990;Activités annexes d’autres activités extractives
Value=1010;Traitement et conservation de viande
Value=1020;Traitement et conservation de poissons, crustacés et mollusques
Value=1030;Traitement et conservation de fruits et légumes
Value=1040;Fabrication d’huiles et graisses végétales et animales
Value=1050;Fabrication de produits laitiers
Value=1061;Travail des grains
Value=1062;Fabrication de produits amylacés
Value=1071;Boulangerie, pâtisserie, biscuiterie
Value=1072;Fabrication de sucre
Value=1073;Fabrication de cacao, chocolat et confiserie
Value=1074;Fabrication de pâtes alimentaires, de couscous et de produits farineux similaires
Value=1075;Fabrication de plats préparés
Value=1079;Fabrication d’autres produits alimentaires, n.c.a.
Value=1080;Fabrication d’aliments pour animaux
Value=1101;Distillation, rectification et mélange de spiritueux
Value=1102;Fabrication de vins
Value=1103;Fabrication de boissons alcoolisées à base de malt; production de malt
Value=1104;Fabrication de boissons non alcoolisées; production d’eaux minérales et autres eaux en bouteille
Value=1200;Fabrication de produits à base de tabac
Value=1311;Préparation et filature des fibres textiles
Value=1312;Tissage des textiles
Value=1313;Achèvement des textiles
Value=1391;Fabrication d’étoffes et d’articles de bonneterie
Value=1392;Fabrication d’articles confectionnés en textiles, sauf l’habillement
Value=1393;Fabrication de tapis et carpettes
Value=1394;Fabrication de cordes, câbles, ficelles et filets
Value=1399;Fabrication d’articles textiles, n.c.a.
Value=1410;Fabrication d’articles d’habillement autre qu’en fourrure
Value=1420;Fabrication d’articles en fourrure
Value=1430;Fabrication d’articles de bonneterie
Value=1511;Apprêt et tannage des cuirs; préparation et teinture des fourrures
Value=1512;Fabrication d’articles de voyage et de maroquinerie, d’articles de sellerie et de bourrellerie
Value=1520;Fabrication de chaussures
Value=1610;Sciage et rabotage du bois
Value=1621;Fabrication de feuilles de placage et de panneaux à base de bois
Value=1622;Fabrication d’ouvrages de charpenterie et de menuiserie du bâtiment
Value=1623;Fabrication d’emballages en bois
Value=1629;Fabrication d’autres ouvrages en bois; fabrication d’ouvrages en liège, vannerie et sparterie
Value=1701;Fabrication de pâte, de papier et de carton
Value=1702;Fabrication de papier et carton ondulés et d’emballages en papier et carton
Value=1709;Fabrication d’autres articles en papier et carton
Value=1811;Imprimerie
Value=1812;Activités annexes à l’imprimerie
Value=1820;Reproduction de supports enregistrés
Value=1910;Cokéfaction
Value=1920;Production de produits pétroliers raffinés
Value=2011;Fabrication de produits chimiques de base
Value=2012;Fabrication d’engrais et de produits azotés
Value=2013;Fabrication de matières plastiques et de caoutchouc synthétique sous formes primaires
Value=2021;Fabrication de pesticides et d’autres produits agrochimiques
Value=2022;Fabrication de peintures, vernis et produits similaires, d’encres d’imprimerie et de mastics
Value=2023;Fabrication de savons et détergents, de produits d’entretien, de parfums et de produits pour la toilette
Value=2029;Fabrication d’autres produits chimiques, n.c.a.
Value=2030;Fabrication de fibres synthétiques ou artificielles
Value=2100;Fabrication de préparations pharmaceutiques, de produits chimiques à usage médicinal et de produits d’herboristerie
Value=2211;Fabrication de pneumatiques et de chambres à air; rechapage et resculptage de pneumatiques
Value=2219;Fabrication d’autres articles en caoutchouc
Value=2220;Fabrication d’articles en matières plastiques
Value=2310;Fabrication de verre et d’articles en verre
Value=2391;Fabrication de produits réfractaires
Value=2392;Fabrication de matériaux de construction en argile
Value=2393;Fabrication d’autres articles en porcelaine et en céramique
Value=2394;Fabrication de ciment, chaux et plâtre
Value=2395;Fabrication d’ouvrages en béton, en ciment et en plâtre
Value=2396;Taille, façonnage et finissage de la pierre
Value=2399;Fabrication d’autres produits minéraux non métalliques, n.c.a.
Value=2410;Sidérurgie et première transformation de l’acier
Value=2420;Métallurgie et première transformation des métaux précieux et des métaux non ferreux
Value=2431;Fonderie de métaux ferreux
Value=2432;Fonderie de métaux non ferreux
Value=2511;Construction et menuiserie métalliques
Value=2512;Fabrication de réservoirs, citernes et conteneurs métalliques
Value=2513;Fabrication de générateurs de vapeur (sauf chaudières de chauffage central à eau chaude)
Value=2520;Fabrication d’armes et de munitions
Value=2591;Forgeage, emboutissage, estampage et profilage du métal; métallurgie des poudres
Value=2592;Traitement et revêtement des métaux; façonnage
Value=2593;Fabrication de coutellerie, d’outils à main et de quincaillerie générale
Value=2599;Fabrication d’ouvrages en métaux, n.c.a.
Value=2610;Fabrication de composants électroniques et de dispositifs d’affichage
Value=2620;Fabrication d’ordinateurs et de matériel périphérique
Value=2630;Fabrication de matériel de communication
Value=2640;Fabrication de matériel électronique grand public
Value=2651;Fabrication de matériel pour la mesure, la vérification, la navigation et le contrôle
Value=2652;Fabrication d’horlogerie
Value=2660;Fabrication de matériel d’irradiation, électromédical et électrothérapeutique.
Value=2670;Fabrication d’instruments d’optique et de matériel photographique
Value=2680;Fabrication de supports magnétiques et optiques
Value=2710;Fabrication de moteurs, génératrices et transformateurs électriques, de matériel électrique de distribution et de commandes
Value=2720;Fabrication de batteries et d’accumulateurs
Value=2731;Fabrication de câbles de fibres optiques
Value=2732;Fabrication d’autres fils et câbles électroniques et électriques
Value=2733;Fabrication de dispositifs de câblage
Value=2740;Fabrication d’appareils électriques d’éclairage
Value=2750;Fabrication d’appareils ménagers
Value=2790;Fabrication d’autres matériels électriques
Value=2811;Fabrication de moteurs et de turbines, sauf moteurs pour avions, automobiles et motocycles
Value=2812;Fabrication de matériel hydraulique
Value=2813;Fabrication d’autres pompes, compresseurs, et articles de robinetterie
Value=2814;Fabrication de paliers, d’engrenages et d’organes mécaniques de transmission
Value=2815;Fabrication de fours et de brûleurs
Value=2816;Fabrication de matériel de levage et de manutention
Value=2817;Fabrication de machines et de matériel de bureau (à l’exception des ordinateurs et du matériel périphérique)
Value=2818;Fabrication d’outils à main à entraînement mécanique
Value=2819;Fabrication d’autres machines d’usage général
Value=2821;Fabrication de machines agricoles et forestières
Value=2822;Fabrication de machines pour le travail des métaux et de machines-outils
Value=2823;Fabrication de machines pour la métallurgie
Value=2824;Fabrication de machines pour les mines, les carrières et la construction
Value=2825;Fabrication de machines pour le traitement des produits alimentaires, des boissons et du tabac
Value=2826;Fabrication de machines pour les industries du textile, de l’habillement et des cuirs
Value=2829;Fabrication d’autres machines d’usage spécifique
Value=2910;Construction de véhicules automobiles
Value=2920;Fabrication de carrosseries pour véhicules automobiles; fabrication de remorques et de semi-remorques
Value=2930;Fabrication de pièces et accessoires pour véhicules automobiles et leurs moteurs
Value=3011;Construction de navires et d’engins flottants
Value=3012;Construction de bateaux de plaisance et de sport
Value=3020;Fabrication de matériel ferroviaire roulant
Value=3030;Construction aéronautique et spatiale et de matériel connexe
Value=3040;Fabrication de véhicules militaires de combat
Value=3091;Fabrication de motocycles
Value=3092;Fabrication de bicycles et de véhicules pour invalides
Value=3099;Fabrication d’autres matériels de transport, n.c.a.
Value=3100;Fabrication de meubles
Value=3211;Fabrication de bijouterie et d’articles similaires
Value=3212;Fabrication de bijouterie de fantaisie et d’articles similaires
Value=3220;Fabrication d’instruments de musique
Value=3230;Fabrication d’articles de sport
Value=3240;Fabrication de jeux et jouets
Value=3250;Fabrication d’instruments et appareils médicaux et dentaires
Value=3290;Autres activités de fabrication, n.c.a.
Value=3311;Réparation d’ouvrages en métaux
Value=3312;Réparation de machines
Value=3313;Réparation de matériel électronique et optique
Value=3314;Réparation de matériel électrique
Value=3315;Réparation de matériel de transport, à l’exception des véhicules à moteur
Value=3319;Réparation d’autres matériels
Value=3320;Installation de machines et de matériel pour l’industrie
Value=3510;Production, transport et distribution d’électricité
Value=3520;Fabrication de gaz, distribution par conduites de combustibles gazeux
Value=3530;Production et distribution de vapeur et climatisation
Value=3600;Collecte et traitement des eaux, distribution d’eau
Value=3700;Réseau d’assainissement
Value=3811;Collecte de déchets non dangereux
Value=3812;Collecte de déchets dangereux
Value=3821;Traitement et évacuation des déchets non dangereux
Value=3822;Traitement et évacuation des déchets dangereux
Value=3830;Récupération des matières
Value=3900;Activités de remise en état et autres services de traitement des déchets
Value=4100;Construction de bâtiments
Value=4210;Construction de routes et de voies ferrées
Value=4220;Projets d’installation d’équipements collectifs
Value=4290;Autres projets de génie civil
Value=4311;Démolition
Value=4312;Préparation des sites
Value=4321;Installation d’électricité
Value=4322;Installation de matériel de plomberie, chauffage et climatisation
Value=4329;Autres travaux d’installation
Value=4330;Travaux de finition
Value=4390;Autres activités de construction spécialisées
Value=4510;Vente de véhicules automobiles
Value=4520;Entretien et réparation de véhicules automobiles
Value=4530;Commerce de pièces et accessoires de véhicules automobiles
Value=4540;Commerce, entretien et réparation de motocycles et de leurs pièces et accessoires
Value=4610;Activités d’intermédiaires de commerce de gros
Value=4620;Commerce de gros de produits agricoles bruts et d’animaux vivants
Value=4630;Commerce de gros de produits alimentaires, boissons et tabac
Value=4641;Commerce de gros de textiles, habillement et chaussures
Value=4649;Commerce de gros d’autres articles de ménage
Value=4651;Commerce de gros d’ordinateurs, de matériel périphérique et de logiciels d’ordinateurs
Value=4652;Commerce de gros de parties et d’équipements électroniques et de télécommunication
Value=4653;Commerce de gros de machines, équipement et fournitures agricoles
Value=4659;Commerces de gros d’autres machines et équipements
Value=4661;Commerce de gros de combustibles solides, liquides et gazeux et de produits dérivés
Value=4662;Commerce de gros de métaux et de minerais métalliques
Value=4663;Commerce de gros de matériaux de construction et d’équipement, et fournitures de quincaillerie, plomberie et chauffage
Value=4669;Commerce de gros de déchets et débris et autres produits, n.c.a.
Value=4690;Commerce de gros non spécialisé
Value=4711;Commerce de détail en magasins non spécialisés, avec vente prédominante de produits alimentaires, boissons et tabacs
Value=4719;Autres commerces de détail en magasins non spécialisés
Value=4721;Commerce de détail de produits alimentaires en magasins spécialisés
Value=4722;Commerce de détail de boissons en magasins spécialisés
Value=4723;Commerce de détail de tabacs en magasins spécialisés
Value=4730;Commerce de détail de carburants automobiles en magasins spécialisés
Value=4741;Commerce de détail d’ordinateurs, d’unités périphériques, de logiciels et de matériel de télécommunications en magasins spécialisés
Value=4742;Commerce de détail de matériel audio et vidéo en magasins spécialisés
Value=4751;Commerce de détail de textiles en magasins spécialisés
Value=4752;Commerce de détail de quincaillerie, peinture et verrerie en magasins spécialisés
Value=4753;Commerce de détail de moquette, tapis, revêtements de murs et sols en magasins spécialisés
Value=4759;Commerce de détail d’appareils électroménagers, de meubles de maison, d’articles d’éclairage et d’autres articles ménagers en magasins spécialisés.
Value=4761;Commerce de détail de livres, journaux et articles de papeterie en magasins spécialisés
Value=4762;Commerce de détail d’enregistrements musicaux et vidéo en magasins spécialisés
Value=4763;Commerce de détail de matériel pour le sport en magasins spécialisés
Value=4764;Commerce de détail de jeux et jouets en magasins spécialisés
Value=4771;Commerce de détail de vêtements, de chaussures et d’articles de cuir en magasins spécialisés
Value=4772;Commerce de détail de produits pharmaceutiques et médicaux, de produits de beauté et d’articles de toilette
Value=4773;Autres commerces de détail d’articles neufs en magasins spécialisés
Value=4774;Vente de détail d’articles d’occasion
Value=4781;Vente de détail sur éventaires et marchés de produits alimentaires, boissons et tabac
Value=4782;Vente de détail sur éventaires et marchés de textiles, habillement et chaussures
Value=4789;Vente de détail sur éventaires et marchés d’autres articles
Value=4791;Vente de détail par les entreprises de vente par correspondance, ou sur Internet
Value=4799;Autres commerces de détail autres qu’en magasin, sur éventaires ou marchés
Value=4911;Transport de voyageurs par chemin de fer interurbain
Value=4912;Transport de marchandises par chemin de fer
Value=4921;Transports terrestres de voyageurs par des réseaux urbains et suburbains
Value=4922;Autres transports terrestres de voyageurs
Value=4923;Transports routiers de marchandises
Value=4930;Transports par conduites
Value=5011;Transports maritimes et côtiers de voyageurs
Value=5012;Transports maritimes et côtiers de marchandises
Value=5021;Transport de voyageurs par voies navigables intérieures
Value=5022;Transport de marchandises par voies navigables intérieures
Value=5110;Transport aérien de voyageurs
Value=5120;Transport aérien de marchandises
Value=5210;Magasinage et entreposage
Value=5221;Activités de services annexes des transports terrestres
Value=5222;Activités de services annexes des transports par eau
Value=5223;Activités de services annexes des transports aériens
Value=5224;Manutention
Value=5229;Autres activités annexes des transports
Value=5310;Activités de poste
Value=5320;Activités de courrier
Value=5510;Activités d’hébergement temporaire
Value=5520;Terrains de camping, parcs pour véhicules de loisirs et caravanes
Value=5590;Autres activités d’hébergement
Value=5610;Activités de restaurants et de services de restauration mobiles
Value=5621;Activités de restauration en kiosque
Value=5629;Autres activités de services de restauration
Value=5630;Activités de consommation de boissons
Value=5811;Édition de livres
Value=5812;Édition d’annuaires et de fichiers d’adresses
Value=5813;Édition de journaux, revues professionnelles, et périodiques
Value=5819;Autres activités d’édition
Value=5820;Édition de logiciels
Value=5911;Activités de production de films cinématographiques et vidéo, et de programmes de télévision
Value=5912;Activités consécutives à la production de films cinématographiques et vidéo, et de programmes de télévision
Value=5913;Activités de distribution de films cinématographiques et vidéo, et de programmes de télévision
Value=5914;Activités de projection de films cinématographiques
Value=5920;Activités d’enregistrement du son et d’édition musicale.
Value=6010;Radiodiffusion
Value=6020;Activités de production et de diffusion de programmes de télévision
Value=6110;Activités de télécommunications par câble
Value=6120;Activités de télécommunications sans fil
Value=6130;Activités de télécommunications par satellite
Value=6190;Autres activités de télécommunications
Value=6201;Activités de programmation informatique
Value=6202;Activités de conseils en matière informatique, et de gestion des moyens informatiques
Value=6209;Autres activités de services concernant la technologie de l’information et l’informatique
Value=6311;Traitement de données, hébergement et activités connexes
Value=6312;Portails d’entrée sur le Web
Value=6391;Activités d’agence de presse
Value=6399;Autres activités de services d’information, n.c.a.
Value=6411;Activités de banques centrales
Value=6419;Autres intermédiations monétaires
Value=6420;Activités des sociétés de portefeuille
Value=6430;Fonds fiduciaires, fonds et entités financières analogues
Value=6491;Crédit-bail
Value=6492;Autres activités de crédit
Value=6499;Autres activités de services financiers, à l’exception des activités d’assurance et de caisses de retraite, n.c.a.
Value=6511;Activités d’assurances sur la vie
Value=6512;Activités d’assurances autres que sur la vie
Value=6520;Activités de réassurance
Value=6530;Activités de caisses de retraite
Value=6611;Administration de marchés financiers
Value=6612;Activités de courtage en contrats de valeurs et de produits
Value=6619;Autres activités auxiliaires des activités de services financiers
Value=6621;Évaluation de risque d’assurance et de dommages
Value=6622;Activités des agents d’assurance et des courtiers
Value=6629;Autres activités auxiliaires de financement des assurances et caisses de retraite
Value=6630;Activités de gestion de fonds
Value=6810;Activités immobilières sur biens propres ou loués
Value=6820;Activités immobilières à forfait ou sous contrat
Value=6910;Activités juridiques
Value=6920;Activités comptables et d’audit; conseil fiscal
Value=7010;Activités de bureaux principaux
Value=7020;Activités de conseils en matière de gestion
Value=7110;Activités d’architecture et d’ingénierie et de conseils techniques connexes
Value=7120;Activités d’essais et d’analyses techniques
Value=7210;Recherche-développement expérimental en sciences physiques et naturelles et en ingénierie
Value=7220;Recherche-développement expérimental en sciences sociales et humaines
Value=7310;Publicité
Value=7320;Activités d’études de marché et de sondage
Value=7410;Activités de conception de modèles
Value=7420;Activités photographiques
Value=7490;Autres activités professionnelles, scientifiques et techniques, n.c.a.
Value=7500;Activités de services vétérinaires
Value=7710;Location de véhicules automobiles
Value=7721;Location d’articles pour le sport et les loisirs
Value=7722;Location de vidéocassettes et de vidéodisques
Value=7729;Location d’autres articles personnels ou ménagers
Value=7730;Location d’autres machines, équipement et biens corporels
Value=7740;Location de produits de la propriété intellectuelle et d’autres produits similaires, à l’exception des droits d’auteur
Value=7810;Activités des agences de placement
Value=7820;Activités des agences d’emploi temporaire
Value=7830;Autres activités de fourniture de personnel
Value=7911;Activités des agences de voyages
Value=7912;Activités des voyagistes
Value=7990;Autres activités des services de réservation et activités connexes
Value=8010;Activités de services de sécurité privés
Value=8020;Activités des services de systèmes de sécurité
Value=8030;Activités d’enquête
Value=8110;Activités d’appui aux installations intégrées
Value=8121;Nettoyage général des bâtiments
Value=8129;Autres activités de nettoyage de bâtiments et de nettoyage industriel
Value=8130;Activités des services d’entretien des espaces verts
Value=8211;Activités des services intégrés d’appui administratif
Value=8219;Photocopie, préparation de documents et autres activités spécialisées d’appui aux bureaux
Value=8220;Activités des centres d’appel
Value=8230;Organisation de congrès et de foires commerciales
Value=8291;Activités des agences de recouvrement et de crédit
Value=8292;Activités de conditionnement
Value=8299;Autres activités de services aux entreprises, n.c.a.
Value=8411;Activités d’administration publique générale
Value=8412;Tutelle des activités des organismes qui s’occupent de santé, d’éducation, de culture et d’autres activités sociales, à l’exception de la sécurité sociale
Value=8413;Réglementation de l’exploitation des entreprises et contribution à l’amélioration de son efficacité
Value=8421;Affaires étrangères
Value=8422;Activités de défense
Value=8423;Activités de maintien de l’ordre et de la sécurité publics
Value=8430;Activités de sécurité sociale obligatoire
Value=8510;Activités d’enseignement préprimaire et primaire
Value=8521;Activités d’enseignement secondaire général
Value=8522;Activités d’enseignement secondaire technique et professionnel
Value=8530;Activités d’enseignement supérieur
Value=8541;Activités d’enseignement lié aux sports et aux loisirs
Value=8542;Activités d’enseignement à caractère culturel
Value=8549;Autres activités d’enseignement, n.c.a.
Value=8550;Activités d’appui pédagogique
Value=8610;Activités hospitalières
Value=8620;Activités de pratique médicale et dentaire
Value=8690;Autres activités relatives à la santé
Value=8710;Installations de soins de santé en établissement hospitalier
Value=8720;Activités de soins de santé pour retardés mentaux, malades mentaux et toxicomanes
Value=8730;Activités de soins de santé en établissement hospitalier pour les personnes âgées et les handicapés
Value=8790;Autres activités de soins de santé en établissement hospitalier
Value=8810;Activités d’action sociale sans hébergement pour les personnes âgées et les handicapés
Value=8890;Autres activités d’action sociale sans hébergement
Value=9000;Activités créatives, arts et spectacles
Value=9101;Activités des bibliothèques et archives
Value=9102;Activités des musées et exploitation de sites et monuments historiques
Value=9103;Activités des jardins botaniques et zoologiques et des réserves naturelles
Value=9200;Activités de jeux de hasard et de pari
Value=9311;Exploitation d’installations sportives
Value=9312;Activités des clubs sportifs
Value=9319;Autres activités sportives
Value=9321;Activités des parcs d’attraction et à thèmes
Value=9329;Autres activités récréatives et de loisirs, n.c.a.
Value=9411;Activités d’organisations associatives économiques et patronales
Value=9412;Activités d’organisations associatives professionnelles
Value=9420;Activités de syndicats de salariés
Value=9491;Activités d’organisations religieuses
Value=9492;Activités d’organisations politiques
Value=9499;Activités d’autres organisations associatives, n.c.a.
Value=9511;Réparation d’ordinateurs et de matériel périphérique
Value=9512;Réparation de matériel de communication
Value=9521;Réparation de biens de consommation : matériel électronique
Value=9522;Réparation d’appareils ménagers et de matériel pour la maison et le jardin
Value=9523;Réparation de chaussures et d’articles de cuir
Value=9524;Réparation de mobilier et de meubles de maison
Value=9529;Réparation d’autres articles personnels et ménagers
Value=9601;Lavage et nettoyage à sec de textiles et de fourrures
Value=9602;Coiffure et autres soins esthétiques
Value=9603;Activités de pompes funèbres et activités connexes
Value=9609;Autres activités de services personnels, n.c.a.
Value=9700;Activités des ménages privés employant du personnel domestique
Value=9810;Activités non différenciées de production de biens des ménages privés pour usage propre
Value=9820;Activités non différenciées de production de services des ménages privés pour usage propre
Value=9900;Activités des organisations et organismes extra-territoriaux

[Item]
Label=Libellé des biens et/ou service produits (NAA)
Name=RNB04B
Start=23
Len=4
DataType=Numeric
ZeroFill=Yes

[Item]
Label=L’unité économique dispose d’une comptabilité officielle
Name=RNB06
Start=27
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=L’unité économique dispose d’une comptabilité officielle
Name=RNB06_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=L’effectif permanent qui travaille dans l’entité économique (y compris vous-même)
Name=RNB07
Start=28
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de membres de votre ménage (y compris vous-même) qui travaillent dans l’entité économique
Name=RNB08
Start=31
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Code ID du 1 er  membre de ménage engagé dans l'activité
Name=RNB09A
Start=33
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de mois  travaillés dans l'entité éconolique par le  1 er membre
Name=RNB09B
Start=35
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois  travaillés dans l'entité éconolique par le  1 er membre
Name=RNB09B_VS1
Value=1:12

[Item]
Label=Nombre de jours travaillés par mois travaillé dans l'entité économique  par le  1 er membre
Name=RNB09C
Start=37
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de jours travaillés par mois travaillé dans l'entité économique  par le  1 er membre
Name=RNB09C_VS1
Value=1:31

[Item]
Label=Heures par jour travaillées habituellement dans l'entité économique par le  1 er membre
Name=RNB09D
Start=39
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Heures par jour travaillées habituellement dans l'entité économique par le  1 er membre
Name=RNB09D_VS1
Value=1:24

[Item]
Label=Code ID du 2 eme  membre de ménage engagé dans l'activité
Name=RNB09A1
Start=41
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code ID du 2 eme  membre de ménage engagé dans l'activité
Name=RNB09A1_VS1
Value=1:90

[Item]
Label=Nombre de mois travaillés par la 2 eme membre
Name=RNB09B1
Start=43
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois travaillés par la 2 eme membre
Name=RNB09B1_VS1
Value=1:12

[Item]
Label=Nombre de jour par mois travaillé dans l'entité économique  par le  2 eme membre
Name=RNB09C1
Start=45
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de jour par mois travaillé dans l'entité économique  par le  2 eme membre
Name=RNB09C1_VS1
Value=1:31

[Item]
Label=Heure par jour travaillées habituellement dans l'entité économique par le  2 eme membre
Name=RNB09D1
Start=47
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Heure par jour travaillées habituellement dans l'entité économique par le  2 eme membre
Name=RNB09D1_VS1
Value=1:24

[Item]
Label=Code ID du 3 eme  membre de ménage engagé dans l'activité
Name=RNB09A2
Start=49
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code ID du 3 eme  membre de ménage engagé dans l'activité
Name=RNB09A2_VS1
Value=1:90

[Item]
Label=Nombre de mois travaillés dans l'entité éconolique par le  3 eme  membre
Name=RNB09B2
Start=51
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois travaillés dans l'entité éconolique par le  3 eme  membre
Name=RNB09B2_VS1
Value=1:12

[Item]
Label=Nombre de jours par mois travaillé dans l'entité économique  par le  3 eme membre
Name=RNB09C2
Start=53
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de jours par mois travaillé dans l'entité économique  par le  3 eme membre
Name=RNB09C2_VS1
Value=1:31

[Item]
Label=Heure par jour travaillées habituellement dans l'entité économique par le  3 eme membre
Name=RNB09D2
Start=55
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Heure par jour travaillées habituellement dans l'entité économique par le  3 eme membre
Name=RNB09D2_VS1
Value=1:24

[Item]
Label=Ménage est le seul propriétaire de cette entité économique
Name=RNB10
Start=57
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Ménage est le seul propriétaire de cette entité économique
Name=RNB10_VS1
Value=1;oui
Value=2;non

[Item]
Label=La part des bénéfices qui revient au ménage
Name=RNB11
Start=58
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=La part des bénéfices qui revient au ménage
Name=RNB11_VS1
Value=1;Moins de 25%
Value=2;Entre 25 & 50%
Value=3;Entre 50 & 75%
Value=4;Plus de 75%

[Item]
Label=Type de local exercez-vous cette activité habituellement
Name=RNB12
Start=59
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Type de local exercez-vous cette activité habituellement
Name=RNB12_VS1
Value=1;A domicile
Value=2;Au domicile des clients
Value=3;Ambulant
Value=4;Emplacement fixe dans la rue/marché public
Value=5;Véhicule/bateau (transport)
Value=6;Local professionnel
Value=7;Local virtuel (Internet)
Value=9;Autre (SPECIFIER)

[Item]
Label=Cette entité économique exerce-t-elle une activité exclusivement commerciale
Name=RNB13
Start=60
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Cette entité économique exerce-t-elle une activité exclusivement commerciale
Name=RNB13_VS1
Value=1;Oui
Value=2;Non

[Record]
Label=Recettes des entités commerciales
Name=QHSEC09C
RecordTypeValue='J'
Required=No
MaxRecords=15
RecordLen=121

[Item]
Label=NUMERO D'ORDRE DE L'ENTREPRISE (N.E.)
Name=RND0F
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=NUMERO D'ORDRE DE L'ENTREPRISE (N.E.)
Name=RND01_VSA
Value=1:15

[Item]
Label=L’entité tient-elle un cahier de suivi financier pour  son activité au cours des 12 derniers mois
Name=RND0M
Start=13
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=L’entité tient-elle un cahier de suivi financier pour  son activité au cours des 12 derniers mois
Name=RND0M_VS1
Value=1;oui
Value=2;non

[Item]
Label=Pendant combien de mois cette entité a-t-elle fonctionné au cours des 12 derniers mois
Name=RND0B
Start=14
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Pendant combien de mois cette entité a-t-elle fonctionné au cours des 12 derniers mois
Name=RND03_VSA
Value=0:12

[Item]
Label=Quelle est la valeur des ventes réalisée par cette entité  au cours des 12 derniers mois
Name=RND04
Start=16
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Pour ces valeurs des ventes, quelle est la valeur d’achat des marchandises revendues en l’état
Name=RND0G
Start=28
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Y a-t-il dans l'année des périodes avec des recettes plus importantes et des périodes avec des recettes moins importantes
Name=RND0C
Start=40
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Y a-t-il dans l'année des périodes avec des recettes plus importantes et des périodes avec des recettes moins importantes
Name=RND05_VS1
Value=1;oui
Value=2;non

[Item]
Label=Nombre de mois dont les recettes sont fortes
Name=RND06D
Start=41
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois dont les recettes sont fortes
Name=RND06A_VS1
Value=1:12

[Item]
Label=Nombre de mois dont les recettes sont faibles
Name=RND06E
Start=43
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois dont les recettes sont faibles
Name=RND06B_VS1
Value=1:12

[Item]
Label=Nombre de mois dont les recettes sont normales
Name=RND06C
Start=45
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois dont les recettes sont normales
Name=RND06C_VS1
Value=1:12

[Item]
Label=Montant de la valeur de vente  par U.T dans la periode de recette forte
Name=RND07AA
Start=47
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de mois qui couvrent ce montant ( forte)
Name=RND07A2
Start=59
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois qui couvrent ce montant ( forte)
Name=RND07A2_VS1
Value=1;jour
Value=2;semaine
Value=3;quinzaine
Value=4;mois
Value=5;année

[Item]
Label=Montant de la valeur d'achat par U.T dans la periode de recette forte
Name=RND07B2
Start=60
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de la valeur de vente  par U.T dans la periode de recette faible
Name=RND07AB
Start=72
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de mois qui couvrent ce montant ( faible)
Name=RND07AE
Start=84
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois qui couvrent ce montant ( faible)
Name=RND07AE_VS1
Value=1;jour
Value=2;semaine
Value=3;quinzaine
Value=4;mois
Value=5;année

[Item]
Label=Montant de la valeur d'achat par U.T dans la periode de recette faible
Name=RND07BA
Start=85
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de la valeur de vente  par U.T dans la periode de recette normale
Name=RND07AF
Start=97
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de mois qui couvrent ce montant (normale)
Name=RND07AG
Start=109
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois qui couvrent ce montant (normale)
Name=RND07AG_VS1
Value=1;jour
Value=2;semaine
Value=3;quinzaine
Value=4;mois
Value=5;année

[Item]
Label=Montant de la valeur d'achat par U.T dans la periode de recette normale
Name=RND07BC
Start=110
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Recettes des entités économique
Name=QHSEC09D
RecordTypeValue='['
Required=No
MaxRecords=15
RecordLen=86

[Item]
Label=NUMERO D'ORDRE DE L'ENTREPRISE (N.E.)
Name=RND0H
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=NUMERO D'ORDRE DE L'ENTREPRISE (N.E.)
Name=RND01_VSB
Value=1:15

[Item]
Label=L’entité tient-elle un cahier de suivi financier pour  son activité au cours des 12 derniers mois
Name=RND0D
Start=13
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=L’entité tient-elle un cahier de suivi financier pour  son activité au cours des 12 derniers mois
Name=RND02_VSB
Value=1;oui
Value=2;non

[Item]
Label=Pendant combien de mois cette entité a-t-elle fonctionné au cours des 12 derniers mois
Name=RND0E
Start=14
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Pendant combien de mois cette entité a-t-elle fonctionné au cours des 12 derniers mois
Name=RND03_VSB
Value=0:12

[Item]
Label=Quelle est la valeur des ventes réalisée par cette entité  au cours des 12 derniers mois
Name=RND0I
Start=16
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Y a-t-il dans l'année des périodes avec des recettes plus importantes et des périodes avec des recettes moins importantes
Name=RND0J
Start=28
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Y a-t-il dans l'année des périodes avec des recettes plus importantes et des périodes avec des recettes moins importantes
Name=RND05_VSA
Value=1;oui
Value=2;non

[Item]
Label=Nombre de mois dont les recettes sont fortes
Name=RND06F
Start=29
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois dont les recettes sont fortes
Name=RND06A_VSA
Value=1:12

[Item]
Label=Nombre de mois dont les recettes sont faibles
Name=RND06G
Start=31
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois dont les recettes sont faibles
Name=RND06B_VSA
Value=1:12

[Item]
Label=Nombre de mois dont les recettes sont normales
Name=RND06H
Start=33
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois dont les recettes sont normales
Name=RND06C_VSA
Value=1:12

[Item]
Label=Montant reçu  par U.T dans la periode de recette forte
Name=RND07AC
Start=35
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de mois qui couvre ce montant
Name=RND07AD
Start=47
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois qui couvre ce montant
Name=RND07A2_VSA
Value=1;jour
Value=2;semaine
Value=3;quinzaine
Value=4;mois
Value=5;année

[Item]
Label=Montant reçu  par U.T dans la periode de recette faible
Name=RND07BD
Start=48
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de mois qui couvre ce montant
Name=RND07BB
Start=60
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois qui couvre ce montant
Name=RND07B2_VSB
Value=1;jour
Value=2;semaine
Value=3;quinzaine
Value=4;mois
Value=5;année

[Item]
Label=Montant reçu  par U.T dans la periode de recette normale
Name=RND07CA
Start=61
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de mois qui couvre ce montant
Name=RND07CB
Start=73
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois qui couvre ce montant
Name=RND07C2_VSB
Value=1;jour
Value=2;semaine
Value=3;quinzaine
Value=4;mois
Value=5;année

[Item]
Label=Y a-t-il des biens ou services produits par cette entité qui sont utilisés par les membres de votre ménage ou comme équipements par votre entité de production
Name=RND0K
Start=74
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Y a-t-il des biens ou services produits par cette entité qui sont utilisés par les membres de votre ménage ou comme équipements par votre entité de production
Name=RND08_VSA
Value=1;oui
Value=2;non

[Item]
Label=Quelle est la valeur de ces biens ou services durant les 12 derniers mois
Name=RND0L
Start=75
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Dépenses des entités économique
Name=QHSEC09E
RecordTypeValue='K'
Required=No
MaxRecords=19
RecordLen=111

[Item]
Label=Numéro d'ordre de l'unité de production
Name=RNA0A
Start=11
Len=2
DataType=Numeric

[ValueSet]
Label=Numéro d'ordre de l'unité de production
Name=RNA0A_VSA
Value=1:50

[Item]
Label=Montant dépensé pour l'achat de marchandises revendues en l'état, sans transformation, au cours des 30 derniers jours ou durant le dernier mois où l'entreprise a fonctionné
Name=RND0G1
Start=13
Len=9
DataType=Numeric

[Item]
Label=Montant dépensé en achat de matières premières pour les produits vendus au cours des 30 derniers jours ou durant le dernier mois où l'entreprise a fonctionné
Name=RND0GA
Start=22
Len=9
DataType=Numeric

[Item]
Label=Montant dépensé en salaires, primes et charges sociales au cours des 30 derniers jours ou durant le dernier mois où l'entreprise a fonctionné
Name=RND0GB
Start=31
Len=9
DataType=Numeric

[Item]
Label=Montant dépensé pour louer des terrain, des bâtiments, des machines, des véhicules, etc. au cours des 30 derniers jours ou durant le dernier mois où l'entreprise a fonctionné
Name=RND0GC
Start=40
Len=9
DataType=Numeric

[Item]
Label=Montant dépensé en entretien et réparation au cours des 30 derniers jours ou durant le dernier mois où l'entreprise a fonctionné
Name=RND0GD
Start=49
Len=9
DataType=Numeric

[Item]
Label=Montant dépensé en transport au cours des 30 derniers jours ou durant le dernier mois où l'entreprise a fonctionné
Name=RND0GE
Start=58
Len=9
DataType=Numeric

[Item]
Label=Montant  dépensé en eau et en électricité et gaz au cours des 30 derniers jours ou durant le dernier mois où l'entreprise a fonctionné
Name=RND0GF
Start=67
Len=9
DataType=Numeric

[Item]
Label=Montant  dépensé en autres services (téléphone, télex, fax, internet etc.) au cours des 30 derniers jours ou durant le dernier mois où l'entreprise a fonctionné
Name=RND0GG
Start=76
Len=9
DataType=Numeric

[Item]
Label=Le montant des frais d'assurances payée par l'entreprise au cours des 12 derniers mois
Name=RND0GH
Start=85
Len=9
DataType=Numeric

[Item]
Label=Le montant des impôts et taxes payés par l'entreprise au cours des 12 derniers mois
Name=RND0GI
Start=94
Len=9
DataType=Numeric

[Item]
Label=Montant dépensé en autres charges au cours des 30 derniers jours ou durant le dernier mois où l'entreprise a fonctionné
Name=RND0GJ
Start=103
Len=9
DataType=Numeric

[Record]
Label=Estimation par le propriétaire de l'entité économique
Name=QHSEC09F
RecordTypeValue='\'
Required=No
MaxRecords=15
RecordLen=28

[Item]
Label=numéro d'ordre de l'entreprise (N.E)
Name=RNF01
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant des bénéfices dégagés par l'entité  par UT en DA
Name=RNF01A
Start=13
Len=9
DataType=Numeric

[Item]
Label=unite de temps
Name=RNF01B
Start=22
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=unite de temps
Name=RNF01B_VS1
Value=1;jour
Value=2;semaine
Value=3;quinzaine
Value=4;mois
Value=5;année

[Item]
Label=Nombre d’UT par mois
Name=RNF01C
Start=23
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre d’UT par mois
Name=RNF01C_VS1
Value=1:30

[Item]
Label=Nombre de mois durant les 12 derniers mois qui couvre ce montant
Name=RNF01D
Start=25
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nombre de mois durant les 12 derniers mois qui couvre ce montant
Name=RNF01D_VS1
Value=1:12

[Item]
Label=le bénéfice mensuel dégagé par votre unité
Name=RNF01E
Start=27
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=le bénéfice mensuel dégagé par votre unité
Name=RNF01E_VS1
Value=1;Moins de  30 000 DA
Value=2;De 30 000 à moins de 75 000 DA
Value=3;De 75 000 à moins de 150 000 DA
Value=4;De 150 000 à moins de 300 000 DA
Value=5;De 300 000 DA et plus

[Item]
Label=La part des bénéfices qui revient au ménage
Name=RNF01F
Start=28
Len=1
DataType=Numeric

[ValueSet]
Label=La part des bénéfices qui revient au ménage
Name=RNF01F_VS1
Value=1;Moins de 25%
Value=2;Entre 25 & 50%
Value=3;Entre 50 & 75%
Value=4; Plus de 75%

[Record]
Label=Biens de l'entité économique
Name=QHSEC09G
RecordTypeValue=']'
Required=No
MaxRecords=9
RecordLen=62

[Item]
Label=numéro d'ordre de l'entreprise (N.E)
Name=RNF0A
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Ménage propietaire/copropriétaire locataire ou mixte
Name=RNF0B
Start=13
Len=1
DataType=Numeric

[ValueSet]
Label=Ménage propietaire/copropriétaire locataire ou mixte
Name=RNF0B_VS1
Value=1;Propriétaire
Value=2;Locataire
Value=3;Mixte

[Item]
Label=Valeur actuelle des locaux professionnels
Name=RNF0C
Start=14
Len=9
DataType=Numeric

[Item]
Label=Possession de machines
Name=RNF0D
Start=23
Len=1
DataType=Numeric

[ValueSet]
Label=Possession de machines
Name=RNF0D_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Valeur actuelle des machines
Name=RNF0E
Start=24
Len=9
DataType=Numeric

[Item]
Label=Possession mateiel roulant (vaoiture, motos )
Name=RNF0F
Start=33
Len=1
DataType=Numeric

[ValueSet]
Label=Possession mateiel roulant (vaoiture, motos )
Name=RNF0F_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Valeur actuelle du materiel roulant
Name=RNF0G
Start=34
Len=9
DataType=Numeric

[Item]
Label=Possession mobilier et equipement de bureau
Name=RNF0H
Start=43
Len=1
DataType=Numeric

[ValueSet]
Label=Possession mobilier et equipement de bureau
Name=RNF0H_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Valeur actuelle mobilier  et equipement de bureau
Name=RNF0I
Start=44
Len=9
DataType=Numeric

[Item]
Label=Possession d'autres equipements
Name=RNF0J
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=Possession d'autres equipements
Name=RNF0J_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Valeur actuelle des autres equipements
Name=RNF0K
Start=54
Len=9
DataType=Numeric

[Record]
Label=Credits de l'entité économique
Name=QHSEC09H
RecordTypeValue='^'
Required=No
MaxRecords=15
RecordLen=120

[Item]
Label=numero d'ordre de l'entreprise (N.E)
Name=RNH00
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=numero d'ordre de l'entreprise (N.E)
Name=RNH00_VS1
Value=1:15

[Item]
Label=avez vous contracté des emprunts qui sont en cours de rembourssement
Name=RNH01
Start=13
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=avez vous contracté des emprunts qui sont en cours de rembourssement
Name=RNH01_VS1
Value=1;oui
Value=2;non

[Item]
Label=Nombre de crédit contracté au cours des 12 derniers mois
Name=RNH02
Start=14
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de ces crédits (non compris les intérêts) de 1 crédits
Name=RNH03A
Start=16
Len=9
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de ces crédits (non compris les intérêts) de 2 crédits
Name=RNH03B
Start=25
Len=9
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de ces crédits (non compris les intérêts) de 3 crédits
Name=RNH03C
Start=34
Len=9
DataType=Numeric
ZeroFill=Yes

[Item]
Label=la période totale de remboursement du crédit en mois (crédit 1)
Name=RNH04A
Start=43
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=la période totale de remboursement du crédit en mois (crédit 2)
Name=RNH04B
Start=46
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=la période totale de remboursement du crédit en mois (crédit 3)
Name=RNH04C
Start=49
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=l'organisme (ou la personne) qui a prêté cet argent (crédit 1)
Name=RNH05A
Start=52
Len=9
DataType=Alpha

[ValueSet]
Label=l'organisme (ou la personne) qui a prêté cet argent (crédit 1)
Name=RNH05A_VS1
Value='A        ';Banque
Value='B        ';Micro crédit de l’ANGEM
Value='C        ';Crédit dans le cadre ANADE (ex ANSEJ)
Value='D        ';Crédit dans le cadre CNAC
Value='E        ';crédit dans le cadre d'autoentrepreneur
Value='F        ';Autre institution de microcrédit
Value='G        ';Particuliers (membre de ménage)
Value='H        ';Particuliers (hors ménage)
Value='X        ';Autre(cpécifier)

[Item]
Label=l'organisme (ou la personne) qui a prêté cet argent (crédit 2)
Name=RNH05B
Start=61
Len=9
DataType=Alpha

[ValueSet]
Label=l'organisme (ou la personne) qui a prêté cet argent (crédit 2)
Name=RNH05B_VS1
Value='A        ';Banque
Value='B        ';Micro crédit de l’ANGEM
Value='C        ';Crédit dans le cadre ANADE (ex ANSEJ)
Value='D        ';Crédit dans le cadre CNAC
Value='E        ';crédit dans le cadre d'autoentrepreneur
Value='F        ';Autre institution de microcrédit
Value='G        ';Particuliers (membre de ménage)
Value='H        ';Particuliers (hors ménage)
Value='X        ';Autre(cpécifier)

[Item]
Label=l'organisme (ou la personne) qui a prêté cet argent crédit3)
Name=RNH05C
Start=70
Len=9
DataType=Alpha

[ValueSet]
Label=l'organisme (ou la personne) qui a prêté cet argent crédit3)
Name=RNH05C_VS1
Value='A        ';Banque
Value='B        ';Micro crédit de l’ANGEM
Value='C        ';Crédit dans le cadre ANADE (ex ANSEJ)
Value='D        ';Crédit dans le cadre CNAC
Value='E        ';crédit dans le cadre d'autoentrepreneur
Value='F        ';Autre institution de microcrédit
Value='G        ';Particuliers (membre de ménage)
Value='H        ';Particuliers (hors ménage)
Value='X        ';Autre(cpécifier)

[Item]
Label=Raison principale du recours à l'endettement (crédit1)
Name=RNH06A
Start=79
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Raison principale du recours à l'endettement (crédit1)
Name=RNH06A_VS1
Value=1;Acquisition d'un bien immobilier (local, terrain, etc.)
Value=2;Acquisition d’équipement et de matériels professionnel
Value=3;Achat d'un moyen de transport
Value=4;Charges courantes
Value=5;Autre (spécifier)

[Item]
Label=Raison principale du recours à l'endettement (crédit2)
Name=RNH06B
Start=80
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Raison principale du recours à l'endettement (crédit2)
Name=RNH06B_VS1
Value=1;Acquisition d'un bien immobilier (local, terrain, etc.)
Value=2;Acquisition d’équipement et de matériels professionnel
Value=3;Achat d'un moyen de transport
Value=4;Charges courantes
Value=5;Autre (spécifier)

[Item]
Label=Raison principale du recours à l'endettement (crédit3)
Name=RNH06C
Start=81
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Raison principale du recours à l'endettement (crédit3)
Name=RNH06C_VS1
Value=1;Acquisition d'un bien immobilier (local, terrain, etc.)
Value=2;Acquisition d’équipement et de matériels professionnel
Value=3;Achat d'un moyen de transport
Value=4;Charges courantes
Value=5;Autre (spécifier)

[Item]
Label=l'emprunt a été accordé avec un taux d'intérêt (crédit 1)
Name=RNH07A
Start=82
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=l'emprunt a été accordé avec un taux d'intérêt (crédit 1)
Name=RNH07A_VS1
Value=1;oui
Value=2;non

[Item]
Label=l'emprunt a été accordé avec un taux d'intérêt (crédit 2)
Name=RNH07B
Start=83
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=l'emprunt a été accordé avec un taux d'intérêt (crédit 2)
Name=RNH07B_VS1
Value=1;oui
Value=2;non

[Item]
Label=l'emprunt a été accordé avec un taux d'intérêt (crédit 3)
Name=RNH07C
Start=84
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=l'emprunt a été accordé avec un taux d'intérêt (crédit 3)
Name=RNH07C_VS1
Value=1;oui
Value=2;non

[Item]
Label=cet emprunt a t-il été remboursé au cours des 12 derniers mois (crédit 1)
Name=RNH08A
Start=85
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=cet emprunt a t-il été remboursé au cours des 12 derniers mois (crédit 1)
Name=RNH08A_VS1
Value=1;Oui, totalement
Value=2;Oui, partiellement
Value=3;Non

[Item]
Label=cet emprunt a t-il été remboursé au cours des 12 derniers mois (crédit 2)
Name=RNH08B
Start=86
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=cet emprunt a t-il été remboursé au cours des 12 derniers mois (crédit 2)
Name=RNH08B_VS1
Value=1;Oui, totalement
Value=2;Oui, partiellement
Value=3;Non

[Item]
Label=cet emprunt a t-il été remboursé au cours des 12 derniers mois (crédit 3)
Name=RNH08C
Start=87
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=cet emprunt a t-il été remboursé au cours des 12 derniers mois (crédit 3)
Name=RNH08C_VS1
Value=1;Oui, totalement
Value=2;Oui, partiellement
Value=3;Non

[Item]
Label=le montant du remboursement mensuel en DA (crédit 1)
Name=RNH09A
Start=88
Len=9
DataType=Numeric
ZeroFill=Yes

[Item]
Label=le montant du remboursement mensuel en DA (crédit 2)
Name=RNH09B
Start=97
Len=9
DataType=Numeric
ZeroFill=Yes

[Item]
Label=le montant du remboursement mensuel en DA (crédit 3)
Name=RNH09C
Start=106
Len=9
DataType=Numeric
ZeroFill=Yes

[Item]
Label=le nombre de rembours ments effectués au cours des 12 derniers mois (crédit 1)
Name=RNH10A
Start=115
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=le nombre de rembours ments effectués au cours des 12 derniers mois (crédit 1)
Name=RNH10A_VS1
Value=1:12

[Item]
Label=le nombre de rembours ments effectués au cours des 12 derniers mois (crédit 2)
Name=RNH10B
Start=117
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=le nombre de rembours ments effectués au cours des 12 derniers mois (crédit 2)
Name=RNH10B_VS1
Value=1:12

[Item]
Label=le nombre de rembours ments effectués au cours des 12 derniers mois (crédit 3)
Name=RNH10C
Start=119
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=le nombre de rembours ments effectués au cours des 12 derniers mois (crédit 3)
Name=RNH10C_VS1
Value=1:12

[Record]
Label=Rétrospectif bimestriel alimentaire
Name=QHSEC10A
RecordTypeValue='E'
Required=No
MaxRecords=5
RecordLen=82

[Item]
Label=Code des produits
Name=ALIM01
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Libellés des produits
Name=ALIM02
Start=13
Len=40
DataType=Alpha

[Item]
Label=Unité de mesure
Name=ALIM03
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de mesure
Name=ALIM03_VS1
Value=1;Kg
Value=2;L
Value=3;M
Value=4;P
Value=9;NC

[Item]
Label=Quantite acquise
Name=ALIM04
Start=54
Len=6
DataType=Numeric
Decimal=3
ZeroFill=Yes

[Item]
Label=Prix unitaire
Name=ALIM05
Start=60
Len=6
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Valeur total
Name=ALIM06
Start=66
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Periodicité
Name=ALIM07
Start=78
Len=2
DataType=Numeric

[ValueSet]
Label=Periodicité
Name=ALIM07_VS1
Value=1;1 mois ou moins
Value=2;+1 à 2 mois
Value=3;+2 à 3 mois
Value=4;+3 à 4 mois
Value=5;+4 à  5 mois
Value=6;+5 à 6 mois
Value=7;+6 à 7 mois
Value=8;+7 à  8 mois
Value=9;+8 à 9 mois
Value=10;+9 à 10 mois
Value=11;+10 à 11 mois
Value=12;+11 à 12 mois
Value=13;+12 mois
Value=14;Exceptionnel

[Item]
Label=Mode d'acquisition
Name=ALIM08
Start=80
Len=1
DataType=Numeric

[ValueSet]
Label=Mode d'acquisition
Name=ALIM08_VS1
Value=1;Au comptant
Value=2;Facilité de paiement
Value=3;En ligne
Value=4;A Crédit
Value=5;Autoconsommation
Value=6;Autofourniture
Value=7;Don
Value=8;Rétribution en nature
Value=9;Troc

[Item]
Label=Lieu d'acquisition
Name=ALIM09
Start=81
Len=2
DataType=Numeric

[ValueSet]
Label=Lieu d'acquisition
Name=ALIM09_VS1
Value=1;Epicerie/magasin
Value=2;Supérette
Value=3;Grande surface
Value=4;Boutique en ligne
Value=5;Marché réglementé
Value=6;Commerce non structure
Value=7;ETS pub non commercial
Value=8;ETS privé non commercial
Value=9;Particulier / association
Value=10;Etranger
Value=99;Non concerne

[Record]
Label=Rétrospectif annuel alimentaire
Name=QHSEC10B
RecordTypeValue='L'
Required=No
MaxRecords=5
RecordLen=80

[Item]
Label=Code des produits
Name=ALIMA00
Start=11
Len=2
DataType=Numeric

[Item]
Label=libellés des produits
Name=ALIMA01
Start=13
Len=40
DataType=Alpha

[Item]
Label=unité de mesure
Name=ALIMA02
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=unité de mesure
Name=ALIMA02_VS1
Value=1;Kg
Value=2;L
Value=3;M
Value=4;P
Value=9;NC

[Item]
Label=Quantite acquise
Name=ALIMA03
Start=54
Len=4
DataType=Numeric

[Item]
Label=Prix unitaire
Name=ALIMA04
Start=58
Len=6
DataType=Numeric

[Item]
Label=Valeur total
Name=ALIMA05
Start=64
Len=12
DataType=Numeric

[Item]
Label=Periodicité
Name=ALIMA06
Start=76
Len=2
DataType=Numeric

[ValueSet]
Label=Periodicité
Name=ALIMA06_VS1
Value=1;1 mois ou moins
Value=2;+1 à 2 mois
Value=3;+2 à 3 mois
Value=4;+3 à 4 mois
Value=5;+4 à  5 mois
Value=6;+5 à 6 mois
Value=7;+6 à 7 mois
Value=8;+7 à  8 mois
Value=9;+8 à 9 mois
Value=10;+9 à 10 mois
Value=11;+10 à 11 mois
Value=12;+11 à 12 mois
Value=13;+12 mois
Value=14;Exceptionnel

[Item]
Label=Mode d'acquisition
Name=ALIMA07
Start=78
Len=1
DataType=Numeric

[ValueSet]
Label=Mode d'acquisition
Name=ALIMA07_VS1
Value=1;Au comptant
Value=2;Facilité de paiement
Value=3;En ligne
Value=4; A Crédit
Value=5;Autoconsommation
Value=6;Autofourniture
Value=7;Don
Value=8;Rétribution en nature
Value=9;Troc

[Item]
Label=Lieu d'acquisition
Name=ALIMA08
Start=79
Len=2
DataType=Numeric

[ValueSet]
Label=Lieu d'acquisition
Name=ALIMA08_VS1
Value=1;Epicerie/magasin
Value=2;Supérette
Value=3;Grande surface
Value=4;Boutique en ligne
Value=5;Marché réglementé
Value=6;Commerce non structure
Value=7;ETS pub non commercial
Value=8;ETS privé non commercial
Value=9;Particulier / association
Value=10;Etranger
Value=99;Non concerne

[Record]
Label=Rétrospectif bimestriel non alimentaire
Name=QHSEC10C
RecordTypeValue='M'
Required=No
MaxRecords=5
RecordLen=81

[Item]
Label=Code des produits
Name=ALIMA0A
Start=11
Len=2
DataType=Numeric

[Item]
Label=Libellés des produits
Name=ALIMA0B
Start=13
Len=40
DataType=Alpha

[Item]
Label=unité de mesure
Name=ALIMA0C
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=unité de mesure
Name=ALIMA0C_VS1
Value=1;Kg
Value=2;L
Value=3;M
Value=4;P
Value=9;NC

[Item]
Label=Quantite acquise
Name=ALIMA0D
Start=54
Len=4
DataType=Numeric

[Item]
Label=Prix unitaire
Name=ALIMA0E
Start=58
Len=6
DataType=Numeric

[Item]
Label=Valeur total
Name=ALIMA0F
Start=64
Len=12
DataType=Numeric

[Item]
Label=Periodicité
Name=ALIMA0G
Start=76
Len=2
DataType=Numeric

[ValueSet]
Label=Periodicité
Name=ALIMA0G_VS1
Value=1;1 mois ou moins
Value=2;+1 à 2 mois
Value=3;+2 à 3 mois
Value=4;+3 à 4 mois
Value=5;+4 à  5 mois
Value=6;+5 à 6 mois
Value=7;+6 à 7 mois
Value=8;+7 à  8 mois
Value=9;+8 à 9 mois
Value=10;+9 à 10 mois
Value=11;+10 à 11 mois
Value=12;+11 à 12 mois
Value=13;+12 mois
Value=14;Exceptionnel

[Item]
Label=Mode d'acquisition
Name=ALIMA0H
Start=78
Len=1
DataType=Numeric

[ValueSet]
Label=Mode d'acquisition
Name=ALIMA0H_VS1
Value=1;Au comptant
Value=2;Facilité de paiement
Value=3;En ligne
Value=4; A Crédit
Value=5;Autoconsommation
Value=6;Autofourniture
Value=7;Don
Value=8;Rétribution en nature
Value=9;Troc

[Item]
Label=Lieu d'acquisition
Name=ALIMA0I
Start=79
Len=2
DataType=Numeric

[ValueSet]
Label=Lieu d'acquisition
Name=ALIMA0I_VS1
Value=1;Epicerie/magasin
Value=2;Supérette
Value=3;Grande surface
Value=4;Boutique en ligne
Value=5;Marché réglementé
Value=6;Commerce non structure
Value=7;ETS pub non commercial
Value=8;ETS privé non commercial
Value=9;Particulier / association
Value=10;Etranger
Value=99;Non concerne

[Item]
Label=Etat d'acquisition
Name=ALIMNAJ
Start=81
Len=1
DataType=Numeric

[ValueSet]
Label=Etat d'acquisition
Name=ALIMNAJ_VS1
Value=1;Neuf
Value=2;Occasion
Value=9;N.C

[Record]
Label=Rétrospectif annuel non alimentaire
Name=QHSEC10D
RecordTypeValue='N'
Required=No
MaxRecords=5
RecordLen=81

[Item]
Label=Code des produits
Name=ALIMA001
Start=11
Len=2
DataType=Numeric

[Item]
Label=libellés des produits
Name=ALIMA002
Start=13
Len=40
DataType=Alpha

[Item]
Label=unité de mesure
Name=ALIMA003
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=unité de mesure
Name=ALIMA003_VS1
Value=1;Kg
Value=2;L
Value=3;M
Value=4;P
Value=9;NC

[Item]
Label=Quantite acquise
Name=ALIMA004
Start=54
Len=4
DataType=Numeric

[Item]
Label=Prix unitaire
Name=ALIMA005
Start=58
Len=6
DataType=Numeric

[Item]
Label=Valeur total
Name=ALIMA006
Start=64
Len=12
DataType=Numeric

[Item]
Label=Periodicité
Name=ALIMA007
Start=76
Len=2
DataType=Numeric

[ValueSet]
Label=Periodicité
Name=ALIMA007_VS1
Value=1;1 mois ou moins
Value=2;+1 à 2 mois
Value=3;+2 à 3 mois
Value=4;+3 à 4 mois
Value=5;+4 à  5 mois
Value=6;+5 à 6 mois
Value=7;+6 à 7 mois
Value=8;+7 à  8 mois
Value=9;+8 à 9 mois
Value=10;+9 à 10 mois
Value=11;+10 à 11 mois
Value=12;+11 à 12 mois
Value=13;+12 mois
Value=14;Exceptionnel

[Item]
Label=Mode d'acquisition
Name=ALIMA008
Start=78
Len=1
DataType=Numeric

[ValueSet]
Label=Mode d'acquisition
Name=ALIMA008_VS1
Value=1;Au comptant
Value=2;Facilité de paiement
Value=3;En ligne
Value=4; A Crédit
Value=5;Autoconsommation
Value=6;Autofourniture
Value=7;Don
Value=8;Rétribution en nature
Value=9;Troc

[Item]
Label=Lieu d'acquisition
Name=ALIMA009
Start=79
Len=2
DataType=Numeric

[ValueSet]
Label=Lieu d'acquisition
Name=ALIMA009_VS1
Value=1;Epicerie/magasin
Value=2;Supérette
Value=3;Grande surface
Value=4;Boutique en ligne
Value=5;Marché réglementé
Value=6;Commerce non structure
Value=7;ETS pub non commercial
Value=8;ETS privé non commercial
Value=9;Particulier / association
Value=10;Etranger
Value=99;Non concerne

[Item]
Label=Etat d'acquisition
Name=ALIMNA10
Start=81
Len=1
DataType=Numeric

[ValueSet]
Label=Etat d'acquisition
Name=ALIMNA10_VS1
Value=1;Neuf
Value=2;Occasion
Value=9;N.C

[Record]
Label=Dépense de consommation du mois d'enquête
Name=QHSEC10E
RecordTypeValue='O'
Required=No
MaxRecords=90
RecordLen=83

[Item]
Label=N° de passage
Name=DEPC00
Start=11
Len=2
DataType=Numeric

[Item]
Label=Libellés des produits
Name=DEPC01
Start=13
Len=40
DataType=Alpha

[Item]
Label=Code des produits
Name=DEPC02
Start=53
Len=1
DataType=Numeric

[Item]
Label=Unité de mesure
Name=DEPC03
Start=54
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de mesure
Name=DEPC03_VS1
Value=1;Kg
Value=2;L
Value=3;M
Value=4;P
Value=9;NC

[Item]
Label=Quantite acquise
Name=DEPC04
Start=55
Len=4
DataType=Numeric

[Item]
Label=Prix unitaire
Name=DEPC05
Start=59
Len=6
DataType=Numeric

[Item]
Label=Valeur total
Name=DEPC06
Start=65
Len=12
DataType=Numeric

[Item]
Label=dernière acquisition
Name=DEPC07
Start=77
Len=2
DataType=Numeric

[ValueSet]
Label=dernière acquisition
Name=DEPC07_VS1
Value=1;1 mois ou moins
Value=2;+1 à 2 mois
Value=3;+2 à 3 mois
Value=4;+3 à 4 mois
Value=5;+4 à  5 mois
Value=6;+5 à 6 mois
Value=7;+6 à 7 mois
Value=8;+7 à  8 mois
Value=9;+8 à 9 mois
Value=10;+9 à 10 mois
Value=11;+10 à 11 mois
Value=12;+11 à 12 mois
Value=13;+12 mois
Value=14;Exceptionnel

[Item]
Label=Mode d'acquisition
Name=DEPC08
Start=79
Len=1
DataType=Numeric

[ValueSet]
Label=Mode d'acquisition
Name=DEPC08_VS1
Value=1;Au comptant
Value=2;Facilité de paiement
Value=3;En ligne
Value=4; A Crédit
Value=5;Autoconsommation
Value=6;Autofourniture
Value=7;Don
Value=8;Rétribution en nature
Value=9;Troc

[Item]
Label=Lieu d'acquisition
Name=DEPC09
Start=80
Len=2
DataType=Numeric

[ValueSet]
Label=Lieu d'acquisition
Name=DEPC09_VS1
Value=1;Epicerie/magasin
Value=2;Supérette
Value=3;Grande surface
Value=4;Boutique en ligne
Value=5;Marché réglementé
Value=6;Commerce non structure
Value=7;ETS pub non commercial
Value=8;ETS privé non commercial
Value=9;Particulier / association
Value=10;Etranger
Value=99;Non concerne

[Item]
Label=Durée de renouvellement
Name=DEPC11
Start=82
Len=2
DataType=Numeric

[ValueSet]
Label=Durée de renouvellement
Name=DEPC11_VS1
Value=1;1 jour
Value=2;+1 jour à 3jour
Value=3;+3 jour à 1 semaine
Value=4;+1 semaine à 2 semaine
Value=5;+2 semaine à 1 mois
Value=6;+1 mois à 2 mois
Value=7;+2 mois à 4 mois
Value=8;+4 mois à 6 mois
Value=9;+6 mois à 8 mois
Value=10;+8 mois à 10 mois
Value=11;+10 mois à 12 mois
Value=12;+12 mois
Value=13;exceptionnel

[Record]
Label=Dépense du mois à l'exterieur
Name=QHSEC10F
RecordTypeValue='P'
Required=No
MaxRecords=90
RecordLen=117

[Item]
Label=N° de passage
Name=DEPM01
Start=11
Len=2
DataType=Numeric

[Item]
Label=libellés des produits
Name=DEPM02
Start=13
Len=40
DataType=Alpha

[Item]
Label=Code des produits
Name=DEPM03
Start=53
Len=1
DataType=Numeric

[Item]
Label=Valeur total
Name=DEPM04
Start=54
Len=12
DataType=Numeric

[Item]
Label=Code d'identification de la personne effectué la dépense
Name=DEPM05
Start=66
Len=2
DataType=Numeric

[Item]
Label=Nom et prénom de la personne effectué la dépense
Name=DEPM06
Start=68
Len=50
DataType=Alpha

[Record]
Label=présence aux repas
Name=QHSEC10G
RecordTypeValue='Q'
Required=No
MaxRecords=90
RecordLen=92

[Item]
Label=N° d'ordre  de la personne
Name=PRES00
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=N° d'ordre  de la personne
Name=PRES00_VS1
Value=0:89;Membre du menage
Value=90:99;Visiteur

[Item]
Label=Nom et prénom des personne présentes
Name=PRES01
Start=13
Len=40
DataType=Alpha

[Item]
Label=Nbr de fois durant 3j la personne présente au 2 eme passage au dej
Name=PRES02AA
Start=53
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 2 eme passage au dej
Name=PRES02AA_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 2 eme passage au din
Name=PRES02AB
Start=55
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 2 eme passage au din
Name=PRES02AB_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 3eme passage au dej
Name=PRES02BA
Start=57
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 3eme passage au dej
Name=PRES02BA_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 3 eme passage au din
Name=PRES02BB
Start=59
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 3 eme passage au din
Name=PRES02BB_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 4eme passage au dej
Name=PRES02CA
Start=61
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 4eme passage au dej
Name=PRES02CA_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 4 eme passage au din
Name=PRES02CB
Start=63
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 4 eme passage au din
Name=PRES02CB_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 5 eme passage au dej
Name=PRES02DA
Start=65
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 5 eme passage au dej
Name=PRES02DA_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 5 eme passage au din
Name=PRES02DB
Start=67
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 5 eme passage au din
Name=PRES02DB_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 6 eme passage au dej
Name=PRES02EA
Start=69
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 6 eme passage au dej
Name=PRES02EA_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 6 eme passage au din
Name=PRES02EB
Start=71
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 6 eme passage au din
Name=PRES02EB_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 7 eme passage au dej
Name=PRES02FA
Start=73
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 7 eme passage au dej
Name=PRES02FA_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 7 eme passage au din
Name=PRES02FB
Start=75
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 7 eme passage au din
Name=PRES02FB_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 8 eme passage au dej
Name=PRES02GA
Start=77
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 8 eme passage au dej
Name=PRES02GA_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 8 eme passage au din
Name=PRES02GB
Start=79
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 8 eme passage au din
Name=PRES02GB_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 9 eme passage au dej
Name=PRES02HA
Start=81
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 9 eme passage au dej
Name=PRES02HA_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 9 eme passage au din
Name=PRES02HB
Start=83
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 9 eme passage au din
Name=PRES02HB_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 10 eme passage au dej
Name=PRES02IA
Start=85
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 10 eme passage au dej
Name=PRES02IA_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 10 eme passage au din
Name=PRES02IB
Start=87
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 10 eme passage au din
Name=PRES02IB_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 11 eme passage au dej
Name=PRES02JA
Start=89
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 11 eme passage au dej
Name=PRES02JA_VS1
Value=0:10

[Item]
Label=Nbr de fois durant 3j la personne présente au 11 eme passage au din
Name=PRES02JB
Start=91
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nbr de fois durant 3j la personne présente au 11 eme passage au din
Name=PRES02JB_VS1
Value=0:10

[Record]
Label=Identification des exploitations agricoles
Name=QHSEC11A
RecordTypeValue='U'
Required=No
MaxRecords=50
RecordLen=64

[Item]
Label=N° ordre de la parcelle
Name=RNBA00
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nom de la parcelle
Name=RNB0A
Start=13
Len=40
DataType=Alpha

[Item]
Label=Statut de propriété de cette parcelle ou unité d'elevage actuellement
Name=RNB0B
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=Statut de propriété de cette parcelle ou unité d'elevage actuellement
Name=RNB0B_VS1
Value=1;Propriété du ménage
Value=2;Louée par le ménage
Value=3;Propriété de l'associé
Value=4;Exploitée gratuitement par le ménage
Value=5;Cédée au cours des 12 derniers mois
Value=6;LA CONCESSION (IMTYAZ)
Value=7;LA RÉHABILITATION DES TERRES
Value=8;COOPÉRATIVE AGRICOLE  (ASSOCIATION)

[Item]
Label=Mode de gestion de de cette parcelle ou unité d'élevage au cours des 12 derniers mois
Name=RNB0C
Start=54
Len=1
DataType=Numeric

[ValueSet]
Label=Mode de gestion de de cette parcelle ou unité d'élevage au cours des 12 derniers mois
Name=RNB0C_VS1
Value=1;Gestion exclusive par le ménage
Value=2;Gestion en association
Value=3;Mise en location
Value=4;Mise en jachère
Value=5;Métayage
Value=9;Autre ( à spécifier)

[Item]
Label=Part en pourcentage  de cette terre ou ferme qui revenait à votre ménage pour les 12 derniers mois
Name=RNB0D
Start=55
Len=3
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Part en pourcentage  de cette terre ou ferme qui revenait à votre ménage pour les 12 derniers mois
Name=RNB0D_VS1
Value=0:100;Pourcentage

[Item]
Label=Code  ID du membre 1 qui posséde ou exploite cette terre
Name=RNB0E
Start=58
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code  ID du membre 1 qui posséde ou exploite cette terre
Name=RNB0E_VS1
Value=1:90

[Item]
Label=Code  ID du membre 2 qui posséde ou exploite cette terre
Name=RNB0F
Start=60
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Code  ID du membre 3 qui posséde ou exploite cette terre
Name=RNB0G
Start=62
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code  ID du membre 3 qui posséde ou exploite cette terre
Name=RNB0G_VS1
Value=1:90

[Item]
Label=Activité exercée dans cette terre ou ferme au cours des 12 derniers mois
Name=RNB0H
Start=64
Len=1
DataType=Numeric

[ValueSet]
Label=Activité exercée dans cette terre ou ferme au cours des 12 derniers mois
Name=RNB0H_VS1
Value=1;Cultures / plantation
Value=2;Elevage
Value=3;Cultures / plantations et élevage
Value=4;Aucune activité

[Record]
Label=Cultures
Name=QHSEC11B1
RecordTypeValue='V'
Required=No
MaxRecords=58
RecordLen=104

[Item]
Label=Code cultures
Name=AGRC02B
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code cultures
Name=AGRC02B_VS1
Value=1:15

[Item]
Label=Libellé de la culture
Name=AGRC02B1
Start=13
Len=40
DataType=Alpha

[Item]
Label=Ménage  ou l'un des membres a produit [cultures] au cours des 12 derniers mois
Name=AGRC02B1A
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=Ménage  ou l'un des membres a produit [cultures] au cours des 12 derniers mois
Name=AGRC02B1A_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=la superficie en hectares réservé à cette [CULTURE/PLANTATION] .. au cours des 12 derniers mois
Name=AGRC02BBL
Start=54
Len=6
DataType=Numeric

[Item]
Label=Quantité recoltée pendant la campagne passée
Name=AGRC02B1B
Start=60
Len=5
DataType=Numeric

[Item]
Label=Unité de mesure UM
Name=AGRC02B1C
Start=65
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de mesure UM
Name=AGRC02B1C_VS1
Value=1;Tonne
Value=2;Quintal
Value=3;Kilogramme
Value=4;Gramme
Value=5;Vente sur pied

[Item]
Label=le  pourcentage de cette récolte de [CULTURE/PLANTATION]  est revenu à votre ménage
Name=AGRC02BBM
Start=66
Len=3
DataType=Numeric

[ValueSet]
Label=le  pourcentage de cette récolte de [CULTURE/PLANTATION]  est revenu à votre ménage
Name=AGRC02BBM_VS1
Value=1:100;Pourcentage

[Item]
Label=la quantité de la recolte de [CULTURE/PLANTATION] qui est déstinée à l'autoconsommation ou comme  nourriture pour le bétail
Name=AGRC02B1D
Start=69
Len=5
DataType=Numeric

[Item]
Label=Unité de mesure UM
Name=AGRC02B1E
Start=74
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de mesure UM
Name=AGRC02B1E_VS1
Value=1;Tonne
Value=2;Quintal
Value=3;Kilogramme
Value=4;Gramme
Value=5;Vente sur pied

[Item]
Label=Quelle est la quantité de cette [CULTURE/PLANTATION] qui  a été perdue suite à des catastrophe tel que les incendies ou autres choses
Name=AGRC02B1F
Start=75
Len=5
DataType=Numeric

[Item]
Label=Unité de mesure UM
Name=AGRC02B1G
Start=80
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de mesure UM
Name=AGRC02B1G_VS1
Value=1;Tonne
Value=2;Quintal
Value=3;Kilogramme
Value=4;Gramme
Value=5;Vente sur pied

[Item]
Label=la quantité de production que le ménage a donné comme cadeau ou en zakat ou aûmone ou bien contrepartie de travail au  cours de la campagne écoulée
Name=AGRC02B1H
Start=81
Len=2
DataType=Numeric

[ValueSet]
Label=la quantité de production que le ménage a donné comme cadeau ou en zakat ou aûmone ou bien contrepartie de travail au  cours de la campagne écoulée
Name=AGRC02B1H_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Unité de mesure UM
Name=AGRC02B1J
Start=83
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de mesure UM
Name=AGRC02B1J_VS1
Value=1;Tonne
Value=2;Quintal
Value=3;Kilogramme
Value=4;Gramme
Value=5;Vente sur pied

[Item]
Label=la production ou une partie  revenant au ménage  a été vendue
Name=AGRC02BAK
Start=84
Len=8
DataType=Numeric

[ValueSet]
Label=la production ou une partie  revenant au ménage  a été vendue
Name=AGRC02BAK_VS1
Value=1;oui
Value=2;non

[Item]
Label=quantité de la partie de la production qui a déjà été vendue
Name=AGRC02BBN
Start=92
Len=6
DataType=Numeric

[Item]
Label=Unité de mesure UM
Name=AGRC02B1JA
Start=98
Len=1
DataType=Numeric

[ValueSet]
Label=Unité de mesure UM
Name=AGRC02B1JA_VS1
Value=1;Tonne
Value=2;Quintal
Value=3;Kilogramme
Value=4;Gramme
Value=5;Vente sur pied

[Item]
Label=le montant tiré de la vente de ce produit
Name=AGRC02BBO
Start=99
Len=6
DataType=Numeric

[Record]
Label=Dépenses en Agricultures
Name=QHSEC11B2
RecordTypeValue='W'
Required=No
MaxRecords=16
RecordLen=74

[Item]
Label=Code produits
Name=AGRC01B2
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code produits
Name=AGRC01B2_VS1
Value=1:45

[Item]
Label=Libellé du produit
Name=AGRC02B2
Start=13
Len=50
DataType=Alpha

[Item]
Label=Ménage a payé [article] au cours des 12 derniers mois
Name=AGRC04B3
Start=63
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Elevage
Name=QHSEC11C1
RecordTypeValue='2'
Required=No
MaxRecords=13
RecordLen=129

[Item]
Label=Code elevage
Name=AGRC02C
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code elevage
Name=AGRC02C_VS1
Value=1:15

[Item]
Label=Libellé elevage (Espèces)
Name=AGRC02BA
Start=13
Len=40
DataType=Alpha

[Item]
Label=Au cours des 12 derniers mois, le ménage a élevé des […]
Name=AGRC02B1K
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=Au cours des 12 derniers mois, le ménage a élevé des […]
Name=AGRC02B1K_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Nombre de tete que compte actuellement au total  le troupeau
Name=AGRC02B1L
Start=54
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre appartenant au ménage lui-meme
Name=AGRC02B1M
Start=57
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de ménage possédait ou élevait-il il au cours des 12 derniers mois
Name=AGRC02B1N
Start=60
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre possédés ou élevés par le ménage, sont  nés au cours des 12 derniers mois
Name=AGRC02B1O
Start=63
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre confiés au ménage au  cours des 12 derniers mois
Name=AGRC02B1P
Start=66
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre reçu en don au cours des 12 derniers mois
Name=AGRC02B1Q
Start=69
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre acheté  au cours des 12 derniers mois
Name=AGRC02B1R
Start=72
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Valeur totale de l'achat
Name=AGRC02BAL
Start=75
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre offert en don  à d'autres ménages au cours des 12 derniers mois
Name=AGRC02B1S
Start=87
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre volé ou morts  du fait de catastrophes naturelles
Name=AGRC02B1T
Start=90
Len=3
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Vendre  au cours des 12 derniers mois?
Name=AGRC02B1U
Start=93
Len=1
DataType=Numeric

[ValueSet]
Label=Vendre  au cours des 12 derniers mois?
Name=AGRC02B1U_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Montant reçu des ventes  au cours des 12 derniers mois
Name=AGRC02BAM
Start=94
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de ce revenu de la vente qui revient au ménage
Name=AGRC02BAN
Start=106
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=montant de Revenu reçu de l'arrangement
Name=AGRC02BAO
Start=118
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Dépenses élevage
Name=QHSEC11C2
RecordTypeValue='Y'
Required=No
MaxRecords=13
RecordLen=64

[Item]
Label=Code produits
Name=AGRC01BA
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code produits
Name=AGRC01BA_VS1
Value=1:45

[Item]
Label=Description du dépense
Name=AGRC02BB
Start=13
Len=40
DataType=Alpha

[Item]
Label=Le montant que le ménage a payé [article] au cours des 12 derniers mois
Name=AGRC04BA
Start=53
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=produits dérivés
Name=QHSEC11D
RecordTypeValue='Z'
Required=No
MaxRecords=14
RecordLen=101

[Item]
Label=Code sous produits
Name=AGRC01C2
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code sous produits
Name=AGRC01C2_VS1
Value=1:45

[Item]
Label=Libellé du sous produit
Name=AGRC02C2
Start=13
Len=40
DataType=Alpha

[Item]
Label=Ménage ou l'un de ses membres a produit un [sous produit] au cours des 12 derniers mois
Name=AGRC02B1V
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=Ménage ou l'un de ses membres a produit un [sous produit] au cours des 12 derniers mois
Name=AGRC02B1V_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Valeur estimée du [sous produit] consommé par les membres du ménage au cours des 12 derniers mois
Name=AGRC02B1W
Start=54
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Valeur estimée du [sous produit] donnée à d'autres ménages au cours des 12 derniers mois
Name=AGRC02B1X
Start=66
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant reçu de la vente  de [sous produit] au cours des 12 derniers mois
Name=AGRC02B1Y
Start=78
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant des revenus de la vente des ces [sous produits] qui revient au ménage
Name=AGRC02B1Z
Start=90
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Actifs fonciers pour la production agricole
Name=QHSEC11E1
RecordTypeValue='b'
Required=No
MaxRecords=15
RecordLen=92

[Item]
Label=Numero d'exploitation agricole
Name=AGRC08F1_1
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nom de l'exploitation agricole
Name=AGRC08F1_2
Start=13
Len=40
DataType=Alpha

[Item]
Label=Periode d'aquisition de  cette exploitation
Name=AGRC08F1_3
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=Periode d'aquisition de  cette exploitation
Name=AGRC08F1_3_VS1
Value=1;Moins de 12 mois
Value=2;Plus de 12 mois

[Item]
Label=Mode d’acquisition de cette exploitation
Name=AGRC08F1_4
Start=54
Len=1
DataType=Numeric

[ValueSet]
Label=Mode d’acquisition de cette exploitation
Name=AGRC08F1_4_VS1
Value=1;Achetée
Value=2;Reçue comme don
Value=3;Héritée

[Item]
Label=Valeur (réelle ou estimée) de cette parcelle agricole ou unité d"élevage au moment de son acquisition
Name=AGRC08F1_5
Start=55
Len=9
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant cette parcelle agricole ou unité d'élevage pourrait elle être vendue aujourd’hui
Name=AGRC08F1_6
Start=64
Len=9
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Mode de cession de cette exploitation
Name=AGRC08F1_7
Start=73
Len=1
DataType=Numeric

[ValueSet]
Label=Mode de cession de cette exploitation
Name=AGRC08F1_7_VS1
Value=1;Achetée
Value=2;Reçue comme don
Value=3;Hérité

[Item]
Label=Valeur (réelle ou estimée) de cession de cette parcelle agricole ou unité d"élevage
Name=AGRC08F1_8
Start=74
Len=9
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de location reçu de cette exploitation en DA
Name=AGRC08F1_9
Start=83
Len=9
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Exploitation agricole
Name=AGRC08F1_10
Start=92
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Exploitation agricole
Name=AGRC08F1_10_VS1
Value=1;Oui
Value=2;Non

[Record]
Label=Actifs non fonciers pour la production agricole
Name=QHSEC11E2
RecordTypeValue='c'
Required=No
MaxRecords=17
RecordLen=67

[Item]
Label=Code identifiant
Name=AGRCF2
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code identifiant
Name=AGRCF2_VS1
Value=1:45

[Item]
Label=Nature des actifs
Name=AGRCF2_2
Start=13
Len=40
DataType=Alpha

[Item]
Label=Votre ménage possède-elle cet actif [...] en bon fonctionnement?
Name=AGRCF2_3
Start=53
Len=1
DataType=Numeric

[ValueSet]
Label=Votre ménage possède-elle cet actif [...] en bon fonctionnement?
Name=AGRCF2_3_VS1
Value=1;OUI
Value=2;NON

[Item]
Label=le nombre des [ACTIFS]
Name=AGRCF2_4
Start=54
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant d'obtiendriez-vous de cette vente
Name=AGRCF2_5
Start=56
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Exploitation de foret
Name=QHSEC11F
RecordTypeValue='a'
Required=No
MaxRecords=6
RecordLen=68

[Item]
Label=code produit
Name=EFORET1
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Libellé produit
Name=EFORET01
Start=13
Len=20
DataType=Alpha

[Item]
Label=le montant reçu des ventes de produits forestiers
Name=EFORET2
Start=33
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Valeur des produits forestiers consommés par le ménage au cours des 12 derniers mois
Name=EFORET3
Start=45
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Dépense au cours des 12 derniers mois  pour obtenir ces produits
Name=EFORET4
Start=57
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=pêche et aquaculture
Name=QHSEC11G
RecordTypeValue='q'
Required=No
MaxRecords=5
RecordLen=73

[Item]
Label=Code produit
Name=PECHPS111
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Code produit
Name=PECHPS111_VS1
Value=1:15

[Item]
Label=Libellé produit
Name=PECHES110
Start=13
Len=25
DataType=Alpha

[Item]
Label=le revenu total provenant de la vente [éspece]  au cours des 12 derniers mois
Name=PECHPS112
Start=38
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=la valeur totale de (espèce) consomméspar votre ménage
Name=PECHPS2
Start=50
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant dépensé au cours des 12 derniers mois pour obtenir ce [éspece]
Name=PECHPS113
Start=62
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Pensions et indemnités
Name=QHSEC12A
RecordTypeValue='_'
Required=No
MaxRecords=5
RecordLen=123

[Item]
Label=Code du ligne
Name=PEN00
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Sources de revenus
Name=PEN00A
Start=13
Len=40
DataType=Alpha

[Item]
Label=un membre de votre ménage bénifice de source
Name=PEN01
Start=53
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=un membre de votre ménage bénifice de source
Name=PEN01_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=code ID bénéficière N° 1
Name=PEN02A1
Start=54
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=montant de revenus du 1 bénificiaires
Name=PEN02A2
Start=56
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=code ID bénéficière N° 2
Name=PEN02B1
Start=68
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=montant de revenus du 2 bénificiaires
Name=PEN02B2
Start=70
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=code ID bénéficière N° 3
Name=PEN02C1
Start=82
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=montant de revenus du 3 bénificiaires
Name=PEN02C2
Start=84
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=code ID bénéficière N° 4
Name=PEN02D1
Start=96
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=montant de revenus du 4 bénificiaires
Name=PEN02D2
Start=98
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=code ID bénéficière N° 5
Name=PEN02E1
Start=110
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=montant de revenus du 5 bénificiaires
Name=PEN02E2
Start=112
Len=12
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Filets sociaux
Name=QHSEC12B
RecordTypeValue='g'
Required=No
MaxRecords=10
RecordLen=133

[Item]
Label=Identifiant du programme
Name=FILSOC001
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=source de revenus
Name=FILSOC002
Start=13
Len=45
DataType=Alpha

[Item]
Label=le ménage bénifice du programme
Name=FILSOC01
Start=58
Len=1
DataType=Numeric

[ValueSet]
Label=le ménage bénifice du programme
Name=FILSOC01_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=le ménage / personne specifique bénifice de cette aide
Name=FILSOC02
Start=59
Len=1
DataType=Numeric

[ValueSet]
Label=le ménage / personne specifique bénifice de cette aide
Name=FILSOC02_VS1
Value=1;Menage
Value=2;Individu

[Item]
Label=Code ID 1er bénificiaire
Name=FILSOC3A1
Start=60
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant correspondant du 1er bénificiaire
Name=FILSOC3A2
Start=62
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=UT
Name=FILSOC3A3
Start=74
Len=1
DataType=Numeric

[ValueSet]
Label=UT
Name=FILSOC3A3_VS1
Value=1;Mois
Value=2;Trimestre
Value=3;Années

[Item]
Label=Nombre de unité de temps
Name=FILSOCA4
Start=75
Len=4
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Code ID 2eme bénificiaire
Name=FILSOC3B1
Start=79
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant correspondant du 2eme bénificiaire
Name=FILSOC3B2
Start=81
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=UT
Name=FILSOC3B3
Start=93
Len=1
DataType=Numeric

[ValueSet]
Label=UT
Name=FILSOC3B3_VS1
Value=1;Mois
Value=2;Trimestre
Value=3;Années

[Item]
Label=Nombre de unité de temps
Name=FILSOC3B4
Start=94
Len=4
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Code ID  3eme bénificiaire
Name=FILSOC3C1
Start=98
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant correspondant du 3eme bénificiaire
Name=FILSOC3C2
Start=100
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=UT
Name=FILSOC3C3
Start=112
Len=1
DataType=Numeric

[ValueSet]
Label=UT
Name=FILSOC3C3_VS1
Value=1;Mois
Value=2;Trimestre
Value=3;Années

[Item]
Label=Nombre de unité de temps
Name=FILSOC3C4
Start=113
Len=4
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant reçu de cette aide au cours de 12 derniers mois
Name=FILSOC04A
Start=117
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=UT
Name=FILSOC04B
Start=129
Len=1
DataType=Numeric

[ValueSet]
Label=UT
Name=FILSOC04B_VS1
Value=1;Mois
Value=2;Trimestre
Value=3;Années

[Item]
Label=Nombre de unité de temps
Name=FILSOC04C
Start=130
Len=4
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Revenus du patrimoine et autres revenus
Name=QHSEC12C
RecordTypeValue='R'
Required=No
MaxRecords=10
RecordLen=113

[Item]
Label=code de source
Name=REV00A
Start=11
Len=2
DataType=Numeric

[Item]
Label=libellé de la source  revenus
Name=REV00B
Start=13
Len=50
DataType=Alpha

[Item]
Label=le ménage Bénéficie de sources
Name=REV01
Start=63
Len=1
DataType=Numeric

[ValueSet]
Label=le ménage Bénéficie de sources
Name=REV01_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Code ID 1er bénificiaire
Name=REV02A
Start=64
Len=2
DataType=Numeric

[Item]
Label=Montant
Name=REV02AA
Start=66
Len=8
DataType=Numeric

[Item]
Label=Code ID   2eme  bénificiaire
Name=REV02B
Start=74
Len=2
DataType=Numeric

[Item]
Label=Montant
Name=REV02BB
Start=76
Len=8
DataType=Numeric

[Item]
Label=Code ID 3eme  bénificiaire
Name=REV02C
Start=84
Len=2
DataType=Numeric

[Item]
Label=Montant
Name=REV02CC
Start=86
Len=8
DataType=Numeric

[Item]
Label=Code ID  4eme  bénificiaire
Name=REV02D
Start=94
Len=2
DataType=Numeric

[Item]
Label=Montant
Name=REV02DD
Start=96
Len=8
DataType=Numeric

[Item]
Label=Code ID 5eme  bénificiaire
Name=REV02E
Start=104
Len=2
DataType=Numeric

[Item]
Label=Montant
Name=REV02EE
Start=106
Len=8
DataType=Numeric

[Record]
Label=transferts reçus au cours des 12 derniers mois
Name=QHSEC12D
RecordTypeValue='S'
Required=No
MaxRecords=50
RecordLen=34

[Item]
Label=N° du transfert
Name=TRANS00
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Id personne qui recu le transfert
Name=TRANS1
Start=13
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Lien de parente pour personne qui a fait le transfert
Name=TRANS2
Start=15
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Lien de parente pour personne qui a fait le transfert
Name=TRANS2_VS1
Value=1;conjoint
Value=2;enfant
Value=3;père/mère
Value=4;frère/ soeur
Value=5;autre parent
Value=6;aucun lien de parenté

[Item]
Label=Residence de l'expediteur
Name=TRANS3
Start=16
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Residence de l'expediteur
Name=TRANS3_VS1
Value=1;algérie
Value=2;etranger

[Item]
Label=Nature  transfert
Name=TRANS4
Start=17
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nature  transfert
Name=TRANS4_VS1
Value=1;en éspèce
Value=2;en nature (alimentaire)
Value=3;en nature (non alimentaire)

[Item]
Label=Principale utilisation de ce transfert
Name=TRANS5
Start=18
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Principale utilisation de ce transfert
Name=TRANS5_VS1
Value=1;scolarité, eductaion
Value=2;santé, malade
Value=3;transfert lié à la pension alimentaire
Value=4;selon vos besoin
Value=5;investissement
Value=6;remboursement de prêts ou dettes
Value=7;évenements (cérimonie, fetra, achoura)
Value=99;autre ( spécifier)

[Item]
Label=La frequance du transfert
Name=TRANSA
Start=20
Len=1
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Unite de temps de transfert
Name=TRANSB
Start=21
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Unite de temps de transfert
Name=TRANSB_VS1
Value=1;mois
Value=2;trismestre
Value=3;semestre
Value=4;année
Value=5;irrégulier

[Item]
Label=Montant de trasfert de chaque frequance
Name=TRANSC
Start=22
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Reçu un autre transfert
Name=TRAUT
Start=34
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Reçu un autre transfert
Name=TRAUT_VS1
Value=1;Oui
Value=2;Non

[Record]
Label=Transfert envoyé  au cours des 12 derniers mois
Name=QHSEC12E
RecordTypeValue='T'
Required=No
MaxRecords=50
RecordLen=33

[Item]
Label=N° du transfert
Name=TRANEV00A
Start=11
Len=2
DataType=Numeric

[Item]
Label=ID du benificière
Name=TRANSE1
Start=13
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Lien de parenté pour personne qui a envoyé le transfert
Name=TRANSE2
Start=15
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Lien de parenté pour personne qui a envoyé le transfert
Name=TRANSE2_VS1
Value=1;conjoint
Value=2;enfant
Value=3;père/mère
Value=4;frère/ soeur
Value=5;autre parent
Value=6;aucun lien de parenté

[Item]
Label=Residence de benificière
Name=TRANSE3
Start=17
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Residence de benificière
Name=TRANSE3_VS1
Value=1;algérie
Value=2;etranger

[Item]
Label=Nature du transfert
Name=TRANSE4
Start=18
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Nature du transfert
Name=TRANSE4_VS1
Value=1;en éspèce
Value=2;en nature (alimentaire)
Value=3;en nature (non alimentaire)

[Item]
Label=Principale utilisation de ce transfert
Name=TRANSE5
Start=19
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Principale utilisation de ce transfert
Name=TRANSE5_VS1
Value=1;scolarité, eductaion
Value=2;santé, malade
Value=3;transfert lié à la pension alimentaire
Value=4;selon vos besoin
Value=5;investissement
Value=6;remboursement de prêts ou dettes
Value=7;évenements (cérimonie, fetra, achoura)
Value=9;autre à spécifier

[Item]
Label=Unité de temps de transfert
Name=TRANSE5B
Start=20
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Unité de temps de transfert
Name=TRANSE5B_VS1
Value=1;mois
Value=2;trismestre
Value=3;semestre
Value=4;année
Value=5;irrégulier

[Item]
Label=Montant du transfert  de chaque frequence
Name=TRANSE5C
Start=21
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Envoi un autre transfert
Name=TRAUU
Start=33
Len=1
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Envoi un autre transfert
Name=TRAUU_VS1
Value=1;Oui
Value=2;Non

[Record]
Label=Epargne et crédit
Name=QHSEC13A
RecordTypeValue='h'
Required=No
MaxRecords=45
RecordLen=185

[Item]
Label=Numero d'ordre
Name=LIST001
Start=11
Len=2
DataType=Numeric
ZeroFill=Yes

[ValueSet]
Label=Numero d'ordre
Name=LIST00_VS11
Value=1:45

[Item]
Label=Household member name
Name=QHNOV
Start=13
Len=20
DataType=Alpha

[Item]
Label=Posséssion d'un compte dans une établissement ou une carte prépayée
Name=EPCR1
Start=33
Len=4
DataType=Alpha

[ValueSet]
Label=Posséssion d'un compte dans une établissement ou une carte prépayée
Name=EPCR1_VS1
Value='A   ';Banque classique publique
Value='B   ';Banque classique privée
Value='C   ';Poste
Value='D   ';Carte prépayée

[Item]
Label=Possèssion de l'épargne dans ces comptes
Name=EPCR2
Start=37
Len=1
DataType=Numeric

[ValueSet]
Label=Possèssion de l'épargne dans ces comptes
Name=EPCR2_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Epargne globale de dans ces comptes
Name=EPCR3
Start=38
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de l'épargne conservée dans ces comptes au cours des 12 derniers mois
Name=EPCR4
Start=50
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Possède de l'épargne conservée à la maison
Name=EPCR5
Start=62
Len=1
DataType=Numeric

[ValueSet]
Label=Possède de l'épargne conservée à la maison
Name=EPCR5_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Montant del'épargne globale de conservée à la maison
Name=EPCR6
Start=63
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de l'épargne conservée à la maison au cours des 12 derniers mois
Name=EPCR7
Start=75
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=A contracté un crédit au cours des 12 derniers mois
Name=EPCR8
Start=87
Len=1
DataType=Numeric

[ValueSet]
Label=A contracté un crédit au cours des 12 derniers mois
Name=EPCR8_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Nombre de crédits contractés au cours des 12 derniers moi
Name=EPCR9
Start=88
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de ce(s) crédit(s) au cours des 12 derniers mois
Name=EPCR10A
Start=90
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de ce(s) crédit(s) au cours des 12 derniers mois
Name=EPCR10B
Start=102
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de ce(s) crédit(s) au cours des 12 derniers mois
Name=EPCR10C
Start=114
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de mois le(s) crédit(s) s'étale(nt) t-il(s)
Name=EPCR11A
Start=126
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de mois le(s) crédit(s) s'étale(nt) t-il(s)
Name=EPCR11B
Start=128
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre de mois le(s) crédit(s) s'étale(nt) t-il(s)
Name=EPCR11C
Start=130
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Principale utilisation que [NOM] a fait de ce(s) crédit(s)
Name=EPCR12A
Start=132
Len=1
DataType=Numeric

[ValueSet]
Label=Principale utilisation que [NOM] a fait de ce(s) crédit(s)
Name=EPCR12A_VS1
Value=1;Éducation
Value=2;Santé
Value=3;Équipement du ménage (voiture, appareil ménager, etc.)
Value=4;Acquisition de terrain, bien immobilier, construction, réparation de maison
Value=5;Démarrer une affaire
Value=6;Événements spéciaux (fêtes, mariages, etc.)
Value=7;Loisirs et vacances
Value=9;Autre à spécifier

[Item]
Label=Principale utilisation que [NOM] a fait de ce(s) crédit(s)
Name=EPCR12B
Start=133
Len=1
DataType=Numeric

[ValueSet]
Label=Principale utilisation que [NOM] a fait de ce(s) crédit(s)
Name=EPCR12B_VS1
Value=1;Éducation
Value=2;Santé
Value=3;Équipement du ménage (voiture, appareil ménager, etc.)
Value=4;Acquisition de terrain, bien immobilier, construction, réparation de maison
Value=5;Démarrer une affaire
Value=6;Événements spéciaux (fêtes, mariages, etc.)
Value=7;Loisirs et vacances
Value=9;Autre à spécifier

[Item]
Label=Principale utilisation que [NOM] a fait de ce(s) crédit(s)
Name=EPCR12C
Start=134
Len=1
DataType=Numeric

[ValueSet]
Label=Principale utilisation que [NOM] a fait de ce(s) crédit(s)
Name=EPCR12C_VS1
Value=1;Éducation
Value=2;Santé
Value=3;Équipement du ménage (voiture, appareil ménager, etc.)
Value=4;Acquisition de terrain, bien immobilier, construction, réparation de maison
Value=5;Démarrer une affaire
Value=6;Événements spéciaux (fêtes, mariages, etc.)
Value=7;Loisirs et vacances
Value=9;Autre à spécifier

[Item]
Label=Crédit(s) a-t-il (ont-ils) été contracté(s)
Name=EPCR13A
Start=135
Len=1
DataType=Numeric

[ValueSet]
Label=Crédit(s) a-t-il (ont-ils) été contracté(s)
Name=EPCR13A_VS1
Value=1;Banque
Value=2;Organismes d'aide à l'emploi
Value=3;Particulier (membre de ménage)
Value=4;Particulier (hors le ménage)
Value=5;Employeur
Value=6;Association
Value=9;Autre à spécifier

[Item]
Label=Crédit(s) a-t-il (ont-ils) été contracté(s)
Name=EPCR13B
Start=136
Len=1
DataType=Numeric

[ValueSet]
Label=Crédit(s) a-t-il (ont-ils) été contracté(s)
Name=EPCR13B_VS1
Value=1;Banque
Value=2;Organismes d'aide à l'emploi
Value=3;Particulier (membre de ménage)
Value=4;Particulier (hors le ménage)
Value=5;Employeur
Value=6;Association
Value=9;Autre à spécifier

[Item]
Label=Crédit(s) a-t-il (ont-ils) été contracté(s)
Name=EPCR13C
Start=137
Len=1
DataType=Numeric

[ValueSet]
Label=Crédit(s) a-t-il (ont-ils) été contracté(s)
Name=EPCR13C_VS1
Value=1;Banque
Value=2;Organismes d'aide à l'emploi
Value=3;Particulier (membre de ménage)
Value=4;Particulier (hors le ménage)
Value=5;Employeur
Value=6;Association
Value=9;Autre à spécifier

[Item]
Label=Crédit(s) a(ont) été accordé(s) avec un taux d'intérêt
Name=EPCR14A
Start=138
Len=1
DataType=Numeric

[ValueSet]
Label=Crédit(s) a(ont) été accordé(s) avec un taux d'intérêt
Name=EPCR14A_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Crédit(s) a(ont) été accordé(s) avec un taux d'intérêt
Name=EPCR14B
Start=139
Len=1
DataType=Numeric

[ValueSet]
Label=Crédit(s) a(ont) été accordé(s) avec un taux d'intérêt
Name=EPCR14B_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Crédit(s) a(ont) été accordé(s) avec un taux d'intérêt
Name=EPCR14C
Start=140
Len=1
DataType=Numeric

[ValueSet]
Label=Crédit(s) a(ont) été accordé(s) avec un taux d'intérêt
Name=EPCR14C_VS1
Value=1;Oui
Value=2;Non

[Item]
Label=Crédit a (ont) été remboursé(s) au cours des 12 derniers mois
Name=EPCR15A
Start=141
Len=1
DataType=Numeric

[ValueSet]
Label=Crédit a (ont) été remboursé(s) au cours des 12 derniers mois
Name=EPCR15A_VS1
Value=1;Oui, totalement
Value=2;Non, partiellement
Value=3;Non

[Item]
Label=Crédit a (ont) été remboursé(s) au cours des 12 derniers mois
Name=EPCR15B
Start=142
Len=1
DataType=Numeric

[ValueSet]
Label=Crédit a (ont) été remboursé(s) au cours des 12 derniers mois
Name=EPCR15B_VS1
Value=1;Oui, totalement
Value=2;Non, partiellement
Value=3;Non

[Item]
Label=Crédit a (ont) été remboursé(s) au cours des 12 derniers mois
Name=EPCR15C
Start=143
Len=1
DataType=Numeric

[ValueSet]
Label=Crédit a (ont) été remboursé(s) au cours des 12 derniers mois
Name=EPCR15C_VS1
Value=1;Oui, totalement
Value=2;Non, partiellement
Value=3;Non

[Item]
Label=Montant de remboursement mensuel de ce(s) crédit(s) au cours des 12 derniers mois
Name=EPCR16A
Start=144
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de remboursement mensuel de ce(s) crédit(s) au cours des 12 derniers mois
Name=EPCR16B
Start=156
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Montant de remboursement mensuel de ce(s) crédit(s) au cours des 12 derniers mois
Name=EPCR16C
Start=168
Len=12
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre d'échéances déjà remboursées pour crédit 1
Name=EPCR17A
Start=180
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre d'échéances déjà remboursées pour crédit 2
Name=EPCR17B
Start=182
Len=2
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Nombre d'échéances déjà remboursées pour  crédit 3
Name=EPCR17C
Start=184
Len=2
DataType=Numeric
ZeroFill=Yes

[Record]
Label=Other variables
Name=OTHER_VARIABLES
RecordTypeValue='3'
RecordLen=67

[Item]
Label=Other items
Name=BADCLOSED
Start=11
Len=1
DataType=Numeric

[ValueSet]
Label=Other items
Name=BADCLOSED_VS1
Value=0;Bad closed
Value=1;Well closed

[Item]
Label=Start time
Name=START_TIME
Start=12
Len=10
DataType=Numeric

[Item]
Label=End time
Name=END_TIME
Start=22
Len=10
DataType=Numeric

[Item]
Label=Census Longitude
Name=CENSUS_LONGITUDE
Start=32
Len=13
DataType=Numeric
Decimal=6
DecimalChar=Yes

[Item]
Label=Census Latitude
Name=CENSUS_LATITUDE
Start=45
Len=13
DataType=Numeric
Decimal=6
DecimalChar=Yes

[Item]
Label=Distance between census and survey
Name=DISTANCE_CENS_SURVEY
Start=58
Len=10
DataType=Numeric
