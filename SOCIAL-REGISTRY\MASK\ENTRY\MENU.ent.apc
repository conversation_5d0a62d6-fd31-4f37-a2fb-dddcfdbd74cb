﻿PROC GLOBAL
numeric ok, numlang, som, i, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pas, j, cur, err, col, t, g, k, enq, n, x, ligne;
numeric NbOCC;
string AgentCode, accreditation, temp, numHH, OperatorCode, pourTablette, wrkprj, floppy;
array nbHHAssigned(5000);
string ftpServerUrl, slash, password, sdcard1, sdcard, app1, TheCommand;
string goog,wdir,result1,statut, nomCM, idQuest,reportt,color1;
file piffile, tempfile, FileAgentCode;
array V1(6000);
array V2(6000);
array code(5000);
array string label(5000);
array nbMenAssigne(5000);   
array string XV1(2000,9); //we say that we have a maximum of 2000 households assigned to an interviewer in a cluster
list string IdFollow;

string csweb_url = "XX"; //csweb server
string username = "XX";
string pwd = "XX";

alpha(20) result;
alpha(30) FNAMExx;
alpha(3) FSIZExx;

valueset valset;
list string setReg, setDep ;

function OperatorMenu1()
	//Dynamic valuesets for supervisor and interviewer Group of options
	valueset CodesOptions;
	CodesOptions = GOPT_VS1;
	
	if accreditation = "supervisor" then
		//nothing
	ELSEif accreditation = "interviewer" then
		CodesOptions.remove(0); //interviewer cannot assign households
	ELSEif accreditation = "coordo" then
		CodesOptions.remove(0); //
		CodesOptions.remove(1); //
		CodesOptions.remove(5); //
		CodesOptions.remove(6); //
		CodesOptions.remove(7); //
		CodesOptions.remove(8); //
	endif;
	
	setvalueset(GOPT,CodesOptions);

end;

function OperatorMenu()
	//we define the options according to the level of accreditation
	valueset CodesOptions;
	CodesOptions = OPTION_VS1;
	
	//we reduce the list to the codes starting with the digit selected at GOPT
	do i = 1 while i<= length(OPTION_VS1.codes)
		if tonumber(edit("99",OPTION_VS1.codes(i))[1:1]) <> GOPT then
			CodesOptions.remove(OPTION_VS1.codes(i));
		endif;
	enddo;
	
	//we remove the options that do not concern certain stakeholders
	if accreditation = "supervisor" then
		CodesOptions.remove(31);
		CodesOptions.remove(36);
	
	elseif accreditation = "interviewer" then
		CodesOptions.remove(3);
		CodesOptions.remove(30);
		CodesOptions.remove(33);
		CodesOptions.remove(34);
		CodesOptions.remove(36);
	elseif accreditation = "coordo" then
		CodesOptions.clear();
		CodesOptions.add(getlabel(OPTION_VS1,33),33);
		CodesOptions.add(getlabel(OPTION_VS1,36),36);
		CodesOptions.add(getlabel(OPTION_VS1,90),90);
		

	endif;
	setvalueset(OPTION,CodesOptions);
end;


function onchangelanguage()
	//to update the valueset of GOPT if the operator change language
	OperatorMenu1();
end;


function genpff1(string app) //function to assign households
	SetFile( piffile, temp + app + ".pff",create) ;
		Filewrite (piffile, "[Run Information]");
		Filewrite (piffile, "Version=CSPro 7.7");
		Filewrite (piffile, "AppType=Entry");		
		Filewrite (piffile, "[DataEntryInit]");
		Filewrite (piffile, "OperatorID=Anyone");
		Filewrite (piffile, "StartMode=ADD;"+edit("99999",CNULSAMP));
		Filewrite (piffile, "Lock=Modify,Verify");
		Filewrite (piffile, "FullScreen=YES");
		Filewrite (piffile, "NoFileOpen=Yes");	
		Filewrite (piffile, "ShowInApplicationListing=Hidden");							
		Filewrite (piffile, "[Files]");
		Filewrite (piffile, "Application= " + "../ENTRY/" + app + ".pen");
		Filewrite (piffile, "InputData= " + "../REF/" + "sample.dat");
		Filewrite (piffile, "[ExternalFiles]");
		Filewrite (piffile, "FOLLOW_DICT= " + "../REF/" + "follow.dat");
		Filewrite (piffile, "[Parameters]");
		if getos() = 20 then Filewrite (piffile, "OnExit=.\Menu.pff"); endif;
 
 close( piffile );
 end; 

function genpff3(string app) //fonction pour entamer l'interview
	
	SetFile( piffile, temp + app + ".pff",create) ;
		Filewrite (piffile, "[Run Information]");
		Filewrite (piffile, "Version=CSPro 7.7");
		Filewrite (piffile, "AppType=Entry");		
		Filewrite (piffile, "Description = ENCV");		
		Filewrite (piffile, "[DataEntryInit]");
		Filewrite (piffile, "OperatorID=Anyone");
		FileWrite( piffile, "ShowInApplicationListing = hidden" );		
		Filewrite (piffile, "StartMode=ADD;" + edit("99999",CNULSAMP) + numHH);
		Filewrite (piffile, "Lock=ADD,Verify");
		Filewrite (piffile, "AutoAdd=No");
		Filewrite (piffile, "FullScreen=No");
		Filewrite (piffile, "NoFileOpen=Yes");		
		Filewrite (piffile, "[Files]");
		Filewrite (piffile, "Application= " + "../ENTRY/" + app + ".pen");
		Filewrite (piffile, "InputData= " + "../DATA/" + "M" + OperatorCode + edit("99999",CNULSAMP) + ".csdb");
		Filewrite (piffile, "CommonStore=.\MySettings.db");
		Filewrite (piffile, "[ExternalFiles]");
	
	   close(SAMPLE_DICT);
	   setfile(SAMPLE_DICT,"../REF/sample.dat");
	   if !loadcase(SAMPLE_DICT,CNULSAMP) then
			errmsg("Votre fichier d'échantillon n'est pas complet, veuillez télécharger la mise à jour avant de continuer");
			reenter;
	   endif;
		Filewrite (piffile, "[Parameters]");
		Filewrite (piffile, "cluster = " + maketext("%d",CNULSAMP));
		Filewrite (piffile, "HHnumber = " + numHH);
		Filewrite (piffile, "interviewer = " + OperatorCode);
		Filewrite (piffile, "LGA = " + maketext("%d",XLGA));
		Filewrite (piffile, "DISTRICT = " + maketext("%d",XDISTRICT));
		
		for i in SAMPLE2 where XNUMBER = tonumber(numHH) do
			Filewrite (piffile, "WARD = " + maketext("%d",XWARD));
//SARR			Filewrite (piffile, "SETTLEMENT = " + XSETTLEMENT);
//SARR			Filewrite (piffile, "CensLat = " + maketext("%d",XLATITUDE*1000000));
//SARR			Filewrite (piffile, "CensLon = " + maketext("%d",XLONGITUDE*1000000));
		endfor;
		if getos() = 20 then Filewrite (piffile, "OnExit=.\Menu.pff"); endif;
	   close(SAMPLE_DICT);
	close( piffile );
 end;
 
  { Runs a .PFF for an application }
function runpff( string app);
    { Using single quotes instead of double quotes as double quotes are needed in the string generated }

    if getos() in 20:29 then 
    	execPFF (temp + app + ".pff",stop); //sous android on ne peut pas avoir 2 applications .pen ouvertes simultanément, c'est pourquoi il faut stopper la 1ère   	
    else 
    	execPFF (temp + app + ".pff"); 
    endif;
end;



function string couleur(xxx)
	if xxx = 0 then couleur = "red" else couleur = "black"; endif;
end;


function ResultHH()
	//this function is used to display the household interview result in the OP_select function
	alpha(9) idHH = maketext("%05d%04d",FCLUSTER,FNUMBER);
	FNAMExx = FNAME; FSIZExx = ""; //FSIZExx = edit("ZZ9",FSIZE); if FSIZE >=95 then FSIZExx = "DK"; endif;
	if locate(SOCIAL_DICT,=,idHH) and retrieve(SOCIAL_DICT) then
		//We retrieve the result in the data file
		if A15 = notappl then
			result1 = ">> In progress";
		elseif BADCLOSED = 0 then
			result1 = "☻ Bad closed";
		elseif A15 = 1 then
			result1 = "✔ Completed";		
		elseif A15 in 2 then
			result1 = "☻ No HH member";
		elseif A15 = 3 then
			result1 = "☻ HH absent";		
		elseif A15 = 4 then
			result1 = "☻ Refused";		
		elseif A15 = 5 then
			result1 = "☻ Postponed";		
		elseif A15 = 6 then
			result1 = "☻ DWELLING VACANT";		
		elseif A15 = 7 then
			result1 = "☻ DWELLING Destroyed";		
		elseif A15 = 8 then
			result1 = "☻ DWELLING not found";		
		elseif A15 = 96 then
			result1 = "☻ Other";
		endif;
		
		//We update the information of the map from that of the household file
		if HHSIZE <> Notappl then	FSIZExx = edit("ZZ9",HHSIZE); endif;
		if strip(A14) <> "" then FNAMExx = A14 endif;
	else
		result1 = tr("Ø Not started");
	endif;
	// ✔ ☺ ☻ Ø  ±
end;

function ListAssignment (string x1,x2,string x3, x4, string x5,x6,x7,string x8);
 //this function will allow to load a matrix which will be displayed for the selection of the household
	inc(ligne);
	XV1(ligne,1) = x1;
	XV1(ligne,2) = maketext("%d",x2);
	XV1(ligne,3) = x3;
	XV1(ligne,4) = maketext("%d",x4);
	XV1(ligne,5) = x5;
	XV1(ligne,6) = maketext("%f",x6); 
	XV1(ligne,7) = maketext("%f",x7);
	XV1(ligne,8) = x8;	
end;
 //  selection of the household to be interviewed
function op_select();
	//We associate the files with the dictionaries that will be used
	ligne = 0;
	CLOSE(FOLLOW_DICT);
	Filedelete("../REF/follow.dat.csidx");
	setfile(FOLLOW_DICT,"../REF/follow.dat");
	close(SOCIAL_DICT);
	filecopy(maketext("../DATA/M%s%05d.csdb",OperatorCode,Cnulsamp),maketext("../WORK/M%s%05d.csdb",OperatorCode,Cnulsamp));
	CLOSE(SOCIAL_DICT);
	setfile(SOCIAL_DICT,maketext("../WORK/M%s%05d.csdb",OperatorCode,Cnulsamp),append);
	
	if countcases(FOLLOW_DICT where FCLUSTER=Cnulsamp and FINTCODE = userCode) = 0 then
		errmsg("Aucun ménage affecté à %s dans le DR %05d", getlabel(userCode,userCode),CNULSAMP );      
		reenter;
	
	else //at least 1 household has been assigned to this agent in the cluster
		XV1.clear();  
		IdFollow.clear();

		//We take the households from the assignment file
		forcase FOLLOW_DICT where FCLUSTER=Cnulsamp and FINTCODE = tonumber(OperatorCode) do
			ResultHH(); 
			ListAssignment(result1,FNUMBER,strip(FNAMExx) + maketext(" (ENCV-2025-%05d. %s)",CNULSAMP,strip(FSTRUCNAME)),FPHONE,FSIZExx,FLONGITUDE,FLATITUDE,FLODGING);
			IdFollow.add(maketext("%05d%04d",FCLUSTER,FNUMBER)); 
		endfor;
	endif;
	
	ok = 2;
	do while ok = 2
		k = showarray(maketext(TR("Pour démarrer ou modifier un questionnaire, veuillez sélectionner un ménage dans l'EA : %05d"), CNULSAMP),
				XV1,
				title("RESULTAT", "N°SEQ", "HH NOM", "PHONE", "TAILLE","LONGITUDE","LATITUDE", "LOCATION") );
		if k = 0 then 
			reenter;
		else 
			ok = accept(maketext("Voici ce que vous avez sélectionné: Ménage n° %s, Chef de ménage = %s, voulez-vous continuer ?",strip(XV1(k,2)),strip(XV1(k,3))),"Yes","No, Correct my choice","No, Cancel","Display on map");
			if ok = 0 then
				reenter;
			elseif ok = 1 then
				alpha(9) idHH = IdFollow(k); 
				if locate (FOLLOW_DICT,=,idHH) and retrieve(FOLLOW_DICT) then
					numHH = edit("9999",FNUMBER);
				endif;
			elseif ok = 3 then //Cancel
				reenter;
			elseif ok = 4 then //Display on the map
				if tonumber(strip(XV1(k,6))) > 0 or tonumber(strip(XV1(k,6))) < 0 then //we test if there are GPS coordinates
					// execsystem(maketext("gps:%s,%s,%s",strip(XV1(k,8)), strip(XV1(k,7)), strip(XV1(k,3)) ));
					execsystem(maketext("gps:%s,%s",strip(XV1(k,7)), strip(XV1(k,6)) ));
					reenter;
				else //if there are not GPS coordinates
					errmsg("Aucune coordonnée GPS disponible dans la liste des ménages");
					reenter;
				endif;
			endif;
		endif;	
	enddo;
 end; 


function  ClientBluetooth(string Type, string FolderOrFile1, string FolderOrFile2, string message); 
	//this function allows the transfer by bluetooth. Type takes the values get or put, FolderOrFile1 is depending on whether type is get or put, source or destination folder
	if syncconnect(Bluetooth) then		
		if tolower(type) = "put" then syncfile(put,FolderOrFile1,FolderOrFile2); endif;
		if tolower(type) = "get" then syncfile(get,FolderOrFile1,FolderOrFile2); endif;
		syncdisconnect();
		if message <> "" then errmsg(message); endif;
	else
		errmsg("Erreur dans le processus, veuillez réessayer!!!");
	endif;
end;

function  ClientDropBox(string Type, string FolderOrFile1, string FolderOrFile2, string message); 
	//this function allows you to transfer via Dropbox. Type takes the values get or put, FolderOrFile1 is depending on whether type is get or put, source or destination folder
	if syncconnect(Dropbox) then		
		if tolower(type) = "put" then syncfile(put,FolderOrFile1,FolderOrFile2); endif;
		if tolower(type) = "get" then syncfile(get,FolderOrFile1,FolderOrFile2); endif;
		syncdisconnect();
		if message <> "" then errmsg(message); endif;
	else
		errmsg("Erreur dans le processus, veuillez réessayer!!!");
	endif;
end;


function receive_assign()
	close(FOLLOW_DICT);
	close(SAMPLE_DICT);
	filedelete("../REF/follow.dat.csidx");
	filedelete("../REF/sample.dat.csidx");
	syncserver(Bluetooth);
end;

function transfer_assign()
	close(FOLLOW_DICT);
	close(SAMPLE_DICT);
	//Assignments are transfered thru bluetooth
	//ClientBluetooth("Put", "../REF/follow.dat", "../REF/", TR("Assignments successfully transferred to interviewer"));
	if syncconnect(Bluetooth) then		
		syncfile(put,"../REF/follow.dat","../REF/"); 
		syncfile(put,"../REF/sample.dat","../REF/"); 
		syncdisconnect();
		errmsg("Les missions ont été transférées avec succès à l'intervieweur"); 
	else
		errmsg("Erreur dans le processus, veuillez réessayer!!!");
	endif;
	
end;


function transfer_data()
	//the investigator transfers his entire DATA file previously zipped by bluetooth to the controller
	//the supervisor transfers its entire DATA folder by dropbox
	
	numeric typeTransf = accept("Que fais-tu ? ?",
								"1 - Envoyer des données uniquement",
								"2 - Envoyer des données et des images d'EA " + edit("99999",CNULSAMP),
								"3 - Envoyer des données et des images de tous les clusters");
	if typeTransf = 0 then
		reenter
	endif;
	
	CLOSE(FOLLOW_DICT); close(SAMPLE_DICT);
	
	if accreditation = "interviewer" then //transfer thru bluetooth
		//we first zip its DATA folder and copy it into the WORK folder 
		compress("../WORK/DATA.zip","../DATA/*.csdb");
		filedelete("../WORK/DOCUMENTS.zip");
		if typeTransf = 2 then
			compress("../WORK/DOCUMENTS.zip","../DOCUMENTS/?" + edit("99999",CNULSAMP) + "*.png"); //We have documents starting by D and B
		elseif typeTransf = 3 then
			compress("../WORK/DOCUMENTS.zip","../DOCUMENTS/*.png");
		endif;
		
		syncserver(Bluetooth);
		
	elseif accreditation = "supervisor" then //transfer on Dropbox server
		compress("../WORK/DATA"+OperatorCode+".zip","../DATA/*.csdb");
		filecopy("../REF/follow.dat","../REF/follow_" + OperatorCode +".dat");
		filecopy("../REF/sample.dat","../REF/sample_" + OperatorCode +".dat");
		list string listFiles = "../REF/follow_" + OperatorCode +".dat",
								"../REF/sample_" + OperatorCode +".dat";
		compress("../WORK/REF"+OperatorCode+".zip",listFiles);
		
		//for documents we want to make the list of clusters with pictures
		list string listD,ListClust;
		dirlist(listD,"../DOCUMENTS",filter := "*.png");
		
		do i = 1 while i<= listD.length()
			string clust = listD(i)[length(listD(i))-13:5]; //DXXXXXYYYZZ.png
			
			IF (typeTransf = 2 and tonumber(clust) = CNULSAMP) or (typeTransf = 3) then
				IF ListClust.SEEK(clust) = 0 then
					ListClust.add(clust);
				endif;
			endif;
		enddo;
		
		do i= 1 while i<= ListClust.length()
			compress("../WORK/DOCUMENTS" + OperatorCode + ListClust(i) + ".zip","../DOCUMENTS/?" + ListClust(i) +"*.png");
		enddo;
		
		// if syncconnect(Dropbox) then
		if syncconnect(Csweb,csweb_url,username,pwd) then
			syncfile(put,"../WORK/DATA"+OperatorCode+".zip","SOCIAL/DATA/"); //transfer data
			syncfile(put,"../WORK/REF"+OperatorCode+".zip","SOCIAL/REF/"); //transfer Assignments
			do i= 1 while i<= ListClust.length()
				syncfile(put,"../WORK/DOCUMENTS"+OperatorCode + ListClust(i) + ".zip","SOCIAL/DOCUMENTS/"); //transfer documents pictures
			enddo;

			syncfile(GET,"SOCIAL/ENTRY/entry*.zip","../ENTRY/"); //Receive update
			syncfile(GET,"SOCIAL/REF/sample*.zip","../WORK/"); //Receive update with the new added or reassigned clusters, we put the new clusters in WORK folder to merge with the previous sample that is in REF folder
					
				decompress("../ENTRY/entry.zip","../ENTRY/"); 
				filedelete("../ENTRY/entry.zip");

				IF fileexist("../WORK/sample.zip") then
					decompress("../WORK/sample.zip","../WORK/");
					IF !fileexist("../REF1/sample.dat") or diagnostics("md5","../REF1/sample.dat") <> diagnostics("md5","../WORK/sample.dat") THEN
						Filecopy("../WORK/sample.dat","../REF1/sample.dat");
						fileconcat(SAMPLE_DICT,"../REF/sample.dat","../REF1/sample.dat","../REF/sample.dat"); //This order of concatening will fix two things : add news clusters and add reassigned clusters, if you change order of concatenation, it will not add reassigned clusters
					ENDIF;
					filedelete("../WORK/sample.zip");
					filedelete("../WORK/sample.dat*");//* to delete also .csidx
				endif;
				

			syncdisconnect();
	    	errmsg("Transfert de données terminé avec succès!!!"); 	    	
		else
			errmsg("Erreur dans le processus, veuillez reprendre!!!");
		endif;		
	endif;

end;

function receive_data()
	
	filedelete("../WORK/DATA.zip");
	filedelete("../WORK/AgentCode.dat");
	close(SOCIAL_DICT);
	if syncconnect(Bluetooth) then	
		//Before receive data, the program check that the interviewer who is sending data is in the same team than the supervisor
		syncfile(Get,"../REF/AgentCode.dat","../WORK/AgentCode.dat");
		setfile(tempfile, "../WORK/AgentCode.dat");
		string text;
		fileread(tempfile,text);
		close(tempfile);
		
		if text[1:2] + "0" = operatorCode then
			syncfile(Get,"../WORK/DATA.zip","../WORK/");
			syncfile(Get,"../WORK/DOCUMENTS*.zip","../WORK/");
			decompress("../WORK/DATA.zip","../DATA/");
			decompress("../WORK/DOCUMENTS.zip","../DOCUMENTS/");
			syncdisconnect();
			errmsg("Données copiées avec succès");
		else
			errmsg("Vous êtes connecté à l'intervieweur %s, qui ne fait pas partie de votre équipe. Les données ne seront pas transférées.",text);			
			syncdisconnect();
		endif;
			
	else
		errmsg("Erreur dans le processus, veuillez réessayer!!!");
	endif;	
		
end;  


function receive_data_coordo()
	

	// if syncconnect(dropbox) then
	if syncconnect(Csweb,csweb_url,username,pwd) then

		syncfile(GET,"SOCIAL/DATA/DATA*.zip","../DATA/");
		syncfile(GET,"SOCIAL/REF/REF*.zip","../REF/");
		syncfile(GET,"SOCIAL/DOCUMENTS/DOCUMENTS*.zip","../DOCUMENTS/");
		
		list string listFiles;
		//unzip downloaded .zip
		dirlist(listFiles,"../DATA","*.zip");
		do i = 1 while i<= listFiles.length()
			decompress(listFiles(i));
			filedelete(listFiles(i));
		enddo;
		
		dirlist(listFiles,"../REF","*.zip");
		do i = 1 while i<= listFiles.length()
			decompress(listFiles(i));
			filedelete(listFiles(i));
		enddo;
		
		dirlist(listFiles,"../DOCUMENTS","*.zip");
		do i = 1 while i<= listFiles.length()
			decompress(listFiles(i));
			filedelete(listFiles(i));
		enddo;
		
	else
		errmsg(tr("Erreur dans le processus, veuillez réessayer !!!"));
	endif;		
		
end;

function receive_update()
//You can receive the update either by bluetooth or on the server

close(SAMPLE_DICT);
filedelete("../REF/sample.dat.csidx");

i = accept("Comment souhaitez-vous recevoir la mise à jour?","1-Du serveur","2-Via Bluetooth");

	if i = 0 then //nothing has been selected
		reenter;
	elseif i = 2 then //superior bluetooth reception
		syncserver(Bluetooth);
	elseif i = 1 then
		// if syncconnect(Dropbox) then
		if syncconnect(Csweb,csweb_url,username,pwd) then
			syncfile(GET,"SOCIAL/ENTRY/entry*.zip","../ENTRY/"); //Receive update
			syncfile(GET,"SOCIAL/REF/sample*.zip","../WORK/"); //Receive update with the new added or reassigned clusters, we put the new clusters in WORK folder to merge with the previous sample that is in REF folder
			//syncfile(GET,"SOCIAL/REF/sampleinfo*.zip","../REF/");
			
				decompress("../ENTRY/entry.zip","../ENTRY/"); 
				decompress("../WORK/sampleinfo.zip","../REF/"); 
				filedelete("../ENTRY/entry.zip");
				filedelete("../WORK/sampleinfo.zip");
				
				IF fileexist("../WORK/sample.zip") then
					decompress("../WORK/sample.zip","../WORK/");
					IF !fileexist("../REF1/sample.dat") or diagnostics("md5","../REF1/sample.dat") <> diagnostics("md5","../WORK/sample.dat") THEN
						Filecopy("../WORK/sample.dat","../REF1/sample.dat"); //in fOLDER REF1 we have the last version of additive sample
						fileconcat(SAMPLE_DICT,"../REF/sample.dat","../REF1/sample.dat","../REF/sample.dat"); //This order of concatening will fix two things : add news clusters and add reassigned clusters, if you change order of concatenation, it will not add reassigned clusters
					ENDIF;
					filedelete("../WORK/sample.zip");
					filedelete("../WORK/sample.dat*");//* to delete also .csidx
				endif;
				
			syncdisconnect();
	    	errmsg("Mise à jour reçue avec succès!!!"); 	    	
		endif;		
	endif;
	
end;

function transfer_update()
//the transfer of the update is done by the supervisor thru bluetooth to the interviewer
	if accreditation = "supervisor" then

		close(SAMPLE_DICT);
		filedelete("../REF/sample.dat.csidx");
		
		if syncconnect(Bluetooth) then		
			syncfile(put,"../ENTRY/*.pen","../ENTRY/"); 
			syncfile(put,"../REF/sample.dat","../REF/"); 
			syncdisconnect();
			errmsg("Mise à jour transférée avec succès"); 
		else
			errmsg("Erreur dans le processus, veuillez réessayer!!!");
		endif;

	endif;
end;


function usb()
	if getos() in 10:19 then //we are trying to automatically detect the USB key inserted on the machine
	    som = 0;
		string CleUSB = "GHIJKLMNOP";
		do varying i = 1 while i <= length(CleUSB) 
			if direxist(concat(CleUSB[i:1], ":/")) then 
				floppy = concat(CleUSB[i:1],":");
				som = som + 1;
			endif;
		enddo;
		if Som = 0  then errmsg("Vous n'avez pas inséré de clé USB/carte SD, veuillez en insérer une pour le transfert");  
			reenter;
		endif;  
		if som >= 2 then errmsg("Vous avez inséré plusieurs clés USB/cartes SD ou votre disque est partitionné"); 
			i = accept("Veuillez sélectionner le nom de USB/FLASH DRIVE","D:","E:","F:","G:","H:","I:","J:","K:","L:","M:","N:","O:","P:");
			
			if i > 0 then floppy = CleUSB[i:1] + ":"; else reenter; endif;
		endif;		
	elseif getos() in 20:29 then //we are trying to detect the sdcard in the tablet
	    if !direxist(sdcard) then errmsg("Yvous n'avez pas inséré de carte SD, veuillez en insérer une pour l'opération");
	    else 
	    	som = 1 ; //i.e we found sdcard
	    	if pourTablette = "sdcard" then floppy = sdcard; endif;
	    	if pourTablette = "sdcard1" then floppy = sdcard1; endif;  	
	    endif; 
 	endif;
end; 

function Backup()

//We first save in the BACKUP of the machine
  string repertoire = strip(wrkprj);
	
	if accreditation = "supervisor" then ; //backup on the tablet only for the supervisors
		string destination =  concat(strip(repertoire), strip(slash), "BACKUP", strip(slash),OperatorCode + " Day_" + edit("99999999", sysdate("DDMMYYYY"))+ " Hour_" + edit("999999",systime()));

		dircreate(destination); //create the backup directory on the machine
		// dircreate(destination + slash + "DATA");
		// dircreate(destination + slash + "REF");
		
		// OK = FileCopy( concat(strip(repertoire),strip(slash),"DATA",strip(slash),"*"), strip(destination + slash + "DATA"));
		// OK = OK + FileCopy( concat(strip(repertoire),strip(slash),"REF",strip(slash),"*"), strip(destination + slash + "REF"));
		OK = compress(destination + slash + "DATA.zip","../DATA/*.csdb");
		OK = OK + compress(destination + slash + "REF.zip","../REF/*");

		if ok then errmsg("Sauvegarde locale terminée avec succès"); endif;
	endif;
 	
//then we make a backup on an external device
	pourTablette = "sdcard1";
	usb();
	if som >= 1 then
		if !DirExist(concat(strip(floppy), strip(slash), "BACKUP")) then DirCreate(concat(strip(floppy), strip(slash), "BACKUP")); endif;
		string destination2 =  concat(strip(floppy), strip(slash), "BACKUP", strip(slash),OperatorCode + " Day_" + edit("99999999", sysdate("DDMMYYYY"))+ " Hour_" + edit("999999",systime()));
		dircreate(destination2); //we create the backup directory on the USB key
		// dircreate(destination2 + slash + "DATA");
		// dircreate(destination2 + slash + "REF");	
		// OK = FileCopy( concat(strip(repertoire),strip(slash),"DATA",strip(slash),"*"), strip(destination2 + slash + "DATA"));
		// OK = OK + FileCopy( concat(strip(repertoire),strip(slash),"REF",strip(slash),"*"), strip(destination2 + slash + "REF"));

		OK = compress(destination2 + slash + "DATA.zip","../DATA/*.csdb");
		OK = OK + compress(destination2 + slash + "REF.zip","../REF/*");
		// OK = OK + FileCopy( concat(strip(repertoire),strip(slash),"REF",strip(slash),"*"), strip(destination2 + slash + "REF"));

	  	if ok and som >=1 then errmsg("Sauvegarde terminée avec succès sur le USB key/SdCard"); endif;
	endif;

	  	if !som then errmsg("La sauvegarde n'a pas été effectuée sur un USB key/SdCard"); endif;
	 	
end;

 function SummaryOfAssignment()
 	string reportFilename = "..\REPORTS\Assignments.html"; 	
	setfile(tempFile, reportFilename, create);
	// Standard HTML headers
	filewrite(tempFile, "<!DOCTYPE html>");
	filewrite(tempFile, "<html><head>");
	
	// Add CSS to make tables look nice
	filewrite(tempFile, "<style type='text/css'>");
	filewrite(tempFile, "table, th, td {border: 1px solid black;border-collapse: collapse;padding: 8px}");	
	filewrite(tempFile, "</style>");
	filewrite(tempFile, "<title>Resume of Assignments </title>");
	filewrite(tempFile, "</head>");
	filewrite(tempFile, "<body>");
	// Id-items
	filewrite(tempFile, "<h2><font color = blue>Resume of Assignments</font></h2>");

    nbHHAssigned.clear();
	
    //we go through lines 1 to 1 and load the recap table of assignments
	close(SAMPLE_DICT);
	setfile(SAMPLE_DICT,"../REF/sample.dat"); 
	loadcase(SAMPLE_DICT,CNULSAMP);
	

	
    do i=1 while XNUMBER(i) > 0
		nbHHAssigned(XINTCODE(i)) = nbHHAssigned(XINTCODE(i)) + 1;
    enddo;
    
    //we write the result in html
    filewrite(tempFile, "<p>%s</p>",getlabel(CNULSAMP,CNULSAMP));
	filewrite(tempFile, "<p>Number of households in the cluster : %d</p>",i-1);
	filewrite(tempFile, "<p><font color = blue>Table 1 : Summary of Assignments by interviewer</font></p>");
	filewrite(tempFile, "<table>");
	filewrite(tempFile, "<tr><th>Interviewer code</th><th>Intervierwer name</th><th>Number of households assigned</th><th>Estimated individuals</th></tr>");
	// filewrite(tempFile, "<tr><th>Interviewer code</th><th>Intervierwer name</th><th>Number of households assigned</th></tr>");
	som = 0;

	
	valset.clear();
	valset.add("Pas encore affecté",0);
	do i = 0 while i<= 9 //in case of a maximum of 9 interviewers per team
		if getlabel(USERCODE,USERCODE+i) <> "" then
			valset.add(getlabel(USERCODE,USERCODE+i),USERCODE+i);
		endif;
	enddo;
	
	do k= 1 while k <= length(valset.codes);
			x= valset.codes(k);
			filewrite(tempFile, "<tr>");
			filewrite(tempFile, "<td>%03d</td>", x);
			filewrite(tempFile, "<td>%s</td>", getlabel(valset, x));
			filewrite(tempFile, "<td>%d</td>", nbHHAssigned(x));
	//SARR		filewrite(tempFile, "<td>%d</td>", sum(XSIZE where XSIZE<95 AND XINTCODE = x));
			filewrite(tempFile, "</tr>");
			som = som + (k>1)*nbHHAssigned(x); //this allows to have the total number of households assigned. K = 1 correspond of non assigned households
	enddo;	 	
 
 	filewrite(tempFile, "</table>");

    	filewrite(tempFile, "<p><font color = blue> Table 2 : Detail of Assignments</font></p>");
		filewrite(tempFile, "<table>");
		filewrite(tempFile, "<tr><th>Line</th><th>HH name</th><th>HH SIZE</th><th>COMPOUND NAME</th><th>COMPOUND PHONE</th><th>HH Phone</th><th>Settlement</th><th>Address</th><th>Others numbers</th><th>Interviewer</th></tr>");
		do n= 1 while XNUMBER(n) >= 0
			
			filewrite(tempFile, "<tr>");
			filewrite(tempFile, "<td>%d</td>", n);
			filewrite(tempFile, "<td>%s</td>", strip(XNAME(n)));
//SARR			filewrite(tempFile, "<td>%d</td>", visualvalue(XSIZE(n)));
//SARR			filewrite(tempFile, "<td>%s</td>", XSTRUCNAME(n));
//SARR			filewrite(tempFile, "<td>%s</td>", strip(XCOMP_OWNER_CONTACT(n)));
//SARR			filewrite(tempFile, "<td>%d</td>", visualvalue(XPHONE(n)));
//SARR			filewrite(tempFile, "<td>%s</td>", strip(XSETTLEMENT(n)));
//SARR			filewrite(tempFile, "<td>%s</td>", strip(XLODGING(n)));
//SARR			filewrite(tempFile, "<td>%s</td>", strip(XOTHER_NUMBERS(n)));
			
			string color2;
			if XINTCODE(n) = 0 then color2 = "red" else color2 = "black" endif;
			
			filewrite(tempFile, "<td><font color = %s>%s</font></td>",color2, getlabel(valset, XINTCODE(n)));
			filewrite(tempFile, "</tr>");			       
   		 enddo;    	
 		filewrite(tempFile, "</table>");    

	filewrite(tempFile, "</body>");
	filewrite(tempFile, "</html>");
	close(tempFile);
	if getos() in 20:29 then
		// Android - use "browse:"
		execsystem(maketext("view:%s", reportFilename));
	else
		// Windows - use "explorer.exe <filename>"
		execsystem(maketext("%sexplorer.exe %s", 
						    pathname(Windows), 
						    reportFilename));
	endif;
   
   end;

function MergeFiles()
	//this function will be a sub-function of the functions DetailedResult, WorkProgress
	//It allows you to create work files, merged as needed
	
	close(SOCIAL_DICT);close(FOLLOW_DICT);close(SAMPLE_DICT);
	STRING prefixe;
	
	if accreditation = "supervisor" then
		prefixe = OperatorCode[1:2]
	elseif  accreditation = "interviewer" then
		prefixe = OperatorCode
	endif;
	fileconcat(SOCIAL_DICT,"../WORK/MALL.csdb","../DATA/M" + prefixe + "*.csdb"); 
	
	Setfile(SOCIAL_DICT,"../WORK/MALL.csdb",append);
	setfile(FOLLOW_DICT,"../REF/follow.dat",append);
	setfile(SAMPLE_DICT,"../REF/sample.dat");

end;


function DisplayOnMap()
	//this function will display the points on the map, maps.me for android and google earth for windows
	Setfile(tempfile, wdir + slash + "KML" + edit("99999",CNULSAMP)+ ".kml");
	
	filewrite(tempfile,'<?xml version="1.0" encoding="UTF-8"?>');
	filewrite(tempfile,'<kml xmlns="http://earth.google.com/kml/2.0">');
	filewrite(tempfile,'<Document>');
	filewrite(tempfile,'<name>EA %05d </name>',cnulsamp);

	setfile(SAMPLE_DICT,"../REF/sample.dat");
	loadcase(SAMPLE_DICT,cnulsamp); //we load the sample households of the current cluster

	do i = 1 while !special(XNUMBER(i)) //we count all the lines of the sample file

		filewrite(tempfile,'<Placemark>');	
			if getos() in 20:29 then //on android
				filewrite(tempfile,"<name>NAME : %s. Num %d</name>",strip(XNAME(i)),i);
				filewrite(tempfile,'<description>HH</description>');
				filewrite(tempfile,'<styleUrl>#placemark-red</styleUrl>');

			else //on windows
				filewrite(tempfile,"<name></name>");
				filewrite(tempfile,'<description>NAME : %s. Num %d</description>',strip(XNAME(i)),i);
				//we will color the cluster points in green and the sampled points in red
				string couleur2 = "FF0000FF"; //else couleur2 = "FF00FF00"; endif;
				filewrite(tempfile,'<Style><IconStyle><scale>0.6</scale><Icon><href>http://maps.google.com/mapfiles/kml/shapes/shaded_dot.png</href></Icon><color>%s</color></IconStyle></Style>',couleur2);
			endif;
			filewrite(tempfile,'<Point>');
//SARR			filewrite(tempfile,"<coordinates>%5.6f, %5.6f </coordinates></Point>",XLONGITUDE(i),XLATITUDE(i));
		filewrite(tempfile,'</Placemark>');

	enddo;

	filewrite(tempfile,'</Document>');
	filewrite(tempfile,'</kml>');	
	close(tempfile);
	
	if getos() in 20:29 then
		execsystem("view:" + filename(tempfile));
	elseif getos() in 10:19 then
		TheCommand = concat( '"', strip(goog), '\googleearth.exe" ','"',wdir + slash + "KML" + edit("99999",CNULSAMP)+ ".kml",'"' );
		ExecSystem( strip(TheCommand),nowait, maximized, focus );
	endif;
	
end;


function DisplayOnMap2()
	//This function will be used to display collected GPS during SR survey

	MergeFiles();
	MAP mymap;
	numeric id_marker;
	mymap.clearmarkers();
	
	forcase SOCIAL_DICT where A10GPS = 1 and !special(a10a) and A08 = CNULSAMP do
		id_marker = mymap.addmarker(a10b,a10a);
		mymap.setmarkerdescription(id_marker,maketext("EA=%05d, HH=%03d, int = %03d",A08,a10,a02));
	endfor;	
	mymap.show();
end;

function export_data()
	close(SOCIAL_DICT);close(FOLLOW_DICT);close(SAMPLE_DICT);
	filedelete("../WORK/MALL.csdb");
	setfile(SOCIAL_DICT,"../WORK/MALL1.csdb",create);
	fileconcat(SOCIAL_DICT,"../WORK/MALL.csdb","../WORK/MALL1.csdb","../DATA/M*.csdb"); 
	execpff("../EXPORT/EXPORT.pff",wait);
	filedelete("../EXPORT/HOUSEHOLD1.do"); filedelete("../EXPORT/INDIVIDUAL1.do"); 
	// errmsg("Open the folder %s and double click on file MASTER.sps and run it.",pathname(application)[1:length(pathname(application))-6] + "SPSS");
	// errmsg("In the MASTER.sps, update the line cd depending of your path");
	errmsg("Ouvrez le dossier %s et exécutez les 2 fichiers Household.do et Individual.do",pathname(application)[1:length(pathname(application))-6] + "EXPORT");
	// VIEW("../SPSS/MASTER.sps");
	// VIEW("../EXPORT/HOUSEHOLD1.DO");
	// VIEW("../EXPORT/INDIVIDUAL1.DO");
end;

function DetailedResult()

	//Cette fonction permet de donner le code résultat de chaque questionnaire entamé 
	//errmsg("In progress");

	MergeFiles();
	
 	string reportFilename = maketext("%sDetailedResult.html", "..\REPORTS\");

	setfile(tempFile, reportFilename,create);

	// Standard HTML headers
	filewrite(tempFile, "<!DOCTYPE html>");
	filewrite(tempFile, "<html><head>");
	
	// Add CSS to make tables look nice
	filewrite(tempFile, "<style type='text/css'>");
	filewrite(tempFile, "table, th, td {border: 1px solid black;border-collapse: collapse;padding: 8px}");	
	filewrite(tempFile, "</style>");
	filewrite(tempFile, "<title>Detailed results</title>");
	filewrite(tempFile, "</head>");
	filewrite(tempFile, "<body>");
	filewrite(tempFile, "<h2><font color = BLUE >RESULTS OF EACH QUESTIONNAIRE OF %s</font></h2>",getvaluelabel(CNULSAMP));


//Table 	
	
	if countcases(SOCIAL_DICT) > 0 then // we check that there are household data
		
		filewrite(tempFile, "<table>");
		filewrite(tempFile, "<tr><th>Line</th><th>EA</th><th>HH number</th><th>HH head</th><th>HH size</th><th>Result</th><th>Status</th><th>Interviewer</th><th>Supervisor</th></tr>");
		
		numeric line = 0;
		forcase SOCIAL_DICT WHERE A08 = CNULSAMP do 
			inc(line);
			color1 = "red";
			result1 = getvaluelabel(A15);
			
			if A15 = 1 and BADCLOSED = 1 then
				color1 = "black";
			elseif A15 in notappl then
				result1 = "in progress";
			endif;
			
			filewrite(tempFile, "<tr style = 'color:%s'>", color1);
			filewrite(tempFile, "<td>%d</td>",LINE);
			filewrite(tempFile, "<td>%04d</td>",A08);
			filewrite(tempFile, "<td>%04d</td>",A10);
			filewrite(tempFile, "<td>%s</td>",strip(A14));
			filewrite(tempFile, "<td>%d</td>",HHSize);
			filewrite(tempFile, "<td>%s</td>",result1);
			filewrite(tempFile, "<td>%s</td>",getvaluelabel(BADCLOSED));
			filewrite(tempFile, "<td>%03d %s</td>",A02,getlabel(userCode_VS1,A02));
			filewrite(tempFile, "<td>%03d %s</td>",A03,getlabel(userCode_VS1,A03));
			filewrite(tempFile, "</tr>");
		endfor;
		filewrite(tempFile, "</table>");
	endif;

	filewrite(tempFile, "</body></html>");
	close(tempFile); 
	
 	if getos() in 20:29 then
		// Android - use "browse:"
	// string	reportFilename = fileconcat(strip(report),strip(slash),"error.HTML",strip(report),strip(slash),);
		execsystem(maketext("view:%s", reportFilename));
	else
		// Windows - use "explorer.exe <filename>"
		execsystem(maketext("%sexplorer.exe %s", 
						    pathname(Windows), 
						    reportFilename));
	endif;		
end;

 

function WorkProgress()
	//This function is used to give the statistics on the data files

	MergeFiles(); 
	
	
	//Calculate stats

	hashmap statsHH(numeric,numeric) default(0) ; //(ea, result). result = -1 for assignment, 
	hashmap statsInd(numeric,numeric) default(0) ; //(ea, result). result = -1 for expected, 
	
	//stats about expected households. 
		//for the supervisor the expected household are in the sample.dat file
		//for the interviewer the expected household are in the follow.dat file
	list listOfClusters; 
	
	if accreditation = "supervisor" then
		forcase SAMPLE_DICT where XSUP = usercode do
			statsHH(XCLUSTER,-1) = count(XNUMBER where XNUMBER>0); //-1 is for assignment
			inc(statsHH(0,-1),count(XNUMBER where XNUMBER>0)); //0 is for the total
			listOfClusters.add(XCLUSTER); 
		endfor;
	elseif accreditation = "interviewer" then
		forcase FOLLOW_DICT where FINTCODE = usercode do
			inc(statsHH(FCLUSTER,-1)); //-1 is for assignment
			inc(statsHH(0,-1)); //0 is for the total
			listOfClusters.add(FCLUSTER); 
		endfor;
	endif;

	//Stats about result of hh survey
	forcase SOCIAL_DICT do
		numeric res;
		if A15 = 96 then
			res = 10
		elseif A15 = notappl then
			res = 9
		else 
			res = A15
		endif;
		numeric HH1 = A08;
		numeric XGPS = A10GPS;
		inc(statsHH(HH1,res)); inc(statsHH(0,res)); inc(statsHH(HH1,0)); inc(statsHH(0,0)); //0 is for the total
		inc(statsHH(HH1,20),XGPS in 3,notappl); inc(statsHH(0,20),XGPS in 3,notappl); //20 is the position where we want save the number of HH without GPS coordinates. You can choose another value in place of 20
		inc(statsHH(HH1,21),BADCLOSED=0); inc(statsHH(0,21),BADCLOSED=0); 
		//WE TRY to match the result of individual with result of hh
	endfor;
	
	//write all the calculated statistics in an html file
 	string reportFilename = maketext("%sWorkProgress.html", "..\REPORTS\");

	setfile(tempFile, reportFilename,create);
	
	// Standard HTML headers
	filewrite(tempFile, "<!DOCTYPE html>");
	filewrite(tempFile, "<html><head>");
	
	// Add CSS to make tables look nice
	filewrite(tempFile, "<style type='text/css'>");
	filewrite(tempFile, "table, th, td {border: 1px solid black;border-collapse: collapse;padding: 8px}");	
	filewrite(tempFile, "</style>");
	filewrite(tempFile, "<title>Progress report</title>");
	filewrite(tempFile, "</head>");
	filewrite(tempFile, "<body>");
	filewrite(tempFile, "<h2><font color = BLUE >Progress report</font></h2>"); 	
	
	listOfClusters.add(0);  //0 is for the total
	listOfClusters.removeDuplicates();
	listOfClusters.sort();
	
	filewrite(tempfile,"<table>");
		//write the headings of the table
		filewrite(tempfile,"<tr>");
			filewrite(tempfile,"<th>Line</th>");
			filewrite(tempfile,"<th>EA</th>");
			IF accreditation = "supervisor" then filewrite(tempfile,"<th>Sup</th>"); else filewrite(tempfile,"<th>Interv</th>"); endif;
			filewrite(tempfile,"<th>Quest</th>");
			filewrite(tempfile,"<th>Expected</th>");
			filewrite(tempfile,"<th>Started</th>");
			filewrite(tempfile,"<th>Completed</th>");
			filewrite(tempfile,"<th>No member</th>");
			filewrite(tempfile,"<th>HH absent</th>");
			filewrite(tempfile,"<th>Refused</th>");
			filewrite(tempfile,"<th>Postponed</th>");
			filewrite(tempfile,"<th>Dwelling vacant</th>");
			filewrite(tempfile,"<th>Dwelling destroyed</th>");
			filewrite(tempfile,"<th>Dwelling not found</th>");
			filewrite(tempfile,"<th>Partially</th>");
			filewrite(tempfile,"<th>Other</th>");
//			filewrite(tempfile,"<th>Incapacitated</th>");
			filewrite(tempfile,"<th>Missing GPS</th>");
			filewrite(tempfile,"<th>Bad closed</th>");
//			filewrite(tempfile,"<th>Temporary codes</th>");
		filewrite(tempfile,"</tr>");
	
		//write the content of the cells of the table
		numeric line = 0;
		do i = 1 while i<= listOfClusters.length()
			numeric clust = listOfClusters(i);
			inc(line); string colorr;
			if line%2 = 0 then colorr = "style = 'background-color: pink'"; else colorr = ""; endif;
			filewrite(tempfile,"<tr %s>",colorr);
				filewrite(tempfile,"<td>%d</td>",line);
				if clust = 0 then filewrite(tempfile,"<td>TOTAL</td>"); else filewrite(tempfile,"<td>%05d</td>",clust); endif;
				filewrite(tempfile,"<td>%03d %s</td>",usercode,getvaluelabel(usercode));
				
				//stats for household
				filewrite(tempfile,"<td>Household</td>");
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,-1)); //expected
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,0)); //Started
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,1)); //Completed
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,2)); //No member
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,3)); //HH absent
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,4)); //Refused
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,5)); //Postponed
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,6)); //Dwelling vacant
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,7)); //Dwelling destroyed
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,8)); //Dwelling not found
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,9)); //Partially
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,10)); //Other
//				filewrite(tempfile,"<td>-</td>");//,statsHH(clust,11)); //incapacitated
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,20)); //Missing GPS
				filewrite(tempfile,"<td>%d</td>",statsHH(clust,21)); //Bad closed
				// filewrite(tempfile,"<td>%d</td>",statsHH(clust,22)); //Temporary codes
//				filewrite(tempfile,"<td> - </td>"); //Temporary codes
			filewrite(tempfile,"</tr>");	

		enddo;
	
	
	
	
	
	filewrite(tempfile,"</table>");
	
// Close html file
	filewrite(tempFile, "</body></html>");
	close(tempFile); 
	
 	if getos() in 20:29 then
		// Android - use "browse:"
	// string	reportFilename = fileconcat(strip(report),strip(slash),"error.HTML",strip(report),strip(slash),);
		execsystem(maketext("view:%s", reportFilename));
	else
		// Windows - use "explorer.exe <filename>"
		execsystem(maketext("%sexplorer.exe %s", 
						    pathname(Windows), 
						    reportFilename));
	endif;		
end;



function listingMen()
	//this function is a sub-function of the error listing function, it deals with household errors. 
	//The LAST 3 lines of this function are very IMPORTANT
	list string listErr;
	listErr.clear();
	listErr.add(maketext("<P><font color = BLUE >%s, HHNumber = %04d, HH Head = %s, Interviewer = %03d %s </font></P>",getlabel(cnulsamp_vs1,A08),A10,strip(A14),A02,getlabel(USERCODE_VS1,A02)));

//section A
		STRING phone = maketext("%d",A12);
		
		if A12 <> 0 and A12 <> notappl then
			if !(length(phone) in 7,9) or phone[1:1] in "0","1" then
				listErr.add(maketext(100,A12));
			endif;		
		endif;
	
		

//We write in the error file	
	if listErr.length() > 1 then //we add in the tempfile only the households that have errors
		filewrite(tempFile,listErr);
	endif;	
end;


function ErrorListing()
	// This function will produce an html file that contains potentials errors in the data file
	//errmsg("In progress");
	string reportFilename = maketext("%sErrorListing.html", "..\REPORTS\");

	setfile(tempFile, reportFilename,create);

	// Standard HTML headers
	filewrite(tempFile, "<!DOCTYPE html>");
	filewrite(tempFile, "<html><head>");
	
	// Add CSS to make tables look nice
	filewrite(tempFile, "<style type='text/css'>");
	filewrite(tempFile, "table, th, td {border: 1px solid black;border-collapse: collapse;padding: 8px}");	
	filewrite(tempFile, "</style>");
	filewrite(tempFile, "<title>Error listing</title>");
	filewrite(tempFile, "</head>");
	filewrite(tempFile, "<body>");


	numeric nbquest ;
			
	nbquest = accept(maketext("Do vous souhaitez lister les erreurs pour un seul questionnaire ou pour tous les questionnaires de l'EA %d",CNULSAMP), 
						"1 - Un seul questionnaire",
						maketext("2 - Tous les questionnaires de EA %d", CNULSAMP));
	
	//Preparing working files
	MergeFiles();
	
	//Starting to write errors
	
		filewrite(tempFile, "<h2><font color = BLUE >ERROR LISTING</font></h2>");
		
		if nbQuest = 1 then //only one questionnaire
			ok = selcase("Veuillez sélectionner un foyer",SOCIAL_DICT,"")
					where A08 = CNULSAMP
					include (A10,A14,A15);
			if ok = 0 then
				errmsg("Yvous n'avez rien sélectionné");
				reenter;
			else
				listingMen();
			endif;
		elseif nbQuest = 2 then //all the questionnaires
			forcase SOCIAL_DICT where A08 = CNULSAMP do 
				listingMen();
			endfor;
		endif; 


	// Closing html file
	filewrite(tempFile, "</body></html>");
	close(tempFile); 
	
 	if getos() in 20:29 then
		// Android - use "browse:"
		execsystem(maketext("view:%s", reportFilename));
	else
		// Windows - use "explorer.exe <filename>"
		execsystem(maketext("%sexplorer.exe %s", 
						    pathname(Windows), 
						    reportFilename));
	endif;		
end;


function ReadQuestionnaire()
	// This function will produce an html file that contains the printed questionnaire
	// errmsg("In progress");
	
	string reportFilename = maketext("%sReadQuestionnaire.html", "..\REPORTS\");

	setfile(tempFile, reportFilename,create);

	// Standard HTML headers
	filewrite(tempFile, "<!DOCTYPE html>");
	filewrite(tempFile, "<html><head>");
	
	// Add CSS to make tables look nice
	filewrite(tempFile, "<style type='text/css'>");
	filewrite(tempFile, "table, th, td {border: 1px solid black;border-collapse: collapse;padding: 8px}");	
	filewrite(tempFile, "</style>");
	filewrite(tempFile, "<title>Read questionnaire</title>");
	filewrite(tempFile, "</head>");
	filewrite(tempFile, "<body>");


	//Preparing working files
	MergeFiles();
	
	//Starting to write questionnaire int html
	
		filewrite(tempFile, "<h2><font color = BLUE >READING SR QUESTIONNAIRE</font></h2>");
		
		ok = selcase("Veuillez sélectionner un foyer",SOCIAL_DICT,"")
				where A08 = CNULSAMP
				include (A10,A14,A15);
		if ok = 0 then
			errmsg("Vous n'avez rien sélectionné");
			reenter;
		else
		endif;
end;

PROC MENU_FF


preproc
setfont(valuesets, "Arial", 20, bold);
temp = "../ENTRY/";


 if getos() in 10:19 then //windows
  slash = "\";
 elseif getos() in 20:29 then //android
   slash = "/"; 
 endif;

  wrkprj = pathname(application)[1:length(pathname(application))-length(slash + "ENTRY")-1]; 

if !DirExist("../BACKUP") then DirCreate("../BACKUP"); endif;
if !DirExist("../DATA") then DirCreate("../DATA"); endif;
if !DirExist("../WORK") then DirCreate("../WORK"); endif;
if !DirExist("../REPORTS") then DirCreate("../REPORTS"); endif;
if !DirExist("../DOCUMENTS") then DirCreate("../DOCUMENTS"); endif;
if !DirExist("../REF1") then DirCreate("../REF1"); endif; //This folder will contains added sample
sdcard1 = pathname(CSEntryExternal);
sdcard = pathname(CSEntryExternal)[1:pos("/Android",pathname(CSEntryExternal))];
reportt = "..\REPORTS\";
wdir = wrkprj + "\WORK";


goog  = concat( "C:", strip(slash), "Program Files\Google\Google Earth Pro\client" );
if fileexist(concat( "C:", strip(slash), "Program Files (x86)\Google\Google Earth\client\googleearth.exe" )) then //pour prendre en compte la machine de pierro
goog  = concat( "C:", strip(slash), "Program Files (x86)\Google\Google Earth\client" );
endif;

PROC USERCODE

preproc

	string DateVersionApplication = edit("99999999999999",publishdate());

	//We will write the agent code in a file named AgentCode.dat that we will store in the REF folder
	if !fileexist("../REF/AgentCode.dat") then
		warning("Bonjour ! Vous êtes connecté à l'application de l'Enquête Nationale sur la Consommation en Algérie.\nVeuillez sélectionner votre nom dans cette liste pour procéder à la configuration.");	
	else
		setfile(FileAgentCode,"../REF/AgentCode.dat");
		fileread(FileAgentCode,AgentCode); //we retrieve the agent code found on the first line of the AgentCode.dat file
		$ = tonumber(strip(AgentCode));
		close(FileAgentCode);
		noinput;
	endif;

onfocus

	setproperty("DisplayCodesAlongsideLabels","YES");
	
postproc

	//Since a tablet must have a single user, if the agent who had already logged in modifies his account, he is forced to bring back the first account with which he logged in
	if fileexist("../REF/AgentCode.dat") then
		setfile(FileAgentCode,"../REF/AgentCode.dat");
		fileread(FileAgentCode,AgentCode); //we retrieve the agent code found on the first line of the AgentCode.dat file
		$ = tonumber(strip(AgentCode));
		close(FileAgentCode);
	endif;


	//when you log in for the first time, you save the agent code 	
	if !fileexist("../REF/AgentCode.dat") then 
		
		OK = accept(maketext("Vous avez sélectionné %s . si c'est vous, cliquer sur CONTINUE; sinon cliquer sur MODIFY",getvaluelabel(userCode)),"CONTINUE","MODIFY"); 
		if OK = 1 then
			if usercode = 0 then
				password = prompt("Enter the password",password);
				if tolower(password) <> "algerie" then
					errmsg("Mot de passe incorrect, veuillez corriger");
					reenter;
				endif;
			endif;
			setfile(FileAgentCode,"../REF/AgentCode.dat",create); 
			filewrite(FileAgentCode,edit("999",$));
			close(FileAgentCode);
		else
			reenter;
		endif;
	endif;

	
	//the agent is told the version of the application he is working with
	errmsg("ENQUETE NATIONALE SUR LA CONSOMMATION EN ALGERIE 2025.........\nBonjour %s.\nVous travaillez avec la version Application de %s/%s/%s at %sh%smin%ss",getvaluelabel(userCode), DateVersionApplication[7:2],DateVersionApplication[5:2],DateVersionApplication[1:4],DateVersionApplication[9:2],DateVersionApplication[11:2],DateVersionApplication[13:2]);
	

	//we create working variable accreditation and OperatorCode
	if usercode  = 0 then
		accreditation = "coordo";
	elseif edit("999",userCode)[3:1] = "0" then
		accreditation = "supervisor";
	else
		accreditation = "interviewer";
	endif;

	OperatorCode = edit("999",$);

PROC CNULSAMP
preproc
	if !fileexist("../REF/sampleinfo.csdb") and accreditation <> "interviewer" then
		decompress("../WORK/sampleinfo.zip","../REF/");
	endif;

	
ONFOCUS
	//We want each participant to see only the Cluster that have been assigned to him

	valset.clear();

	close(SAMPLE_DICT); close(FOLLOW_DICT); 
	filedelete("../REF/sample.dat.csidx");filedelete("../REF/follow.dat.csidx");
	setfile(SAMPLE_DICT,"../REF/sample.dat"); setfile(FOLLOW_DICT,"../REF/follow.dat",append);
	
	// list completedEA = 32140,32141,32142,32143,32144,32145,32146,32147,32148,32149,32150,32151,32152,32153,31217,31218,31219,31220,31221,31222,31223,31224,31225,31226,31227,31228,31230,31231,31232,31233,32206,32419,32420,32422,32423,32425,32426,32427,32428,32429,32430,32431,32432,32433,32434,32436,30106,30107,30108,30109,30110,30629,30610,30632,30633,30634,30230,30274,30401,32154,32156,32158,32159,32160,32162,32163,32164,30223,30224,30225,30233,30235,30236,30237,30241,30242,30243,32234,32235,32236,32227,32238,32219,32218,32217,30863,30867,30873,30874,33316,33317,33318,33319,33320,33321,33322,33323,33325,33121,33120,33118,33117,33116,33113,31212,31211,31210,30846,30845,30844,30843,32437,32438,32439,32440,32441,32442,32443,32447,32448,32449,32452,32453,32454,32455,33315,33314,33313,33312,33311,33310,33307,33306,33305,33103,33226,33227,33228,33229,33230,33231,33301,33302,33303,33304,33308,33309,33324,30238,30239,30240,32302,32303,32304,32305,32401,32402,32403,32404,32405,32406,32407,32408,32409,32410,32411,32418,32475,32476,32477,32478,32479,32480,32473,32474,32183,32347,30640,32182,32170,32171,32172,32173,32174,32175,32177,32179,32180,32168,32167,32169,32161,32157,30254,30253,30901,30904,30457,30456,30455,30448,30447,30446,30430,30429,30428,30427,30426,32377,32127,32129,32128,32126,32125,32124,32123,30458,32237,32239,32240,32242,32243,32245,32246,32248,32251,32252,32253,32254,32255,30301,30302,30303,30304,30391,30392,30393,30649,30650,30651,30652,30653,30659,30856,30857,30858,30862,30868,32155,32165,32166,32178,32181,30861,31443,31445,31447,31456,31468,31469,32101,32102,30120,30121,30122,30123,30124,30125,30217,30218,30219,30221,30421,30442,31112,31113,31117,31110,31125,33135,33136,33137,33218,33219,33220,33222,33224,33225,33222,33224,33225,32356,32355,32354,32214,32213,32212,32211,32210,32209,32208,32207,32204,32203,32202,33128,33130,33131,33132,33133,33134,30101,30102,30103,30104,30105,30142,30145,30636,30637,30638,30201,30202,30204,30207,30210,30214,30248,30255,30256,32215,32216,32458,32459,32460,32461,32462,32463,32464,32465,32468,32470,32310,32312,32313,32314,32315,32481,32316,32317,32318,32319,32320,32321,32322,32308,32309,32306,32301,32307,32311,32322,32344,32345,32346,32482,32483,32484,32485,30150,30149,30148,30131,30132,30134,30135,30136,30137,30138,30462,30460,30463,33111,33112,33114,33107,33108,33109,33110,33104,33105,33106,33201,33202,33204,33205,33206,33207,33208,33209,33210,33211,33214,33215,33216,30835,30111,30112,30113,30114,30453,30454,30116,30115,30932,30929,30925,30913,30912,30905,30481,30479,30478,31357,31358,31431,31434,31432,31433,31435,31438,31439,30279,30280,30281,30282,30283,30284,30285,30286,33127,33126,33125,33124,30853,30850,32233,32232,32231,32230,32229,32228,32226,32225,32224,32130,32131,32132,32133,32134,32135,32136,32137,32138,32139,32376;
	list completedEA;
	
	if accreditation = "supervisor" then
		forcase SAMPLE_DICT do
			if XSUP = userCode and completedEA.seek(XCLUSTER) = 0 then
				valset.add(getlabel(cnulsamp_VS1,Xcluster),XCLUSTER);
			endif;
		endfor;
	elseif accreditation = "interviewer"  then 
		hashmap tt(numeric) default(0);
		forcase FOLLOW_DICT do
			if FINTCODE = userCode and tt(FCLUSTER)=0  and completedEA.seek(FCLUSTER) = 0  then
				valset.add(getlabel(cnulsamp_VS1,FCLUSTER),FCLUSTER);
				tt(FCLUSTER) = 1;
			endif;
		endfor;	
	endif;
	
	if length(valset.codes) = 0 then
		//this means that the interviewer have not yet received Assignments from the supervisor
		skip to GOPT;
	else
		setvalueset($,valset);
	endif;
	
PROC GOPT
	
onfocus
	OperatorMenu1();

postproc
	IF $ = 1 then 
		OPTION = 11; //this because GOPT = 1 have only one option (11)
		advance;
	endif;
	
	if $ = 7 then 
		close(SOCIAL_DICT); close(FOLLOW_DICT);  close(SAMPLE_DICT);   

		backup();
		reenter; 
	endif;
	
	if $ = 8 then  reenter CNULSAMP; endif;
	if $ = 9 then  stop(1); endif;	

	
PROC OPTION

preproc
	if !fileexist("../REF/sampleinfo.csdb") and accreditation <> "interviewer" then
		//errmsg("Please you don't have the latest version of program, download the update again");
	endif;
	
onfocus
OperatorMenu();

postproc


if $ = 2 then //Assign HOUSEHOLD 
	close(FOLLOW_DICT); close(SAMPLE_DICT);
	genpff1("SAMPLE");
	runpff("SAMPLE");
	reenter;	
elseif $ = 3 then //Point on Assignment
	SummaryOfAssignment();
	reenter GOPT ;	
ELSEif $ = 11 then //saisir Quest Menage
	close(SOCIAL_DICT); close(FOLLOW_DICT); close(SAMPLE_DICT);
	op_select();
	close(SOCIAL_DICT); close(FOLLOW_DICT); close(SAMPLE_DICT);

	genpff3("SOCIAL");
	runpff("SOCIAL");	
	reenter;
	
elseif $ = 30 then	//transfer Assignment
	transfer_assign();
	reenter GOPT ;
elseif $ = 31 then	//receive Assignments
	receive_assign();		
	stop(1);
	reenter GOPT ;
elseif $ = 32 then	//transfer data (supervisor transfer on dropbox and interviewer transfer thru bluetooth
	close(FOLLOW_DICT); close(SOCIAL_DICT); close(SAMPLE_DICT);
	transfer_data();
	stop(1);//we close the application after the data transfer because we also receive the update there
	reenter GOPT ;	
elseif $ = 33 then	//supervisor receive data
	IF accreditation = "coordo" then
		receive_data_coordo();
	else //supersvisor
		receive_data(); 
	endif;
	reenter;
elseif $ = 34 then	//Supervisor Transfer update to interviewer thru bluetooth
	transfer_update();
	stop(1);
	reenter;
elseif $ = 35 then	//receive update
	receive_update();
	stop(1);//we close the application after receiving the update
	reenter;
	
elseif $ = 36 then	//export data to spss and stata
	export_data();
	reenter;
	
elseif $ = 50 then //Error listing
	ErrorListing(); 
	reenter GOPT ;
	
elseif $ = 52 then //Detailed result
	
	DetailedResult();
	reenter;
	
elseif $ = 53 then //Work progress
	WorkProgress();
	reenter GOPT ;
	
elseif $ = 54 then //Read questionnaire
	ReadQuestionnaire();
	reenter GOPT ;
	
elseif $ = 60 then //Show cluster sample households
	DisplayOnMap();
	reenter GOPT ;	
	
elseif $ = 61 then //Show interviewed households
	DisplayOnMap2();
	reenter GOPT ;		
	
elseif $ = 90 then
	stop(1);
else
	reenter;
endif;


 
