﻿---
fileType: Question Text
version: CSPro 7.7
languages:
  - name: EN
    label: English
styles:
  - name: Normal
    className: normal
    css: |
      font-family: Arial;font-size: 16px;
  - name: Instruction
    className: instruction
    css: |
      font-family: Arial;font-size: 14px;color: #0000FF;
  - name: Heading 1
    className: heading1
    css: |
      font-family: Arial;font-size: 36px;
  - name: Heading 2
    className: heading2
    css: |
      font-family: Arial;font-size: 24px;
  - name: Heading 3
    className: heading3
    css: |
      font-family: Arial;font-size: 18px;
questions:
  - name: SOCIAL_DICT.A08
    conditions:
      - questionText:
          EN: |
            <p>EA number</p>
  - name: SOCIAL_DICT.A10
    conditions:
      - questionText:
          EN: |
            <p>Household number</p>
  - name: SOCIAL_DICT.A02
    conditions:
      - questionText:
          EN: |
            <p>Interviewer</p>
  - name: SOCIAL_DICT.A03
    conditions:
      - questionText:
          EN: |
            <p>Supervisor</p>
  - name: SOCIAL_DICT.A04
    conditions:
      - questionText:
          EN: |
            <p>Area</p>
  - name: SOCIAL_DICT.A05
    conditions:
      - questionText:
          EN: |
            <p>LGA</p>
  - name: SOCIAL_DICT.START_TIME
    conditions:
      - questionText:
          EN: |
            <p>Start time</p>
  - name: SOCIAL_DICT.A01
    conditions:
      - questionText:
          EN: |
            <p>Date of the interview</p>
  - name: SOCIAL_DICT.A16
    conditions:
      - questionText:
          EN: |
            <p>Good morning/afternoon Sir/Madam. I am an interviewer for the [National Social Registry]. The aim of this registry is to create a single database that will be used by government institutions as well as national and international aid agencies to inform social protection policies and programs.All necessary measures will be taken to ensure that data is not manipulated or illegally accessed or used for other purposes. The interview will take about <b>[</b><i><b>test the time that it takes for the interview to be conducted, this will depend on the number of household members</b></i><b>]</b>. Do you agree to participate in the interview and consent for the use of the data collected by agencies engaged in social protection programs?<br></p>
  - name: SOCIAL_DICT.A11
    conditions:
      - questionText:
          EN: |
            <p>Full name of respondent</p>
  - name: SOCIAL_DICT.A12
    conditions:
      - questionText:
          EN: |
            <p>Telephone number</p>
  - name: SOCIAL_DICT.A13
    conditions:
      - questionText:
          EN: |
            <p>Address</p>
  - name: SOCIAL_DICT.A14
    conditions:
      - questionText:
          EN: |
            <p>Full name of head of household</p>
  - name: SOCIAL_DICT.B01
    conditions:
      - questionText:
          EN: |
            <p>Line number</p>
  - name: SOCIAL_DICT.B02A
    conditions:
      - logic: curocc() = 1
        questionText:
          EN: |
            <p><b><font color="#0000ff">HOUSEHOLD MEMBER ROSTER<br></font></b></p><p>PLEASE TELL ME THE NAME OF ALL INDIVIDUALS WHO USUALLY LIVE AND EAT THEIR MEALS TOGETHER IN THIS HOUSEHOLD, STARTING WITH THE HEAD OF THE HOUSEHOLD.<br></p><p>What is the First name of the household head?</p><p><span class="instruction">Interviewer:&nbsp;Probe for additional household members</span></p><p><br></p>
      - logic: curocc() > 1
        questionText:
          EN: |
            First name of household member&nbsp;&nbsp;<font color="#0000ff">~~curocc()~~</font><p></p>
  - name: SOCIAL_DICT.B02B
    conditions:
      - logic: curocc() = 1
        questionText:
          EN: |
            <p>What is the Last name of the household head?<br></p>
      - logic: curocc() > 1
        questionText:
          EN: |
            Last name of household member <font color="#0000ff">~~curocc()~~</font><p></p>
  - name: SOCIAL_DICT.B03
    conditions:
      - questionText:
          EN: |
            <p>Is&nbsp;<span style="color: rgb(0, 0, 255);">~~B02A(curocc())~~ ~~B02B(curocc())~~</span>&nbsp;male or female?<br></p>
  - name: SOCIAL_DICT.B04
    conditions:
      - questionText:
          EN: |
            <p>What is the relationship of&nbsp;<span style="color: rgb(0, 0, 255);">~~B02A(curocc())~~ ~~B02B(curocc())~~</span>&nbsp;to&nbsp;<span style="color: rgb(0, 0, 255);">~~B02A(1)~~ ~~B02B(1)~~</span>?<br></p>
  - name: SOCIAL_DICT.B07A
    conditions:
      - questionText:
          EN: |
            <p>&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>Day of Birth</p><p><span class="instruction"><br></span></p><p><span class="instruction">Recode 98&nbsp; If unknown</span><br></p>
  - name: SOCIAL_DICT.B07B
    conditions:
      - questionText:
          EN: |
            <p>&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>Month of Birth</p><p><span style="color: rgb(0, 0, 255); font-size: 14px;">Recode 98&nbsp; If unknown</span><br></p>
  - name: SOCIAL_DICT.B07
    conditions:
      - questionText:
          EN: |
            <p>&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>Year of birth&nbsp;</p>
  - name: SOCIAL_DICT.B08
    conditions:
      - questionText:
          EN: |
            <p>&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>Age</p>
  - name: SOCIAL_DICT.MORE
    conditions:
      - questionText:
          EN: |
            <p>Are there more household members?<br></p>
  - name: SOCIAL_DICT.B05
    conditions:
      - questionText:
          EN: |
            <p><b><font color="#0000ff">B. DEMOGRAPHIC CHARACTERISTICS OF THE HOUSEHOLD MEMBERS&nbsp;</font><font color="#ff00ff">(Individual&nbsp; N°~~curocc()~~)</font></b>&nbsp;</p><p><font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>Residence status</p>
  - name: SOCIAL_DICT.B06
    conditions:
      - questionText:
          EN: |
            <p>&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>Date of arrival</p><p><span class="instruction">RECORD THE YEAR OF ARRIVAL</span></p>
  - name: SOCIAL_DICT.B09
    conditions:
      - questionText:
          EN: |
            <p>&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>Place of Birth</p>
  - name: SOCIAL_DICT.B10
    conditions:
      - questionText:
          EN: |
            <p>&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>Nationality</p>
  - name: SOCIAL_DICT.B11
    conditions:
      - questionText:
          EN: |
            <p>check all Identification documents that&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>possesses</p>
  - name: SOCIAL_DICT.B11X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.B11A
    conditions:
      - questionText:
          EN: |
            <p>Take a picture of one of&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>document available</p>
  - name: SOCIAL_DICT.B11B
    conditions:
      - questionText:
          EN: |
            <p><span class="instruction">Which document did you captured ?</span></p><p><span class="instruction"><br></span></p>
  - name: SOCIAL_DICT.B12
    conditions:
      - questionText:
          EN: |
            <p>Does&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;&nbsp;</font>have a birth certificate ?</p>
  - name: SOCIAL_DICT.B12A
    conditions:
      - questionText:
          EN: |
            <p>Take picture&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>birth certificate</p>
  - name: SOCIAL_DICT.B13
    conditions:
      - questionText:
          EN: |
            <p>What is&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>Ethnicity ?</p>
  - name: SOCIAL_DICT.B13X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.B14
    conditions:
      - questionText:
          EN: |
            <p>What is&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;&nbsp;</font>marital status ?</p>
  - name: SOCIAL_DICT.B15A
    conditions:
      - questionText:
          EN: |
            <p>Is&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>father still alive ?</p>
  - name: SOCIAL_DICT.B15B
    conditions:
      - questionText:
          EN: |
            Does&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>father live in this household ?<p></p>
  - name: SOCIAL_DICT.B15C
    conditions:
      - questionText:
          EN: |
            Select&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>father this household ?<p></p>
  - name: SOCIAL_DICT.B16A
    conditions:
      - questionText:
          EN: |
            Is&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>mother still alive ?<p></p>
  - name: SOCIAL_DICT.B16B
    conditions:
      - questionText:
          EN: |
            Does&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>mother live in this household ?<p></p>
  - name: SOCIAL_DICT.B16C
    conditions:
      - questionText:
          EN: |
            Select&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>mother this household ?<p></p>
  - name: SOCIAL_DICT.C01
    conditions:
      - questionText:
          EN: |
            <p>Line number</p>
  - name: SOCIAL_DICT.C04
    conditions:
      - questionText:
          EN: |
            <p><font color="#0000ff" style=""><b>C. EDUCATION OF THE HOUSEHOLD MEMBERS&nbsp;&nbsp;</b></font><font color="#ff00ff" style="font-weight: bold;">(Individual&nbsp; N°~~curocc()~~)</font><font color="#0000ff" style="font-weight: bold;"><br></font></p><div><b><font color="#ff00ff"><br></font></b></div><p>Has&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;ever been to school?</p>
  - name: SOCIAL_DICT.C05A
    conditions:
      - questionText:
          EN: |
            <p>Which level is&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;attending ?</p>
  - name: SOCIAL_DICT.C05B
    conditions:
      - questionText:
          EN: |
            <p>Which grade is&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;attending&nbsp;?</p>
  - name: SOCIAL_DICT.C06
    conditions:
      - questionText:
          EN: |
            <p>What type of school has&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;attended?</p>
  - name: SOCIAL_DICT.C07A
    conditions:
      - questionText:
          EN: |
            <p>Which was the previous level/grade that&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;attained?</p>
  - name: SOCIAL_DICT.C07B
    conditions:
      - questionText:
          EN: |
            <p>Highest grade attained at level&nbsp;<span style="color: rgb(0, 0, 255);">~~getvaluelabel(C07A)~~</span></p>
  - name: SOCIAL_DICT.C08
    conditions:
      - questionText:
          EN: |
            <p>Can&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;read and/or write in any language?</p>
  - name: SOCIAL_DICT.C09
    conditions:
      - questionText:
          EN: |
            <p>Why has&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;never been to school?</p>
  - name: SOCIAL_DICT.C09X
    conditions:
      - questionText:
          EN: |
            Specify<p></p>
  - name: SOCIAL_DICT.D01
    conditions:
      - questionText:
          EN: |
            <p>Line number</p>
  - name: SOCIAL_DICT.D03
    conditions:
      - questionText:
          EN: |
            <p><font color="#0000ff"><b>D. HEALTH OF THE HOUSEHOLD MEMBERS&nbsp;&nbsp;</b></font><font color="#ff00ff" style="font-weight: bold;">(Individual&nbsp; N°~~curocc()~~)</font><font color="#0000ff" style="font-weight: bold;"><br></font></p><div><font color="#ff00ff" style="font-weight: bold;"><br></font></div><p>Does&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;suffer from any chronic illness?</p>
  - name: SOCIAL_DICT.D04
    conditions:
      - questionText:
          EN: |
            <p>From what type of chronic illness does&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;suffer?</p>
  - name: SOCIAL_DICT.D04X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.D05A
    conditions:
      - questionText:
          EN: |
            <p>Does&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;have difficulty seeing, even if wearing 
            glasses?</p>
  - name: SOCIAL_DICT.D05B
    conditions:
      - questionText:
          EN: |
            Does&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;have any 
            difficulty hearing, even if using 
            a hearing aid?<p></p>
  - name: SOCIAL_DICT.D05C
    conditions:
      - questionText:
          EN: |
            Does&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp; have any 
            difficulty walking or climbing 
            steps?<p></p>
  - name: SOCIAL_DICT.D05D
    conditions:
      - questionText:
          EN: |
            Does&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;have 
            difficulty remembering or 
            concentrating?<p></p>
  - name: SOCIAL_DICT.D05E
    conditions:
      - questionText:
          EN: |
            Does&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;have 
            difficulty with self-care such as 
            washing all over or dressing?<p></p>
  - name: SOCIAL_DICT.D05F
    conditions:
      - questionText:
          EN: |
            Does&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp; have 
            difficulty communicating, for 
            example understanding or being 
            understood?<p></p>
  - name: SOCIAL_DICT.E01
    conditions:
      - questionText:
          EN: |
            <p>Line number</p>
  - name: SOCIAL_DICT.E02
    conditions:
      - questionText:
          EN: |
            <p><font color="#0000ff"><b>E. EMPLOYMENT OF THE HOUSEHOLD MEMBERS&nbsp;&nbsp;&nbsp;</b></font><font color="#ff00ff" style="font-weight: bold;">(Individual&nbsp; N°~~curocc()~~)</font><font color="#0000ff" style="font-weight: bold;"><br></font></p><div>What has been&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>’s MAIN job/ activity in the last 30 days?<br></div>
  - name: SOCIAL_DICT.E03
    conditions:
      - questionText:
          EN: |
            <p>&nbsp;How frequently&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;has been working ?</p>
  - name: SOCIAL_DICT.E04
    conditions:
      - questionText:
          EN: |
            <p>In which (main) sector&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;has been working?</p>
  - name: SOCIAL_DICT.E05
    conditions:
      - questionText:
          EN: |
            <p>Which is&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~'s&nbsp;</font>status?</p>
  - name: SOCIAL_DICT.E06
    conditions:
      - questionText:
          EN: |
            <p>Why&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>has not been working, what is the main 
            reason?</p>
  - name: SOCIAL_DICT.E06X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.F01
    conditions:
      - questionText:
          EN: |
            <p><font color="#0000ff"><b>F. HOUSING</b></font><br></p><p>What is the occupancy status of the dwelling?</p>
  - name: SOCIAL_DICT.F02
    conditions:
      - questionText:
          EN: |
            <p>What is the main construction material of the exterior wall of the main dwelling?</p>
  - name: SOCIAL_DICT.F02X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.F03
    conditions:
      - questionText:
          EN: |
            <p>What is the main material used for roofing of the main dwelling?</p>
  - name: SOCIAL_DICT.F03X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.F04
    conditions:
      - questionText:
          EN: |
            <p>What is the main material used for the floor of the main dwelling?</p>
  - name: SOCIAL_DICT.F04X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.F05
    conditions:
      - questionText:
          EN: |
            <p>What is the household’s main source of lightning?</p>
  - name: SOCIAL_DICT.F05X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.F06
    conditions:
      - questionText:
          EN: |
            <p>What is the household’s main cooking fuel?</p>
  - name: SOCIAL_DICT.F06X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.F07
    conditions:
      - questionText:
          EN: |
            <p>What type of toilet facility does your household use?</p>
  - name: SOCIAL_DICT.F07X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.F08
    conditions:
      - questionText:
          EN: |
            <p>Is the toilet facility shared with other households?</p>
  - name: SOCIAL_DICT.F09
    conditions:
      - questionText:
          EN: |
            <p>How many household in total use this toilet facility including your own household?</p>
  - name: SOCIAL_DICT.F10
    conditions:
      - questionText:
          EN: |
            <p>What is the main source of drinking water used by the household?</p>
  - name: SOCIAL_DICT.F10X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.F11
    conditions:
      - questionText:
          EN: |
            <p>How does the household usually dispose of rubbish/refuse?</p>
  - name: SOCIAL_DICT.F11X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.G00
    conditions:
      - questionText:
          EN: |
            <p>Service facility</p>
  - name: SOCIAL_DICT.G01
    conditions:
      - logic: CUROCC()=1
        questionText:
          EN: |
            <p><b><font color="#0000ff">G. DISTANCE FROM SERVICES<br></font></b></p><p>How far (in km ) is<font color="#397b21">&nbsp;~~getvaluelabel(G00)~~&nbsp;</font> from your household?</p>
      - logic: curocc()>1
        questionText:
          EN: |
            How far (in km ) is<font color="#397b21">&nbsp;~~getvaluelabel(G00)~~&nbsp;</font>&nbsp;from your household?<p></p>
  - name: SOCIAL_DICT.G02
    conditions:
      - questionText:
          EN: |
            <p>How long (minutes) does your household take to reach&nbsp;<font color="#397b21">&nbsp;~~getvaluelabel(G00)~~&nbsp;</font>&nbsp;?</p>
  - name: SOCIAL_DICT.G03
    conditions:
      - questionText:
          EN: |
            <p>Which means does your household mainly use to reach&nbsp;<font color="#397b21">&nbsp;~~getvaluelabel(G00)~~&nbsp;</font>&nbsp;?</p>
  - name: SOCIAL_DICT.G03X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.H00
    conditions:
      - questionText:
          EN: |
            <p>Item</p>
  - name: SOCIAL_DICT.H01
    conditions:
      - logic: CUROCC() in 2:10
        questionText:
          EN: |
            <p>Does anyone in the household own&nbsp;<span style="color: rgb(57, 123, 33);">~~getvaluelabel(H00)~~&nbsp;</span>?</p>
      - logic: curocc()>10
        questionText:
          EN: |
            Does your household own&nbsp;<span style="color: rgb(57, 123, 33);">~~getvaluelabel(H00)~~&nbsp;</span>?<p></p>
      - logic: curocc()=1
        questionText:
          EN: |
            <p><b><font color="#0000ff">H. HOUSEHOLD ASSETS<br></font></b></p><p>Does anyone in the household own&nbsp;<span style="color: rgb(57, 123, 33);">~~getvaluelabel(H00)~~&nbsp;</span>?<br></p>
  - name: SOCIAL_DICT.H02
    conditions:
      - questionText:
          EN: |
            <p>What is the total number of&nbsp;&nbsp;<span style="color: rgb(57, 123, 33);">~~getvaluelabel(H00)~~&nbsp;</span>&nbsp;owned?</p>
  - name: SOCIAL_DICT.H03A
    conditions:
      - questionText:
          EN: |
            <p>How long ago the&nbsp;&nbsp;<span style="color: rgb(57, 123, 33);">first&nbsp;</span>&nbsp;<span style="color: rgb(57, 123, 33);">~~getvaluelabel(H00)~~&nbsp;</span>was obtained?</p><p><span class="instruction">NUMBER OF YEARS, IF LESS THAN ONE YEAR RECORD 00</span><br></p>
  - name: SOCIAL_DICT.H03B
    conditions:
      - questionText:
          EN: |
            <p>How long ago the&nbsp;&nbsp;<span style="color: rgb(57, 123, 33);">second&nbsp;</span><span style="color: rgb(57, 123, 33);">~~getvaluelabel(H00)~~&nbsp;</span>was obtained?</p><p><span style="color: rgb(0, 0, 255); font-size: 14px;">NUMBER OF YEARS, IF LESS THAN ONE YEAR RECORD 00</span><br></p>
  - name: SOCIAL_DICT.I01
    conditions:
      - questionText:
          EN: |
            <p><b><font color="#0000ff">I. INCOME SOURCES<br></font></b></p><p>What is the household’s main source of income?</p>
  - name: SOCIAL_DICT.I01X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.I02
    conditions:
      - questionText:
          EN: |
            <p>What is the household’s second main income source?</p>
  - name: SOCIAL_DICT.I02X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.I03
    conditions:
      - questionText:
          EN: |
            <p>In the past 12 months, did the household received help in the form of money (and goods) from individual(s) not living in the household?</p>
  - name: SOCIAL_DICT.I04
    conditions:
      - questionText:
          EN: |
            <p>If yes, how many times?</p>
  - name: SOCIAL_DICT.I05
    conditions:
      - questionText:
          EN: |
            <p>In total, what was the amount (Dalasi) received in the last 12 months?</p>
  - name: SOCIAL_DICT.I06
    conditions:
      - questionText:
          EN: |
            <p>In the past 12 months, has this household received or collected any aid (money and/or goods) from any organization?</p>
  - name: SOCIAL_DICT.I07
    conditions:
      - questionText:
          EN: |
            <p>If yes, what type of aid has the household received?</p>
  - name: SOCIAL_DICT.I07X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.I08
    conditions:
      - questionText:
          EN: |
            <p>How frequently?</p>
  - name: SOCIAL_DICT.I08X
    conditions:
      - questionText:
          EN: |
            <p>Other</p>
  - name: SOCIAL_DICT.I09
    conditions:
      - questionText:
          EN: |
            <p>From which type of organization?</p>
  - name: SOCIAL_DICT.J01
    conditions:
      - questionText:
          EN: |
            <p><b><font color="#0000ff">J. AGRICULTURAL ACTIVITY<br></font></b></p><p>Does anyone in your household cultivate land?</p>
  - name: SOCIAL_DICT.J02A
    conditions:
      - questionText:
          EN: |
            <p>Type of land</p>
  - name: SOCIAL_DICT.J02
    conditions:
      - questionText:
          EN: |
            <p>How much&nbsp;<span style="color: rgb(57, 123, 33);">&nbsp;</span><span style="color: rgb(57, 123, 33);">~~getvaluelabel(J02A)~~ land&nbsp;</span>&nbsp;does the household cultivate?</p><p><span class="instruction">Specify how many hectares</span></p>
  - name: SOCIAL_DICT.J03
    conditions:
      - questionText:
          EN: |
            <p>If owned, by whom?</p>
  - name: SOCIAL_DICT.J04A
    conditions:
      - questionText:
          EN: |
            <p>Type of ecology</p>
  - name: SOCIAL_DICT.J04
    conditions:
      - questionText:
          EN: |
            <p>Type of ecology :&nbsp;<span style="color: rgb(57, 123, 33);">&nbsp;</span><span style="color: rgb(57, 123, 33);">~~getvaluelabel(J04A)~~.</span><br></p><p><span class="instruction">Specify how many hectares of this type of land is cultivated ?</span></p>
  - name: SOCIAL_DICT.J05A
    conditions:
      - questionText:
          EN: |
            <p>Crops</p>
  - name: SOCIAL_DICT.J05
    conditions:
      - questionText:
          EN: |
            <p>How much land was cultivated with <span style="color: rgb(57, 123, 33);">~~getvaluelabel(J05A)~~&nbsp;</span>for sale or family use during the last 12 months?</p><p><span class="instruction">Specify how many hectares</span></p>
  - name: SOCIAL_DICT.J06
    conditions:
      - questionText:
          EN: |
            <p>Select all members responsibles for cultivation of&nbsp;&nbsp;<span style="color: rgb(57, 123, 33);">~~getvaluelabel(J05A)~~&nbsp;</span></p><p><span class="instruction">MULTIPLE SELECTION IS POSSIBLE</span></p>
  - name: SOCIAL_DICT.J06A
    conditions:
      - questionText:
          EN: |
            <p>Number of males responsibles for cultivation</p>
  - name: SOCIAL_DICT.J06B
    conditions:
      - questionText:
          EN: |
            <p>Number of females responsibles for cultivation</p>
  - name: SOCIAL_DICT.J07
    conditions:
      - questionText:
          EN: |
            <p>Was any household member involved in catching or farming fish for sale or family use in the last 12 months?</p>
  - name: SOCIAL_DICT.J07X
    conditions:
      - questionText:
          EN: |
            <p>Select all members involved in catching or farming fish</p><p><span style="color: rgb(0, 0, 255); font-size: 14px;">MULTIPLE SELECTION IS POSSIBLE</span><br></p>
  - name: SOCIAL_DICT.J08A
    conditions:
      - questionText:
          EN: |
            <p>How many male household members</p>
  - name: SOCIAL_DICT.J08B
    conditions:
      - questionText:
          EN: |
            <p>How many female household members</p>
  - name: SOCIAL_DICT.J09
    conditions:
      - questionText:
          EN: |
            <p>Does anyone of your household own livestock?</p>
  - name: SOCIAL_DICT.J10
    conditions:
      - questionText:
          EN: |
            <p>How many<span style="color: rgb(57, 123, 33);">&nbsp;</span><span style="color: rgb(57, 123, 33);">~~getvaluelabel(J10A)~~&nbsp;</span>does the household own?</p>
  - name: SOCIAL_DICT.J10X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.J11
    conditions:
      - questionText:
          EN: |
            <p>Who is responsible for breeding <span style="color: rgb(57, 123, 33);">~~getvaluelabel(J10A)~~&nbsp;</span>&nbsp;?</p>
  - name: SOCIAL_DICT.K01
    conditions:
      - questionText:
          EN: |
            <p><b><font color="#0000ff">K. IMPACT OF SHOCKS<br></font></b></p><p>In the last 12 months, have the household’s livelihood activities been affected by any major negative event?</p>
  - name: SOCIAL_DICT.K02A
    conditions:
      - questionText:
          EN: |
            <p>livelihood</p>
  - name: SOCIAL_DICT.K02B
    conditions:
      - questionText:
          EN: |
            <p>Was&nbsp;<span style="color: rgb(57, 123, 33);">~~getvaluelabel(K02A)~~&nbsp;&nbsp;</span>affected?</p>
  - name: SOCIAL_DICT.K02AX
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.K03
    conditions:
      - questionText:
          EN: |
            <p>What type of shock affected the household&nbsp;<span style="color: rgb(57, 123, 33);">~~getvaluelabel(K02A)~~ -&nbsp;</span>&nbsp;<span style="color: rgb(57, 123, 33);">~~K02AX~~</span><span style="color: rgb(57, 123, 33);">&nbsp;</span>?</p>
  - name: SOCIAL_DICT.K03X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.K04
    conditions:
      - questionText:
          EN: |
            <p>How do you judge the severity of the losses caused by the shocks in&nbsp;<span style="color: rgb(57, 123, 33);">~~getvaluelabel(K02A)~~ -&nbsp;</span>&nbsp;<span style="color: rgb(57, 123, 33);">~~K02AX~~</span><span style="color: rgb(57, 123, 33);">&nbsp;</span>?</p>
  - name: SOCIAL_DICT.L01
    conditions:
      - logic: curocc()=1
        questionText:
          EN: |
            <p><font color="#0000ff"><b>L. COPING STRATEGIES<br></b></font></p><p>In the last 12 months, was someone in this household in need of resorting to&nbsp; <font color="#397b21">~~getocclabel()~~</font>?</p>
      - logic: curocc()>1
        questionText:
          EN: |
            In the last 12 months, was someone in this household in need of resorting to&nbsp;&nbsp;<font color="#397b21">~~getocclabel()~~</font>?<p></p>
  - name: SOCIAL_DICT.L02
    conditions:
      - questionText:
          EN: |
            <p>In the last 12 months, how often did the household have to resort to&nbsp;<font color="#ff9c00">~~getocclabel()~~</font>?</p>
  - name: SOCIAL_DICT.A10GPS
    conditions:
      - questionText:
          EN: |
            <p>Take GPS</p>
  - name: SOCIAL_DICT.A10A
    conditions:
      - questionText:
          EN: |
            <p>Longitude</p>
  - name: SOCIAL_DICT.A10B
    conditions:
      - questionText:
          EN: |
            <p>Latitude</p>
  - name: SOCIAL_DICT.A15
    conditions:
      - questionText:
          EN: |
            <p>Result of the household interview</p>
  - name: SOCIAL_DICT.A15X
    conditions:
      - questionText:
          EN: |
            <p>Other result</p>
  - name: SOCIAL_DICT.BADCLOSED
    conditions:
      - questionText:
          EN: |
            <p>Other items</p>
  - name: SOCIAL_DICT.END_TIME
    conditions:
      - questionText:
          EN: |
            <p>End time</p>
  - name: SOCIAL_DICT.A06
    conditions:
      - questionText:
          EN: |
            <p>District</p>
  - name: SOCIAL_DICT.A07
    conditions:
      - questionText:
          EN: |
            <p>Ward</p>
  - name: SOCIAL_DICT.A09
    conditions:
      - questionText:
          EN: |
            <p>Town/ Settlement</p>
  - name: SOCIAL_DICT.B04A1
    conditions:
      - questionText:
          EN: |
            <p><b><font color="#0000ff">B. DEMOGRAPHIC CHARACTERISTICS OF THE HOUSEHOLD MEMBERS </font><font color="#ff00ff">(Individual&nbsp; N°~~curocc()~~)</font><font color="#0000ff"><br></font></b></p><p>What is the name of <font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~</font>'s father</p><p><br></p>
  - name: SOCIAL_DICT.B04A2
    conditions:
      - questionText:
          EN: |
            <p>What is the name of&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~</font>'s mother<br></p>
  - name: SOCIAL_DICT.B05X
    conditions:
      - questionText:
          EN: |
            Specify<p></p>
  - name: SOCIAL_DICT.C07
    conditions:
      - questionText:
          EN: |
            Is&nbsp;&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>&nbsp;currently attending school?<p></p>
  - name: SOCIAL_DICT.C08X
    conditions:
      - questionText:
          EN: |
            <p>Specify<br></p>
  - name: SOCIAL_DICT.D06
    conditions:
      - questionText:
          EN: |
            <p>What is&nbsp;&nbsp;<font color="#0000ff">~~B02A(curocc())~~ ~~B02B(curocc())~~&nbsp;</font>’s main disability?</p>
  - name: SOCIAL_DICT.J02C
    conditions:
      - questionText:
          EN: |
            <p>how many hectares of&nbsp;<span style="color: rgb(57, 123, 33);">&nbsp;</span><span style="color: rgb(57, 123, 33);">~~getvaluelabel(J02A)~~ lands ?&nbsp;</span></p>
  - name: SOCIAL_DICT.J04B
    conditions:
      - questionText:
          EN: |
            <p>Do you use this type of ecology :&nbsp;<span style="color: rgb(57, 123, 33);">&nbsp;</span><span style="color: rgb(57, 123, 33);">~~getvaluelabel(J04A)~~</span>?</p>
  - name: SOCIAL_DICT.J05B
    conditions:
      - questionText:
          EN: |
            <p>Did you cultivated&nbsp;<span style="color: rgb(57, 123, 33);">~~getvaluelabel(J05A)~~&nbsp;</span>during the last 12 months?</p>
...
