﻿{Application 'SAMPLE' logic file generated by CSPro}
PROC GLOBAL
numeric i,x,n,som,k,tot,y;
array nbHHAssigned(900);
valueset valset;
file tempfile;
list string listsettle, listSGMT;
list NUMERIC listCO, listSG;

  function HHAssigned()
 	string reportFilename = "..\REPORTS\Assignments.html"; 	
	setfile(tempFile, reportFilename, create);
	// Standard HTML headers
	filewrite(tempFile, "<!DOCTYPE html>");
	filewrite(tempFile, "<html><head>");
	
	// Add CSS to make tables look nice
	filewrite(tempFile, "<style type='text/css'>");
	filewrite(tempFile, "table, th, td {border: 1px solid black;border-collapse: collapse;padding: 8px}");	
	filewrite(tempFile, "</style>");
	filewrite(tempFile, "<title>Resume of Assignments </title>");
	filewrite(tempFile, "</head>");
	filewrite(tempFile, "<body>");
	// Id-items
	filewrite(tempFile, "<h2><font color = blue>Resume of Assignments</font></h2>");

    nbHHAssigned.clear();
	
    //we go through lines 1 to 1 and load the recap table of assignments	
    do i=1 while i <= visualvalue(XTOTAL) 
		nbHHAssigned(visualvalue(XINTCODE(i))) = nbHHAssigned(visualvalue(XINTCODE(i))) + 1;
    enddo;
    
    //we write the result in html
    filewrite(tempFile, "<p>%s</p>",getlabel(CNULSAMP,visualvalue(XCLUSTER)));
	filewrite(tempFile, "<p>Number of households in the cluster : %d</p>",xtotal);
	filewrite(tempFile, "<p><font color = blue>Table 1 : Summary of Assignments by interviewer</font></p>");
	filewrite(tempFile, "<table>");
	filewrite(tempFile, "<tr><th>Interviewer code</th><th>Intervierwer name</th><th>Number of households assigned</th><th>Estimated individuals</th></tr>");
	//filewrite(tempFile, "<tr><th>Interviewer code</th><th>Intervierwer name</th><th>Number of households assigned</th></tr>");
	som = 0;
	
	do k= 1 while k <= length(valset.codes);
			x= valset.codes(k);
			filewrite(tempFile, "<tr>");
			filewrite(tempFile, "<td>%03d</td>", x);
			filewrite(tempFile, "<td>%s</td>", getlabel(valset, x));
			filewrite(tempFile, "<td>%d</td>", nbHHAssigned(x));
			tot = 0;
	//SARR		do n= 1 while n <= visualvalue(XTOTAL) inc(tot,(x=visualvalue(XINTCODE(n)))*(visualvalue(xsize(n))<95)*visualvalue(xsize(n))); enddo;
				
			filewrite(tempFile, "<td>%d</td>", tot);
			filewrite(tempFile, "</tr>");
			som = som + (k>1)*nbHHAssigned(x); //this allows to have the total number of households assigned. K = 1 correspond of non assigned households
	enddo;	 	
 
 	filewrite(tempFile, "</table>");

    	filewrite(tempFile, "<p><font color = blue> Table 2 : Detail of Assignments</font></p>");
		filewrite(tempFile, "<table>");
		filewrite(tempFile, "<tr><th>Line</th><th>HH name</th><th>HH SIZE</th><th>COMPOUND NAME</th><th>COMPOUND PHONE</th><th>HH Phone</th><th>Settlement</th><th>Address</th><th>Others numbers</th><th>Interviewer</th></tr>");
		do varying n= 1 while n <= visualvalue(XTOTAL) 
			
			filewrite(tempFile, "<tr>");
			filewrite(tempFile, "<td>%d</td>", n);
			filewrite(tempFile, "<td>%s</td>", strip(XNAME(n)));
//SARR			filewrite(tempFile, "<td>%d</td>", visualvalue(XSIZE(n)));
//SARR			filewrite(tempFile, "<td>%s</td>", XSTRUCNAME(n));
//SARR			filewrite(tempFile, "<td>%s</td>", strip(XCOMP_OWNER_CONTACT(n)));
//SARR			filewrite(tempFile, "<td>%d</td>", visualvalue(XPHONE(n)));
//SARR			filewrite(tempFile, "<td>%s</td>", strip(XSETTLEMENT(n)));
//SARR			filewrite(tempFile, "<td>%s</td>", strip(XLODGING(n)));
//SARR			filewrite(tempFile, "<td>%s</td>", strip(XOTHER_NUMBERS(n)));
			
			string color1;
			if visualvalue(XINTCODE(n)) = 0 then color1 = "red" else color1 = "black" endif;
			
			filewrite(tempFile, "<td><font color = %s>%s</font></td>",color1, getlabel(valset, visualvalue(XINTCODE(n))));
			filewrite(tempFile, "</tr>");			       
   		 enddo;    	
 		filewrite(tempFile, "</table>");    

	filewrite(tempFile, "</body>");
	filewrite(tempFile, "</html>");
	close(tempFile);
	if getos() in 20:29 then
		// Android - use "browse:"
		execsystem(maketext("view:%s", reportFilename));
	else
		// Windows - use "explorer.exe <filename>"
		execsystem(maketext("%sexplorer.exe %s", 
						    pathname(Windows), 
						    reportFilename));
	endif;
   
   end;
   
FUNCTION saveAssignment()
	//this function save Assignment and write the Assignments in an external file name follow.dat
	close(FOLLOW_DICT);
	setfile(FOLLOW_DICT, "../REF/follow.dat");

	 x = accept("Valider les changements effectués?","Oui","Non","Retourner et corriger");
	 if x = 1 then	
		savepartial();

		do i = 1 while i<= XTOTAL
			FCLUSTER=XCLUSTER;
			FNUMBER=visualvalue(XNUMBER(i));
			// FSTRUCTNUM=visualvalue(XSTRUCTNUM(i));
			FNAME=XNAME(i);
//SARR			FSTRUCNAME=XSTRUCNAME(i);
//SARR			FLODGING=XLODGING(i);
			FINTCODE=visualvalue(XINTCODE(i));
//SARR			FLONGITUDE=visualvalue(XLONGITUDE(i));
//SARR			FLATITUDE=visualvalue(XLATITUDE(i));
			FLGA=visualvalue(XLGA);
			FDISTRICT=visualvalue(XDISTRICT);
			FWARD=visualvalue(XWARD(i));
//SARR			FSETTLEMENT= XSETTLEMENT(i);
//SARR			FF_TYPE= XF_TYPE(i);
//SARR			FMAIN_USE= XMAIN_USE(i);
//SARR			FOTHER_NUMBERS= XOTHER_NUMBERS(i);
//SARR			FPHONE=visualvalue(XPHONE(i));
			writecase(FOLLOW_DICT);
		enddo;
		 HHAssigned();
		stop(1);
		
	elseif x = 2 then
		stop(1)
	elseif x = 3 then
		reenter XINTCODE(1)
	endif;
END;

FUNCTION onstop()
	saveAssignment();
end;

function string color(xx)
	if xx = 1 then
		color = "red"
	else
		color = "black"
	endif;
end;	

function AddHousehold()
	//this function is to add household that has not been taken during the listing
	numeric hhx = tonumber(prompt("Combien de ménages souhaitez-vous ajouter ?",numeric));
	if hhx<=0 then 
		reenter;
	else
		inc(xtotal,hhx);
		advance to XNUMBER(xtotal-hhx+1);
	endif;
end;

function userbase()
	userbar(clear);
	userbar(add button, "ajout ménages", AddHousehold());
	userbar(show);
end;

{
PROC XCLUSTER_CO

preproc
	if XA10 has XNUMBER then //we test if the line is in the initial sample
		$ = CLUSTER_CO(XNUMBER);
	elseif listCO.length() = 1 then
		$ = listCO(1);
		noinput;
	endif;
	
ONFOCUS
	if visualvalue(XLONGITUDE) in 0,notappl then
		protect($,false); // this is for added households on the field
	else
		protect($,true);
	endif;

	if listCO.length() = 1 then
		$ = listCO(1);

	elseif visualvalue($) in notappl, default or listCO.seek(visualvalue($)) = 0 then
		y = listCO.show("Please select the CLUSTER_CO of this new household");
		if y = 0 then 
			errmsg("You selected nothing, please select the settlement before continue");
			reenter XSETTLEMENT;
		else
			$ = listCO(y);
		endif;
	endif;

postproc
	
	if listCO.seek($) = 0 then
		errmsg("The CLUSTER_CO %d is not in the sample, please select the right CLUSTER_CO",$);
		reenter;
	endif;
		
	
PROC XCLUSTER_SG

preproc

	if XA10 has XNUMBER then //we test if the line is in the initial sample
		$ = CLUSTER_SG(XNUMBER);
	elseif listSG.length() = 1 then
		$ = listSG(1);
		noinput;
	endif;
	
ONFOCUS
	if visualvalue(XLONGITUDE) in 0,notappl then
		protect($,false); // this is for added households on the field
	else
		protect($,true);
	endif;

	if listSG.length() = 1 then
		$ = listSG(1);
		
	elseif visualvalue($) in notappl, default or listSG.seek(visualvalue($)) = 0 then
		y = listSG.show("Please select the CLUSTER_SG of this new household");
		if y = 0 then 
			errmsg("You selected nothing, please select the settlement before continue");
			reenter XCLUSTER_CO;
		else
			$ = listSG(y); 
		endif;
	endif;

postproc
	
	if listSG.seek($) = 0 then
		errmsg("The CLUSTER_SG %d is not in the sample, please select the right CLUSTER_SG",$);
		reenter;
	endif;
PROC XEA_SGMT

PREPROC
	if XA10 has XNUMBER then //we test if the line is in the initial sample
		$ = STRIP(EA_SGMT(XNUMBER));
	elseif listSGMT.length() = 1 then
		$ = listSGMT(1);
		noinput;
	endif;
	
ONFOCUS
	if visualvalue(XLONGITUDE) in 0,notappl then
		protect($,false); // this is for added households on the field
	else
		protect($,true);
	endif;

	if listSGMT.length() = 1 then
		$ = listSGMT(1);

	elseif $ = "" or listSGMT.seek(strip($)) = 0 then
		y = listSGMT.show("Please select the EA_segment of this new household");
		if y = 0 then 
			errmsg("You selected nothing, please select the EA_segment before continue");
			reenter XCLUSTER_SG;
		else
			$ = listSGMT(y);
		endif;
	endif;

postproc
	
	if listSGMT.seek(strip($)) = 0 then
		errmsg("The EA_segment %s is not in the sample, please select the right EA_segment",strip($));
		reenter;
	endif;


PROC XSTRUCTNUM

preproc
	// if visualvalue($) = notappl then 
		// $ = 9999; //this is to recognize households added on the field during the survey
	// endif;
onfocus
	//this test that the field have a valid value and then protect it 
	if visualvalue(XLONGITUDE) in 0,notappl then 
		protect($,false)
	else
		protect($,true)
	endif;}
//SARRPROC XF_TYPE
//SARR
// ONFOCUS
	// if visualvalue(XLONGITUDE) in 0,notappl then
		// protect($,false); // this is for added households on the field
	// else
		// protect($,true);
	// endif;
// PROC XLATITUDE
// preproc
	// if visualvalue(XLONGITUDE) in 0,notappl then
		// $ = 0;
	// endif;
	
// onfocus
	// //this test that the field have a valid value and then protect it 
	// if !special(visualvalue($)) then 
		// protect($,true)
	// else
		// protect($,false)
	// endif;
// PROC XLODGING

// onfocus
	// if visualvalue(XLONGITUDE) in 0,notappl  then
		// protect($,false); // this is to allow to enter place when supervisor manually add news households on the field
	// else
		// protect($,true);
	// endif;
// PROC XLONGITUDE
// preproc
	// if visualvalue(XLONGITUDE) in 0,notappl then
		// $ = 0;
	// endif;
	
// onfocus
	// //this test that the field have a valid value and then protect it 
	// if !special(visualvalue($)) then 
		// protect($,true)
	// else
		// protect($,false)
	// endif;
// PROC XMAIN_USE
// ONFOCUS
	// if visualvalue(XLONGITUDE) in 0,notappl then
		// protect($,false); // this is for added households on the field
	// else
		// protect($,true);
	// endif;
// PROC XOTHER_NUMBERS
// ONFOCUS
	// if visualvalue(XLONGITUDE) in 0,notappl then
		// protect($,false); // this is for added households on the field
	// else
		// protect($,true);
	// endif;
// PROC XPHONE

// onfocus
	// //this test that the field have a valid value and then protect it 
	// if !special(visualvalue($)) then 
		// protect($,true)
	// else
		// protect($,false)
	// endif;


// postproc
	
	// if curocc() = XTOTAL then
		// saveAssignment();
	// endif;
// PROC XSETTLEMENT

// PREPROC
	// // if !visualvalue(XLONGITUDE) in 0,notappl then //this is for added households
		// // SKIP to XPHONE;
	// // endif;
	
	
	// {if XA10 has XNUMBER then //we test if the line is in the initial sample
		// $ = strip(SETTLEMENT(XNUMBER));
	// elseif listsettle.length() = 1 then
		// $ = listsettle(1);
		// noinput;
	// endif;}
	// if visualvalue(XLONGITUDE) in 0,notappl and strip($) = "" then //we test if the line is in the initial sample
		// if listsettle.length() = 1 then
			// $ = listsettle(1);
			// noinput;
		// endif;
	// endif; 
// ONFOCUS
	// if visualvalue(XLONGITUDE) in 0,notappl then
		// protect($,false); // this is for added households on the field
	// else
		// protect($,true);
	// endif;

	// if listsettle.length() = 1 then
		// $ = listsettle(1);
		
	// elseif $ = "" or listsettle.seek(strip($)) = 0 then
		// y = listsettle.show("Veuillez sélectionner le lieu d'établissement de ce nouveau ménage");
		// if y = 0 then 
			// errmsg("Vous n'avez rien sélectionné, veuillez sélectionner la colonie avant de continuer");
			// reenter XLATITUDE;
		// else
			// $ = listsettle(y);
		// endif;
	// endif;

// postproc
	
	// if listsettle.seek(strip($)) = 0 then
		// errmsg("The settlement %s is not in the sample, please select the right settlement",strip($));
		// reenter;
	// endif;
		
	
// PROC XSIZE

// ONFOCUS
	// if visualvalue(XLONGITUDE) in 0,notappl then
		// protect($,false); // this is for added households on the field
	// else
		// protect($,true);
	// endif;
// PROC XSTRUCNAME

// onfocus
	// if visualvalue(XLONGITUDE) in 0,notappl then
		// protect($,false); // this is to allow to enter name when supervisor manually add news households on the field
	// else
		// protect($,true);
	// endif;
PROC XWARD
//SARR
preproc
	// if visualvalue(XLONGITUDE) in 0,notappl and special(visualvalue($))  then //we test if the line is in the initial sample
		// $ = $(1); //we assume that in one EA there is only one WARD, so for households added on the field we use the same WARD 
	// endif; 
PROC SAMPLE_FF

PROC SAMPLE_QUES

PREPROC
	CLOSE(INFO_SAMPLE_DICT);
	//setfile(INFO_SAMPLE_DICT,"../REF/sampleinfo.csdb");
	
PROC XCLUSTER

onfocus
	//this test that the field have a valid value and then protect it 
	if !special(visualvalue($)) then 
		protect($,true)
	else
		protect($,false)
	endif;

	userbase();

PROC XTOTAL
Preproc
$=15;
// onfocus
	// do  i = 1 while visualvalue(XINTCODE(i)) >=0
		// //we want to have the total number of filled lines in the roster that correspond to the sample size of the cluster
	// enddo;
	// XTOTAL = i-1;
	
	// //this test that the field have a valid value and then protect it 
	// if !special(visualvalue($)) then 
		// protect($,true)
	// else
		// protect($,false)
	// endif;

// postproc
	
	// // we create the list of settlement, clusterCO, clusterSG and EA_sgmt to allow the supervisor to select when he add a new household
	// listsettle.CLEAR(); listCO.clear(); listSG.clear(); listSGMT.clear();
	

	// do i = 1 while i<=XTOTAL 
// //SARR		// IF listsettle.seek(STRIP(XSETTLEMENT(i))) = 0 then
			// // listsettle.add(STRIP(XSETTLEMENT(i)));
		// // endif;		
	// enddo;

	{forcase INFO_SAMPLE_DICT where XA08 = XCLUSTER do
		for i in INFO_SAMPLE_REC where XA10 > 0 do
			IF listsettle.seek(STRIP(SETTLEMENT)) = 0 then
				listsettle.add(STRIP(SETTLEMENT));
			endif;
			
			IF listCO.seek(CLUSTER_CO) = 0 then
				listCO.add(CLUSTER_CO);
			endif;
			
			IF listSG.seek(CLUSTER_SG) = 0 then
				listSG.add(CLUSTER_SG);
			endif;
			
			IF listSGMT.seek(STRIP(EA_SGMT)) = 0 then
				listSGMT.add(STRIP(EA_SGMT));
			endif;
		endfor;
		
	endfor;}

	//loadcase(INFO_SAMPLE_DICT,XCLUSTER);
PROC XSUP

onfocus
	//this test that the field have a valid value and then protect it 
	if !special(visualvalue($)) then 
		protect($,true)
	else
		protect($,false)
	endif;
PROC XLGA
onfocus
	//this test that the field have a valid value and then protect it 
	if !special(visualvalue($)) then 
		protect($,true)
	else
		protect($,false)
	endif;
PROC XDISTRICT
onfocus
	//this test that the field have a valid value and then protect it 
	if !special(visualvalue($)) then 
		protect($,true)
	else
		protect($,false)
	endif;
PROC XNUMBER

preproc
	$ = curocc();
PROC XNAME
Preproc
skip to XINTCODE;
//onfocus
//SARR	if visualvalue(XLONGITUDE) in 0,notappl then
//SARR		protect($,false); // this is to allow to enter name when supervisor manually add news households on the field
//SARR	else
	//SARR	protect($,true);
//SARR	endif;
PROC XINTCODE
PREPROC
	IF VISUALVALUE($) > 0 and curocc() < xtotal then
		noinput;
	endif;
	
onfocus
	//display dynamic valueset of the interviewers of this supervisor
	
	valset.clear();
	valset.add("Pas encore assigné",0,textcolor := color(visualvalue($) = 0));
	do i = 0 while i<= 9 //in case of a maximum of 9 interviewers per team
		if getlabel(USERCODE,XSUP+i) <> "" then
			valset.add(getlabel(USERCODE,XSUP+i),XSUP+i,textcolor := color(visualvalue($) = XSUP+i));
		endif;
	enddo;

	setvalueset($,valset);

POSTPROC
	//Savepartial();
