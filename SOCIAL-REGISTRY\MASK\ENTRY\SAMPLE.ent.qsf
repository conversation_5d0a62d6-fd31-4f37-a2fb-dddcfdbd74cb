﻿---
fileType: Question Text
version: CSPro 7.7
languages:
  - name: EN
    label: English
styles:
  - name: Normal
    className: normal
    css: |
      font-family: Arial;font-size: 16px;
  - name: Instruction
    className: instruction
    css: |
      font-family: Arial;font-size: 14px;color: #0000FF;
  - name: Heading 1
    className: heading1
    css: |
      font-family: Arial;font-size: 36px;
  - name: Heading 2
    className: heading2
    css: |
      font-family: Arial;font-size: 24px;
  - name: Heading 3
    className: heading3
    css: |
      font-family: Arial;font-size: 18px;
questions:
  - name: SAMPLE_DICT.XCLUSTER
    conditions:
      - questionText:
          EN: |
            <p>Cluster number</p>
  - name: SAMPLE_DICT.XTOTAL
    conditions:
      - questionText:
          EN: |
            <p>Total households in sample</p>
  - name: SAMPLE_DICT.XSUP
    conditions:
      - questionText:
          EN: |
            <p>Supervisor code</p>
  - name: SAMPLE_DICT.XDISTRICT
    conditions:
      - questionText:
          EN: |
            CODE WILAYA<p></p>
  - name: SAMPLE_DICT.XLGA
    conditions:
      - questionText:
          EN: |
            CODE REGION<p></p>
  - name: SAMPLE_DICT.XNUMBER
    conditions:
      - questionText:
          EN: |
            <p>Sequential sampled number of household</p>
  - name: SAMPLE_DICT.XSTRUCNAME
    conditions:
      - questionText:
          EN: |
            <p>Enter the compound name</p><p><i><font color="#ff00ff"><b>Line&nbsp;&nbsp;</b><b>~~XNUMBER~~</b></font></i></p><p></p>
  - name: SAMPLE_DICT.XNAME
    conditions:
      - questionText:
          EN: |
            <p>Name of household head</p><p><i><font color="#ff00ff"><b>Line&nbsp;&nbsp;</b><b>~~XNUMBER~~</b></font></i><br></p>
  - name: SAMPLE_DICT.XLODGING
    conditions:
      - questionText:
          EN: |
            <p>Place of lodging</p><p><i><font color="#ff00ff"><b>Line&nbsp;&nbsp;</b><b>~~XNUMBER~~</b></font></i><br></p>
  - name: SAMPLE_DICT.XINTCODE
    conditions:
      - questionText:
          EN: |
            <p>Select Interviewer assigned to household of&nbsp;<b><font color="#0000ff"> ~~XNAME~~</font></b></p><p><i><font color="#ff00ff"><b style="">Line&nbsp;&nbsp;</b><b style="">~~XNUMBER~~</b></font></i></p>
  - name: SAMPLE_DICT.XLONGITUDE
    conditions:
      - questionText:
          EN: |
            <p>Longitude</p><p><i><font color="#ff00ff"><b>Line&nbsp;&nbsp;</b><b>~~XNUMBER~~</b></font></i><br></p>
  - name: SAMPLE_DICT.XLATITUDE
    conditions:
      - questionText:
          EN: |
            <p>Latitude</p><p><i><font color="#ff00ff"><b>Line&nbsp;&nbsp;</b><b>~~XNUMBER~~</b></font></i><br></p>
  - name: SAMPLE_DICT.XWARD
    conditions:
      - questionText:
          EN: |
            <p>Enter the ward code</p><p></p><p><b style="color: rgb(255, 0, 255); font-style: italic;">Line&nbsp;&nbsp;</b><b style="color: rgb(255, 0, 255); font-style: italic;">~~XNUMBER~~</b></p><p></p>
  - name: SAMPLE_DICT.XSETTLEMENT
    conditions:
      - questionText:
          EN: |
            <p>Enter the settlement name</p><p></p><p><b style="color: rgb(255, 0, 255); font-style: italic;">Line&nbsp;&nbsp;</b><b style="color: rgb(255, 0, 255); font-style: italic;">~~XNUMBER~~</b></p>
  - name: SAMPLE_DICT.XF_TYPE
    conditions:
      - questionText:
          EN: |
            <p>Enter the type of house </p><p><i>(Example :&nbsp;Compound/Structure, Landmark, etc.)</i></p><p></p><p><b style="color: rgb(255, 0, 255); font-style: italic;">Line&nbsp;&nbsp;</b><b style="color: rgb(255, 0, 255); font-style: italic;">~~XNUMBER~~</b></p><p></p>
  - name: SAMPLE_DICT.XMAIN_USE
    conditions:
      - questionText:
          EN: |
            <p>Enter the main use of house</p><p><i>(Example :&nbsp;Residential, Other, etc.)</i></p><p></p><p><b style="color: rgb(255, 0, 255); font-style: italic;">Line&nbsp;&nbsp;</b><b style="color: rgb(255, 0, 255); font-style: italic;">~~XNUMBER~~</b></p><p></p>
  - name: SAMPLE_DICT.XOTHER_NUMBERS
    conditions:
      - questionText:
          EN: |
            <p>Enter the other numbers of house</p><p><i>(Example : Plus code-F878+XVV)</i></p><p></p><p><b style="color: rgb(255, 0, 255); font-style: italic;">Line&nbsp;&nbsp;</b><b style="color: rgb(255, 0, 255); font-style: italic;">~~XNUMBER~~</b></p><p></p>
  - name: SAMPLE_DICT.XSIZE
    conditions:
      - questionText:
          EN: |
            <p>Size of household</p><p><i><font color="#ff00ff"><b>Line&nbsp;&nbsp;</b><b>~~XNUMBER~~</b></font></i><br></p>
  - name: SAMPLE_DICT.XPHONE
    conditions:
      - questionText:
          EN: |
            <p>Phone number</p><p><i><font color="#ff00ff"><b>Line&nbsp;&nbsp;</b><b>~~XNUMBER~~</b></font></i><br></p>
  - name: SAMPLE_DICT.XCLUSTER_CO
    conditions:
      - questionText:
          EN: |
            <p>Enter the cluster_co</p><p><br></p><p></p><p><i><font color="#ff00ff"><b>Line&nbsp;&nbsp;</b><b>~~XNUMBER~~</b></font></i><br></p><p><br></p><p></p>
  - name: SAMPLE_DICT.XCLUSTER_SG
    conditions:
      - questionText:
          EN: |
            <p>enter the cluster_sg</p><p><b style="color: rgb(255, 0, 255); font-style: italic;">Line&nbsp;&nbsp;</b><b style="color: rgb(255, 0, 255); font-style: italic;">~~XNUMBER~~</b><p></p></p>
  - name: SAMPLE_DICT.XEA_SGMT
    conditions:
      - questionText:
          EN: |
            <p>enter the EA_Segment</p><p><p><b style="color: rgb(255, 0, 255); font-style: italic;">Line&nbsp;&nbsp;</b><b style="color: rgb(255, 0, 255); font-style: italic;">~~XNUMBER~~</b><br></p><p><br></p></p>
  - name: SAMPLE_DICT.XHHNUM
    conditions:
      - questionText:
          EN: |
            <p>Household number from the listing</p>
  - name: SAMPLE_DICT.XHHSIZE
    conditions:
      - questionText:
          EN: |
            <p>Enter the household size</p><p></p><p><b style="color: rgb(255, 0, 255); font-style: italic;">Line&nbsp;&nbsp;</b><b style="color: rgb(255, 0, 255); font-style: italic;">~~XNUMBER~~</b></p><p></p>
  - name: SAMPLE_DICT.XSETTLEMENT_CODE
    conditions:
      - questionText:
          EN: |
            <p>Enter the settlement code</p><p></p><p><b style="color: rgb(255, 0, 255); font-style: italic;">Line&nbsp;&nbsp;</b><b style="color: rgb(255, 0, 255); font-style: italic;">~~XNUMBER~~</b><br></p><p></p>
  - name: SAMPLE_DICT.XSTRUCTNUM
    conditions:
      - questionText:
          EN: |
            <p>Enter the compound number of this added household</p><p><p><i><font color="#ff00ff"><b>Line&nbsp;&nbsp;</b><b>~~XNUMBER~~</b></font></i><br></p><p><br></p></p>
...
