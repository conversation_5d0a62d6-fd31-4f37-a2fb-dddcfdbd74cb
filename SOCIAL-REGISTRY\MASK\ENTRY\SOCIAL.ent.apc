﻿{==========================================================================
 SOCIAL.ent.apc - Main Application Logic File
 ==========================================================================
 Description: This file contains the application logic for the Algerian National
              Consumption Survey data collection application.

 Version: 1.0
 Created: 2025

 This application handles:
 - Household roster management
 - Demographic data collection
 - Education information
 - Health status and healthcare access
 - Housing conditions and assets
 - Employment and economic activities
 - GPS location capture

 The application implements complex validation rules, skip patterns,
 and dynamic questionnaire flow based on respondent characteristics.
==========================================================================}

PROC GLOBAL
{==========================================================================
 GLOBAL SECTION
 ==========================================================================
 This section defines global variables, arrays, and functions used throughout
 the application. These elements are accessible from any procedure in the file.
==========================================================================}

{ Global Variables }
numeric tot;        { Total counter for various calculations }
numeric OK;         { Used for storing user confirmation responses }
numeric i, j, n, p; { Loop counters and indices }
numeric err;        { Error flag }
numeric x, e;       { General purpose numeric variables }
numeric tim;        { Time-related variable }
numeric maxmemb;    { Maximum number of household members }

{ GPS-related variables }
numeric id_marker;   { ID for map marker }
numeric initialLat;  { Initial latitude coordinate }
numeric initialLon;  { Initial longitude coordinate }

{ Arrays for dynamic value sets }
array codes(30);            { Array to store numeric codes for value sets }
alpha(900) strnotes;        { Storage for notes text }
array alpha(40) labels(30); { Array to store labels for value sets }

{ Map object for GPS functionality }
map mymap;

{ Value set for dynamic dropdown menus }
valueset MyValueset;

{==========================================================================
 GLOBAL FUNCTIONS
 ==========================================================================
 These functions provide reusable functionality throughout the application.
==========================================================================}

{--------------------------------------------------------------------------
 Function: closeMap

 Purpose: Hides the map object when GPS capture is complete or cancelled

 Parameters:
   m - The map object to hide
--------------------------------------------------------------------------}
function closeMap(map m)
    m.hide();
end;

{--------------------------------------------------------------------------
 Function: retakeGPS

 Purpose: Allows the user to retake GPS coordinates by hiding the current
          map and returning to the GPS capture question

 Parameters:
   m - The map object to hide
--------------------------------------------------------------------------}
function retakeGPS(map m)
	m.hide();
	A10gps = 2;
    reenter A10GPS;
end;

{--------------------------------------------------------------------------
 Function: clean_labels

 Purpose: Resets the codes and labels arrays used for dynamic value sets
          This is called before populating these arrays for dropdown menus
--------------------------------------------------------------------------}
function clean_labels();
    numeric z;
    do z = 1 while z <= 30
      codes(z)  = notappl;
      labels(z) = "";
    enddo;
end;

{--------------------------------------------------------------------------
 Function: endmess

 Purpose: Displays end of survey message and handles user response

 Returns: Boolean - true if user chooses to review the questionnaire,
          false if user chooses to finalize
--------------------------------------------------------------------------}
function endmess();
    { Returns true if response is REVIEW }
    endmess = ( demode() = add &
               accept("FIN DE L'ENQUETE MENAGE",
                      "REVISITER LE QUESTIONNAIRE",
                      "FINALISER") <> 2);
end;
{--------------------------------------------------------------------------
 Function: mapClicked

 Purpose: Event handler for map clicks during GPS capture
          Validates if the clicked location is within acceptable distance
          from the initial position and updates coordinates if valid

 Returns: None, but updates global variables A10A and A10B with coordinates
--------------------------------------------------------------------------}
function mapClicked()
    numeric lat = mymap.getLastClickLatitude();
    numeric lon = mymap.getLastClickLongitude();
	numeric d = gps(distance,lat,lon,initialLat,initialLon);

	if d <= 30 then // we move the point only if he is not too far from the initial position
		mymap.setMarkerLocation(id_marker, lat, lon);
		A10A = lon;  // Store longitude
		A10B = lat;  // Store latitude
	else
		errmsg("Vous êtes trop loin de la position initiale (%d mètres), pour éviter toute erreur nous ne nous y déplacerons pas",d);
	endif;
end;

{--------------------------------------------------------------------------
 Function: adjustPoint

 Purpose: Sets up the map click event handler for GPS coordinate adjustment

 Parameters:
   m - The map object to configure
--------------------------------------------------------------------------}
function adjustPoint(map m);
	m.setOnClick(mapClicked());
end;

{--------------------------------------------------------------------------
 Function: TestValidityName

 Purpose: Validates person names and text entries according to survey rules
          1. Ensures the name is not empty
          2. Checks that the name has at least 2 characters
          3. Verifies the name doesn't start with invalid characters

 Parameters:
   name - The string to validate

 Returns: None, but will force re-entry if validation fails
--------------------------------------------------------------------------}
function TestValidityName(string name)
	if strip(name) = "" then
		errmsg("Le nom/texte ne peut pas être vide");
		reenter; //not allow to continue without fill a name
	elseif length(strip(name)) < 2 then
		errmsg("Le nom/texte %s est trop court",strip(name));
		reenter;
	elseif pos(name[1:1], " 0123456789@,;:!?/#-+*.=()\_{}°[]")>0 then //$[1:1] extract the first character of the name. A name cannot start by one of this letter"0123456789@,;:!?/#-+*.=()\_{}°"
		errmsg("Le nom/texte ne peut pas commencer par %s",name[1:1]);
		reenter;
	endif;
end;

{--------------------------------------------------------------------------
 Function: Onstop

 Purpose: Handles application exit with options to save progress
          This function is called when the user attempts to exit the application

 Returns: None, but may save partial data and/or exit the application
--------------------------------------------------------------------------}
function Onstop()
	ok = accept(TR("Que veux-tu ?"), tr("Sauvegarde"),tr("Annuler"),tr("Quitter sans sauvegarder"));

	if ok in 3 then
		stop(1);  // Exit without saving
	elseif ok = 1 then
		savepartial();  // Save partial data
		stop(1);        // Then exit
	elseif ok in 0, 2 then
		reenter;  // Cancel exit and continue
	endif;
end;

{==========================================================================
 MAIN APPLICATION PROCEDURES
 ==========================================================================
 The following procedures implement the application logic for each section
 of the questionnaire, including validation rules and skip patterns.
==========================================================================}

{--------------------------------------------------------------------------
 Procedure: SOCIAL_FF

 Purpose: Entry point for the application file format
--------------------------------------------------------------------------}
PROC SOCIAL_FF

{--------------------------------------------------------------------------
 Procedure: SOCIAL_LEVEL

 Purpose: Handles application level events
--------------------------------------------------------------------------}
PROC SOCIAL_LEVEL
stop(1);

{--------------------------------------------------------------------------
 Procedure: SECTA_FORM

 Purpose: Main form section A - Identification and basic information
          Handles partial save recovery and questionnaire resumption
--------------------------------------------------------------------------}
PROC SECTA_FORM
preproc
	{ Check if this is a partially completed questionnaire and offer to resume }
	if ispartial() then
		ok = accept("C'est un questionnaire partiel, voulez-vous aller à la dernière position ?","Oui","Non");
		if ok = 1 then
			advance to getsymbol(savepartial);
		endif;
	endif;

PROC A08
preproc
	BADCLOSED = 0;

	if visualvalue($) = notappl then
		$ = tonumber(sysparm("cluster"));
	endif;

	if !special(visualvalue($)) then
		protect($,true)
	else
		protect($,false)
	endif;
PROC A10
preproc

	if visualvalue($) = notappl then
		$ = tonumber(sysparm("HHnumber"));
	endif;

	if !special(visualvalue($)) then
		protect($,true)
	else
		protect($,false)
	endif;
PROC A02
preproc
	if visualvalue($) = notappl then
		$ = tonumber(sysparm("interviewer"));
	endif;
PROC A03
preproc
	$ = int(A02/10)*10;
PROC A05
preproc
	if visualvalue($) = notappl then
		$ = tonumber(sysparm("REGION"));
	endif;

	if !special(visualvalue($)) then
		protect($,true)
	else
		protect($,false)
	endif;


onfocus
PROC A06
preproc
	if visualvalue($) = notappl then
		$ = tonumber(sysparm("WILAYA"));
	endif;

	if !special(visualvalue($)) then
		protect($,true)
	else
		protect($,false)
	endif;
PROC A07
preproc
	if visualvalue($) = notappl then
		$ = tonumber(sysparm("WARD"));
	endif;

	if !special(visualvalue($)) then
		protect($,true)
	else
		protect($,false)
	endif;
PROC A09
preproc
	if $ = "" then
		$ = sysparm("SETTLEMENT");
	endif;

{==========================================================================
 GPS FUNCTIONALITY
 ==========================================================================
 This section implements GPS coordinate capture for household location.

 The application uses CSPro's built-in GPS and mapping capabilities to:
 1. Capture the initial GPS coordinates
 2. Display them on an interactive map
 3. Allow adjustment of the location if needed
 4. Validate that adjustments are within a reasonable distance

 This functionality is critical for spatial analysis of survey data and
 for field team management and supervision.
==========================================================================}

{--------------------------------------------------------------------------
 Procedure: A10GPS

 Purpose: Captures GPS coordinates of the household location
          Displays an interactive map for verification and adjustment

 Flow:
 1. If user chooses to capture GPS (option 1), the system activates GPS
 2. Initial coordinates are captured and stored
 3. A map is displayed centered on these coordinates
 4. A marker is placed at the location
 5. User can close the map, retake coordinates, or adjust the position
 6. Adjustments are validated to be within 30 meters of initial position
--------------------------------------------------------------------------}
PROC A10GPS
preproc
	if $ = 1 then
		// Get GPS coordinates
		gps(coordinates, A10B, A10A);  // A10B = latitude, A10A = longitude
		initialLat = A10B;             // Store initial latitude for validation
		initialLon = A10A;             // Store initial longitude for validation

		// Create map
		mymap.create();                // Initialize map object
		mymap.setCenter(A10B, A10A);   // Center map on captured coordinates
		mymap.setZoom(18);             // Set zoom level for appropriate detail

		// Add marker
		id_marker = mymap.addMarker(A10B, A10A);  // Place marker at coordinates

		// Show map
		mymap.show();                  // Display the map to the user

		// Add interactive buttons
		mymap.addButton("Fermer", closeMap(mymap));       // Close map button
		mymap.addButton("Reprendre", retakeGPS(mymap));   // Retake GPS button
		mymap.addButton("Ajuster", adjustPoint(mymap));   // Adjust location button
	endif;

PROC START_TIME
preproc
	if visualvalue($) = notappl then
		$ = timestamp();
	endif;
PROC A01
preproc
	if special(visualvalue($)) then
		$ = sysdate("YYYYMMDD");
	endif;

	if !(visualvalue($) in 20250513:20251230) then
		protect($,false);
	endif;

PROC A16
POSTPROC

	if $ = 2 then
		skip to END_FORM
	endif;
PROC A13
TestValidityName($);

{==========================================================================
 HOUSEHOLD ROSTER SECTION
 ==========================================================================
 This section handles the collection of demographic information for each
 household member, including names, relationships, age, and sex.

 The roster is a critical component as it establishes the household
 composition that will be referenced throughout the questionnaire.
==========================================================================}

{--------------------------------------------------------------------------
 Procedure: QHLINE

 Purpose: Sets the line number for each household member
          This is used to uniquely identify each person in the household
--------------------------------------------------------------------------}
PROC QHLINE
preproc
  $ = curocc();  // Set to current occurrence (row number)

{--------------------------------------------------------------------------
 Procedure: QHFIRSTN

 Purpose: Captures and validates the first name of each household member
--------------------------------------------------------------------------}
PROC QHFIRSTN
  { Check that response is alphabetic and starts in the first column }
  TestValidityName(QHFIRSTN);

{--------------------------------------------------------------------------
 Procedure: QHLASTN

 Purpose: Captures and validates the last name of each household member
--------------------------------------------------------------------------}
PROC QHLASTN
  TestValidityName(QHLASTN);

{--------------------------------------------------------------------------
 Procedure: QHRELAT

 Purpose: Captures the relationship of each household member to the head
          Enforces household composition rules:
          - First person must be the household head (code 1)
          - Spouse (code 2) must be in the second position
--------------------------------------------------------------------------}
PROC QHRELAT
 if curocc() = 1 <=>  $ <> 1 then
    errmsg( 0031 );  // First person must be household head
    reenter
 elseif $ = 2 & curocc() <> 2 then
    errmsg( 0033 );  // Spouse must be second person
    reenter
 endif;

{--------------------------------------------------------------------------
 Procedure: QHAGE

 Purpose: Captures and validates the age of each household member
          Performs consistency checks against birth year (QHANNEE)
--------------------------------------------------------------------------}
PROC QHAGE

if (2025-QHANNEE-1)>QHAGE then
    errmsg(" Erreur sur l'age , verifier s'il vous plait avant de continiuer");
    //reenter
endif;

if (2025-QHANNEE+1)<=QHAGE then
    errmsg(" Erreur sur l'age , verifier s'il vous plait avant de continuer");
    //reenter
endif;

{--------------------------------------------------------------------------
 Procedure: QHAGF

 Purpose: Handles age-specific questions for young children
          Skips these questions for members 5 years and older
--------------------------------------------------------------------------}
PROC QHAGF
Preproc
if QHAGE>=5 then skip to QHSEX; endif;

{--------------------------------------------------------------------------
 Procedure: QHSEX

 Purpose: Captures the sex of each household member
          Performs consistency checks for spouse's sex compared to head
--------------------------------------------------------------------------}
PROC QHSEX
 if curocc() > 1 & QHRELAT = 2 then
    if $ = $(1) then
     errmsg( 0060, $, $(1) )  // Spouse should be opposite sex of head
     select( "Sexe du chef de ménage", $(1), "Sexe du conjoint", $ );
    endif;
 endif;

{--------------------------------------------------------------------------
 Procedure: QHSM

 Purpose: Captures marital status
          Skips this question for members under 10 years old
--------------------------------------------------------------------------}
PROC QHSM
preproc
if QHAGE<10 then skip to QHMORE; endif;

{--------------------------------------------------------------------------
 Procedure: QHMORE

 Purpose: Controls whether to add more household members
          Ends the household roster group if no more members
--------------------------------------------------------------------------}
PROC QHMORE
postproc
if $=2 then endgroup; endif;

{--------------------------------------------------------------------------
 Procedure: QHMEMBER

 Purpose: Calculates the total number of household members
          This value is used throughout the questionnaire
--------------------------------------------------------------------------}
PROC QHMEMBER
preproc
  $ = totocc( QHSEC01X_FORM );  // Count total occurrences in the roster

{--------------------------------------------------------------------------
 Procedure: QHRESP

 Purpose: Creates a dynamic dropdown list of eligible household respondents
          This demonstrates a key pattern used throughout the application:
          1. Clean the value set arrays
          2. Loop through household members to find eligible ones
          3. Add eligible members to the value set with their names as labels
          4. Apply the value set to the current field
          5. Validate the selected value

 Note: This pattern is repeated for many fields that need to select a
       household member based on specific eligibility criteria
--------------------------------------------------------------------------}
PROC QHRESP
onfocus
  { Initialize the dynamic value set }
  clean_labels();
  j = 0;

  { Loop through all household members }
  do i = 1 while i <= QHMEMBER
    { Only include members aged 12-98 years }
    if QHAGE(i) in 12:98 then
      codes(j)  = i;  // Store the member's line number as the code
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );  // Use full name as label
      j = j + 1;
    endif;
  enddo;

  { Apply the dynamic value set to the current field }
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1:QHMEMBER then
    errmsg( 0017 );  // Error: Selected person is not a household member
    reenter
  endif;



PROC QHSEC02_FORM
Preproc
endsect;
PROC EM00
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
{==========================================================================
 HOUSING SECTION
 ==========================================================================
 This section collects information about the household's dwelling,
 including type, ownership status, construction materials, amenities,
 and number of rooms.
==========================================================================}

{--------------------------------------------------------------------------
 Procedure: H00

 Purpose: Selects the respondent for the housing section
          Creates a dynamic dropdown of eligible household members
--------------------------------------------------------------------------}
PROC H00
onfocus
  { Create dynamic value set of eligible respondents (aged 12+) }
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Validate that selected respondent is a household member }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );  // Error: Not a valid household member
  endif;
savepartial();  // Save progress after selecting respondent

{--------------------------------------------------------------------------
 Procedure: H01

 Purpose: Captures dwelling type
          Allows interviewer to add notes for "Other" responses
--------------------------------------------------------------------------}
PROC H01
if $ = 9 then editnote() endif;  // Allow notes for "Other" dwelling type

{--------------------------------------------------------------------------
 Procedure: H02

 Purpose: Captures dwelling ownership status
          Implements skip pattern based on H02A (ownership document)
--------------------------------------------------------------------------}
PROC H02
Preproc
 if H02A=1 then skip to H03; endif;  // Skip if ownership document exists

{--------------------------------------------------------------------------
 Procedure: H03

 Purpose: Captures number of households sharing the dwelling
          Implements skip pattern based on response
--------------------------------------------------------------------------}
PROC H03
if H03 > 0 then skip to H05 endif;  // Skip to H05 if other households share

{--------------------------------------------------------------------------
 Procedure: H05

 Purpose: Captures main construction material of dwelling
--------------------------------------------------------------------------}
PROC H05

{--------------------------------------------------------------------------
 Procedure: H06

 Purpose: Captures dwelling occupancy status
          Implements skip patterns based on response
--------------------------------------------------------------------------}
PROC H06
if $ in 3,4 then skip to H10 endif;  // Skip to H10 for certain occupancy types
if $ in 5,6 then skip to H09 endif;  // Skip to H09 for other occupancy types

{--------------------------------------------------------------------------
 Procedure: H08

 Purpose: Captures rental payment information
          Allows interviewer to add notes for "Other" responses
--------------------------------------------------------------------------}
PROC H08
if $ = 9 then editnote() endif;  // Allow notes for "Other" payment type

{--------------------------------------------------------------------------
 Procedure: H09

 Purpose: Implements skip pattern to H11
--------------------------------------------------------------------------}
PROC H09
Skip to H11;  // Skip questions about rental arrangements

{--------------------------------------------------------------------------
 Procedure: H10

 Purpose: Captures information about subsidized housing
          Implements skip pattern based on dwelling material (H05)
--------------------------------------------------------------------------}
PROC H10
Preproc
if (H05<>3 and H05<>5) then skip to H12 endif;  // Skip if not applicable materials

{--------------------------------------------------------------------------
 Procedure: H14

 Purpose: Captures number of rooms occupied by the household
          Validates that it doesn't exceed total rooms in dwelling (H13)
--------------------------------------------------------------------------}
PROC H14
if $>H13 then
  errmsg("ERREUR: Le nombre de pièces occupées par le ménage enquêté doit etre inférieur ou égal à czlui du logement, VEUILLEZ CORRIGET AVANT DE POURSUIVRE");
  reenter
endif;
PROC H15
if $=2 then skip to H17A; endif;
PROC H16
if $ = 9 then editnote() endif;
PROC H18
Preproc
if H17D =2 then skip to H19; endif;
PROC H19
Preproc
if H17D =2 then skip to H20; endif;
PROC H21
if $=9 then editnote(); endif;
PROC H22
if $=9 then editnote(); endif;
PROC H23
if $=2 then Skip to H27;  endif;
PROC H24

PROC H25
if $=2 then Skip to H27;  endif;
PROC H26

PROC H27
if $ = 9 then editnote() endif;
PROC H28B
if $=1 & H28A<>1 then
  errmsg("ALERTE: Vérifier la conhérence entre H28A et H28B avant de poursuivre");
  reenter
endif;
PROC H29
if $ = 2 then skip to H31 endif;
PROC H30
if $ = 9 then editnote(); endif;
PROC H31
if $ = 9 then editnote(); endif;
PROC H32
if $ = 9 then editnote(); endif;
PROC H34
if $ in 2,8 then skip to H36 endif;
PROC H35
if $ = 9 then editnote() endif;
PROC H36
if $ = 9 then editnote() endif;
PROC H37
 if $ = 1 then skip to H39 endif;
PROC H38
if pos("X",$) then editnote() endif;
PROC H39

{==========================================================================
 HOUSEHOLD ASSETS SECTION
 ==========================================================================
 This section collects information about household assets and durable goods.
 It captures ownership of various items including:
 - Kitchen appliances
 - Heating and cooling equipment
 - Furniture
 - Electronics and communication devices
 - Vehicles and transportation

 For each item, the questionnaire records:
 - Whether the household owns it
 - Quantity owned
 - Year of acquisition
 - Purchase price
==========================================================================}

{--------------------------------------------------------------------------
 Procedure: EQUIP_00

 Purpose: Selects the respondent for the household assets section
          Creates a dynamic dropdown of eligible household members
--------------------------------------------------------------------------}
PROC EQUIP_00
onfocus
  { Create dynamic value set of eligible respondents (aged 12+) }
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Validate that selected respondent is a household member }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();  // Save progress after selecting respondent

{--------------------------------------------------------------------------
 Procedure: EM0

 Purpose: Sets the line number for each asset in the roster
          This is used to uniquely identify each asset type
--------------------------------------------------------------------------}
PROC EM0
Preproc
$ = curocc();  // Set to current occurrence (row number)

{--------------------------------------------------------------------------
 Procedure: EM1

 Purpose: Defines the list of household assets to be enumerated
          This comprehensive list covers all major household durable goods

 Note: The asset names are in French as this is the survey language
--------------------------------------------------------------------------}
PROC EM1
Preproc
{ Kitchen appliances }
EM1(1)="CUISINIÈRE";                           // Stove
EM1(2)="FOUR ÉLECTRIQUE/ PLAQUE DE CUISSON";   // Electric oven/cooktop
EM1(3)="FOUR À MICRO-ONDES ";                  // Microwave oven
EM1(4)="RÉCHAUD À GAZ/ TRÉPIED,TABOUNA";       // Gas cooker/tripod
EM1(5)="HOTTE ASPIRANTE";                      // Range hood
EM1(6)="RÉFRIGÉRATEUR, RÉFRIG. COMBINÉ";       // Refrigerator/combo
EM1(7)="CONGÉLATEUR";                          // Freezer
EM1(8)="LAVE-VAISSELLE";                       // Dishwasher
EM1(9)="LAVE-LINGE";                           // Washing machine

{ Heating and cooling equipment }
EM1(10)="CLIMATISEUR";                         // Air conditioner
EM1(11)="RADIATEUR À GAZ DE VILLE ";           // City gas heater
EM1(12)="RADIATEUR ÉLECTRIQUE";                // Electric heater
EM1(13)= "CHAUDIÈRE/ CHAUFFAGE CENTRAL";       // Boiler/central heating
EM1(14)="VENTILATEUR";                         // Fan
EM1(15)="CHAUFFE BAIN/EAU";                    // Water heater

{ Other household appliances }
EM1(16)="ASPIRATEUR";                          // Vacuum cleaner
EM1(17)="PÉTRIN";                              // Kneading machine
EM1(18)="AUTRE BIEN ELECTROMÉNAGER (ROBOT)";   // Other appliance (robot)
EM1(19)="MACHINE À COUDRE ";                   // Sewing machine
EM1(20)="MACHINE À TRICOTER";                  // Knitting machine

{ Furniture }
EM1(21)="SALLE À MANGER";                      // Dining room set
EM1(22)="SALON ";                              // Living room set
EM1(23)="BIBLIOTHÈQUE";                        // Bookcase/library
EM1(24)="CHAMBRE À COUCHER";                   // Bedroom set

{ Electronics and entertainment }
EM1(25)="TÉLÉVISION COULEUR";                  // Color TV
EM1(26)="PARABOLE";                            // Satellite dish
EM1(27)="RÉCEPTEUR SATELLITE AVEC INTERNET";   // Satellite receiver with internet
EM1(28)="CHAINE HI FI";                        // Hi-Fi system
EM1(29)="LECTEUR VIDÉO";                       // Video player

{ Computing and communication devices }
EM1(30)="MICRO-ORDINATEUR, LAPTOP";            // Computer/laptop
EM1(31)="TABLETTE";                            // Tablet
EM1(32)="IMPRIMANTE/ SCANNER";                 // Printer/scanner
EM1(33)="MODEM";                               // Modem
EM1(34)="APPAREIL PHOTO";                      // Camera
EM1(35)="CAMÉRA";                              // Video camera
EM1(36)="TÉLÉPHONE FIXE";                      // Landline phone
EM1(37)="TÉLÉPHONE PORTABLE";                  // Mobile phone
EM1(38)="INTERPHONE";                          // Intercom
EM1(39)="CAMÉRA DE SURVEILLANCE";              // Surveillance camera

{ Vehicles and transportation }
EM1(40)="VOITURE";                             // Car
EM1(41)="CAMION";                              // Truck
EM1(42)="CAMIONNETTE";                         // Van
EM1(43)="MOTO/ SCOOTER/ VELOMOTEUR";           // Motorcycle/scooter
EM1(44)="MOBILETTE/ BICYCLETTE";               // Moped/bicycle
EM1(45)="CITERNE";                             // Tank/cistern
EM1(46)="BATEAU À MOTEUR";                     // Motorboat
EM1(47)="BARQUE ET BATEAU DE PLAISANCE";       // Boat and pleasure craft

{ Other assets }
EM1(48)="INSTRUMENT DE MUSIQUE";               // Musical instrument
PROC EM2
if $=3 then skip to next EM0; endif;
PROC EM3
if $<0 then errmsg("La valeur saisie est incorrecte"); endif;
PROC EM5
Preproc
if EM3(curocc())=2 & EM4(curocc())=2 then skip to next EM1 endif;
PROC EQUIP_01
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC QHSEC05000

PROC ED00A
preproc
$=curocc();
 if QHAGE(curocc()) < 3 then
     skip to next ED00A
 endif;
  if curocc() > QHMEMBER then
    endsect
  endif;
PROC ED00B
preproc
ED00B(curocc())= concat( strip(QHFIRSTN(curocc())), " ", strip(QHLASTN(curocc())) );
PROC ED01
Preproc
 if QHAGE(curocc()) < 3 then skip to next ED00A endif;
PROC ED02
if $(curocc())=2 then skip to ED04A endif;
PROC ED03
if $(curocc()) = 5 then editnote() endif;

if $(curocc())=5 then skip to ED19A endif;
if $(curocc())=6 then skip to next ED00A endif;
PROC ED04B
if  ((ED04A(curocc())=3 &  ED04B(curocc()) in 1:7)  or (ED04A(curocc())=4 &  ED04B(curocc()) in 1:4)  or (ED04A(curocc())=5 &  ED04B(curocc()) in 1:3) or( ED04A(curocc())=6 &  ED04B(curocc()) in 1:7)) then skip to ED06 endif;
PROC ED05
if $(curocc()) = 9 then editnote() endif;
skip to ED12;
PROC ED06
Preproc
if ED05(curocc()) = 2 and ED04A(curocc()) in 3,4,5 then skip to ED12 endif;
PROC ED07
if $(curocc())=1 then skip to ED09 endif;
PROC ED08
if $=2 then skip to ED11 endif;
if $ = 99 then editnote() endif;
PROC ED12
Preproc
// a revoir avec l'équipe
if QHAGE(curocc())<16 then skip to ED17 endif;
Postproc
if $(curocc()) in 1,2 then skip to ED14 endif;
if $(curocc()) = 4 then skip to ED17 endif;
PROC ED13
if $ = 99 then editnote() endif;
PROC ED15
savepartial();
PROC ED18
if $(curocc()) = 4 then skip to next ED00A endif;
PROC ED19A
if $ = 9 then editnote() endif;
PROC ED20
if $ = 9 then editnote() endif;
PROC ED23
if $(curocc()) = 6 then skip to ED25 endif;
PROC ED24
Preproc
if ED23(curocc()) in 1,2,3 then skip to ED26 endif;
PROC ED26
if $(curocc()) = 6 then skip to ED28 endif;
PROC ED27
Preproc
if ED26(curocc()) in 1,2,3 then skip to ED29 endif;
PROC ED29
if $(curocc())=6 then skip to ED31 endif;
PROC ED30A
Preproc
if ED29 in 1,2,3 then skip to ED32 endif;
PROC ED32
if $(curocc())=6 then skip to ED34 endif;
PROC ED33A
Preproc
if ED32(curocc()) in 1:3 then skip to ED35 endif;


PROC ED36
if $(curocc())=6 then skip to next ED00A endif;
PROC ED37
savepartial();
PROC EQUIP_02
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC SA00
preproc
$=curocc();
  if curocc() > QHMEMBER then
    endsect
  endif;

PROC QHNOO
preproc
QHNOO(curocc())= concat( strip(QHFIRSTN(curocc())), " ", strip(QHLASTN(curocc())) );
PROC SA01
if $=2 then skip to SA07 endif;
PROC SA02
    if pos( "X", $ ) then editnote() endif;

     if length(strip($)) > 4 then
       errmsg("VOUS NE POUVEZ PAS CHOISIR PLUS DE 4 REPONSES");
       reenter
     endif;
PROC SA03
if $=1 then skip to SA05 endif;
PROC SA04
if $ = 9 then editnote() endif;
PROC SA05
if $=4 then skip to SA07 endif;
PROC SA07
if $=2 then skip to SA12 endif;
PROC SA08
if pos( "X", $ ) then editnote() endif;
if length(strip($)) > 2 then
     errmsg("VOUS NE POUVEZ PAS CHOISIR PLUS DE 2 REPONSES");
    reenter
  endif;
PROC SA09
if $=2 then skip to SA12 endif;
PROC SA10
if pos( "X", $ ) then editnote() endif;
    if length(strip($)) > 2 then
    errmsg("VOUS NE POUVEZ PAS CHOISIR PLUS DE 2 REPONSES");
    reenter
  endif;

PROC SA11
if $ = 9 then editnote() endif;
PROC SA12
if $=2 then skip to SA14 endif;
PROC SA14
if $=2 then skip to SA18 endif;
PROC SA15
if $=1 then skip to SA17 endif;
PROC SA16
Preproc
if SA15(curocc())=3 then skip to SA18 endif;
Postproc
if $ = 9 then editnote() endif;
PROC SA18
if $=2 then skip to SA20 endif;
PROC SA27
if $=2 then skip to SA29 endif;
PROC SA29
if $=6 then skip to SA31 endif;
PROC SA30
Preproc
if SA29(curocc()) in 1,2,3 then skip to SA32 endif;

PROC SA32
if $=6 then skip to SA34 endif;
PROC SA33A
Preproc
if SA32(curocc()) in 1,2,3 then skip to SA35 endif;
PROC SA35
if $=6 then skip to SA37 endif;
PROC SA36
Preproc
if SA35(curocc()) in 1,2,3 then skip to SA38 endif;
PROC SA38
Preproc
 for i in QHSEC01X_FORM
  if QHAGE(i) in 15:49 then
    skip to SA39
  endif;
 enddo;

Postproc
  if !pos( "A", $ ) & !pos( "C", $ ) & !pos( "D", $ ) then skip to SA40 endif;

   if length(strip($)) > 2 then
      errmsg("VOUS NE POUVEZ PAS CHOISIR PLUS DE 2 REPONSES");
      reenter
   endif;

  if pos( "Y", $ ) & length(strip($)) > 1 then
      errmsg("VOUS NE POUVEZ PAS ASSOCIER LA REPONSE NON AVEC UNE AUTRE REPONSE");
      reenter
   endif;
PROC EQUIP_03
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC VD01
if $=2 then skip to EQUIP_04 endif;
PROC VD02

PROC QHSEC7A000

PROC VD00
preproc
  { Initialize household members' questions with information already collected or known }
 $=curocc();
  { exit section  }
  if curocc() > VD02 then
    endsect
  endif;
PROC VD03
if $=2 then skip to VD05 endif;
PROC VD04
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
skip to VD06;
PROC VD09
skip to VD11;
PROC EQUIP_04
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC ACTA0
preproc
  { Initialize household members' questions with information already collected or known }
 $ = curocc();

  if curocc() > QHMEMBER then
    endsect
  endif;
PROC QHNOP
preproc
QHNOP(curocc())= concat( strip(QHFIRSTN(curocc())), " ", strip(QHLASTN(curocc())) );  { person's name must be added to the household schedule roster }
PROC ACTA1
Preproc
 if QHAGE(curocc()) < 10 then
  skip to next ACTA0
 endif;

Postproc
if $= 1 then skip to ACTA3 endif;
savepartial();
PROC ACTA2
onfocus
  clean_labels();
 codes(0)  = 0;
 labels(0) = "CODE NON VALIDE";

 j = 1;
  do i = 1 while i <= QHMEMBER
    if QHLINE(i) <> curocc() & QHAGE(i) >= 10     then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );  { person's name must be added to the household schedule roster }
      j = j + 1;
    endif;
  enddo;
 SetValueSet( @GetSymbol(), codes, labels );

POSTPROC
if $ = 0 then
   errmsg("OPTION NON VALIDE INSCRIRE LE BON CODE DU CONJOINT OU RETOUNER A Q11") ;
   Reenter
endif;
PROC ACTA3
  if $ = 1 then skip to next ACTA0 endif;

if $ = 9 then editnote() endif;
PROC ACTA4H

for i in QHSEC08B000
 if ACTA4A(curocc())=1 or ACTA4B(curocc())=1 or ACTA4C(curocc())=1 or ACTA4D(curocc())=1 or ACTA4E(curocc())=1 or ACTA4F(curocc())=1 or ACTA4G(curocc())=1 or ACTA4H(curocc())=1 then endsect endif;
enddo;

for i in QHSEC08D000
 if ACTA4A(curocc())=2 and ACTA4B(curocc())=2 and ACTA4C(curocc())=2 and ACTA4D(curocc())=2 and ACTA4E(curocc())=2 and ACTA4F(curocc())=2 and ACTA4G=2 and ACTA4H(curocc())=2 then skip ACTD_0(i) endif;
enddo;
PROC ACTB0
preproc
  { Initialize household members' questions with information already collected or known }
    $ = curocc();
  { exit section  }



  if curocc() > QHMEMBER then
    endsect
  endif;

PROC QHNOQ
preproc
$(curocc())= concat( strip(QHFIRSTN(curocc())), " ", strip(QHLASTN(curocc())) );  { person's name must be added to the household schedule roster }
PROC ACTB1L
Preproc
 if ( QHAGE(curocc()) < 10 or ACTA3(curocc()) = 2) then skip to next ACTB0 endif;
PROC ACTB12
if $=2 then skip to ACTB14A endif;
PROC ACTB14A
if $=2 then skip to ACTB14B endif;
PROC ACTB14B
if $=2 then skip to ACTB14C endif;
PROC ACTB14C
if $=2 then skip to ACTB14D endif;
PROC ACTB14D
if $=2 then skip to ACTB14E endif;
PROC ACTB14E
if $=2 then skip to ACTB15 endif;
PROC QHSEC08C_FORM


PROC ACTC_0
Preproc
$=curocc();

  if curocc() > QHMEMBER then
    endsect
  endif;
PROC QHNOR
preproc
QHNOR(curocc())= concat( strip(QHFIRSTN(curocc())), " ", strip(QHLASTN(curocc())) );
PROC ACTC1
Preproc
if (QHAGE(curocc()) < 10 or ACTA3(curocc()) = 2) then skip to next ACTC_0 endif;
Postproc
if $(curocc())=2 then skip to ACTC15 endif;
PROC ACTC12
if $(curocc())=2 then skip to ACTC14 endif;
PROC ACTC15
Preproc
for i in QHSEC08B000 do
  If ACTB8(curocc())=12 then skip to next ACTC_0 endif; // à ajuster avec Dalila
  If ACTB8(curocc())<12 then skip to next ACTC_0 endif; // à ajuster avec Dalila
enddo;
PROC ACTD_0
Preproc
$=curocc();
  if curocc() > QHMEMBER then
    endsect
  endif;
PROC QHNOT
preproc
QHNOT(curocc())= concat( strip(QHFIRSTN(curocc())), " ", strip(QHLASTN(curocc())) );
PROC ACTD1
Preproc
if QHAGE(curocc()) < 10 then
     skip to next ACTD_0
  endif;
Postproc
if $=1 then skip to ACTD1P endif;
if $=2 then endsect endif;

PROC ACTD2
if $=2 then endsect endif;
PROC ACTD13
if $ > 12 then errmsg("erreur sur le nombre MOIS"); reenter endif;
PROC ACTD14
if $ > 31 then errmsg("erreur sur le nombre JOURS"); reenter endif;
PROC ACTD15
if $ > 24 then errmsg("erreur sur le nombre d'HEURES"); reenter endif;
PROC ACTD17
if $=2 then skip to ACTD19 endif;
PROC QHSEC08E_FORM

PROC ACTE_0
Preproc
$=curocc();

  if curocc() > QHMEMBER then
    endsect
  endif;
PROC QHNOU
preproc
$(curocc())= concat( strip(QHFIRSTN(curocc())), " ", strip(QHLASTN(curocc())) );
PROC ACTE_1
Preproc
if !QHAGE(curocc()) in 10:65 then
     skip to next ACTE_0
  endif;
Postproc
if $=2 then skip to ACTE_11 endif;
PROC ACTE_2
if $=1 then skip to ACTE_4 endif;
PROC ACTE_3
if pos( "X", $ ) then editnote() endif;
skip to ACTE_15;
PROC ACTE_4
if pos( "X", $ ) then editnote() endif;
if length(strip($))>3 then
	errmsg("ON NE PEUT PAS COCHER PLUS DE TROIS REPONSES");
	reenter;
endif;
PROC ACTE_6
if pos( "X", $ ) then editnote() endif;

if length(strip($))>3 then
	errmsg("ON NE PEUT PAS COCHER PLUS DE TROIS REPONSES");
	reenter;
endif;
PROC ACTE_8
if $= 3 then skip to ACTE_18 endif;
PROC ACTE_10A
skip to ACTE_18;
PROC ACTE_11
if pos( "X", $ ) then editnote() endif;
PROC ACTE_12
if pos( "X", $ ) then editnote() endif;
if pos( "Y", $ ) and length(strip($)) > 1 then
     errmsg("VOUS NE POUVEZ PAS CHOISIR LA MODALITE AUCUN AVEC UNE AUTRE REPONSE");
    reenter
  endif;
PROC ACTE_13
if $=2 then skip to ACTE_15 endif;
PROC ACTE_14
if pos( "X", $ ) then editnote() endif;
PROC RNA00
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
   // reenter
  endif;
PROC RNA11
Preproc
  if (RNA01 = 1 or RNA02 = 1 or RNA03 = 1 or RNA04 = 1 or RNA05 = 1 or RNA06 = 1 or RNA07 = 1 or RNA08 = 1 or RNA09 =1  or RNA10 = 1) then
    $ = 1
  else
    $ = 2;
  endif;

PROC NANAG
if $ = 0 then
   errmsg(" erreur sur la valeur saisie. Selon la partie A de la section il y'a au moins une entreprises non agricoles");
   reenter
endif;
PROC RNB0O
Preproc
$ = curocc();

 if curocc() > NANAG then
    endsect
  endif;
PROC RNB01
onfocus
  clean_labels();
 codes(0)  = 0;
 labels(0) = "CODE NON VALIDE";

 j = 1;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98     then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );  { person's name must be added to the household schedule roster }
      j = j + 1;
    endif;
  enddo;
 SetValueSet( @GetSymbol(), codes, labels );

POSTPROC
if $ = 0 then errmsg("OPTION NON VALIDE") ;
 reenter
 endif;
PROC RNB03A
onfocus
  clean_labels();
 codes(0)  = 0;
 labels(0) = "CODE NON VALIDE";

 j = 1;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98     then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );  { person's name must be added to the household schedule roster }
      j = j + 1;
    endif;
  enddo;
 SetValueSet( @GetSymbol(), codes, labels );

POSTPROC
if $ = 0 then errmsg("OPTION NON VALIDE") ;
 reenter
 endif;
PROC RNB03B
 clean_labels();
 codes(0)  = 0;
 labels(0) = "UN SEUL PROPRIETAIRE ";

 j = 1;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98     then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );  { person's name must be added to the household schedule roster }
      j = j + 1;
    endif;
  enddo;
 SetValueSet( @GetSymbol(), codes, labels );
PROC RNB08
if $ > RNB07 then errmsg("Erreur, cet effectif doit etre inferieur à l'effectif permanent "); endif;
PROC RNB09A
onfocus
  clean_labels();
 codes(0)  = 0;
 labels(0) = "CODE NON VALIDE";

 j = 1;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98     then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );  { person's name must be added to the household schedule roster }
      j = j + 1;
    endif;
  enddo;
 SetValueSet( @GetSymbol(), codes, labels );

POSTPROC
if $ = 0 then errmsg("OPTION NON VALIDE") ;
 reenter
 endif;
PROC RNB09B
if $>12 then errmsg(" Le nombre de mois doit etre inferieur à 12"); reenter endif;
PROC RNB09C
if $>31 then errmsg(" Le nombre de Jours doit etre inferieur à 31"); reenter endif;
PROC RNB09D
if $>24 then errmsg(" Le nombre d'heures doit etre inferieur à 24h"); reenter endif;
PROC RNB09A1
  clean_labels();
 codes(0)  = 0;
 labels(0) = "UN OU DEUX ";

 j = 1;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98     then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );  { person's name must be added to the household schedule roster }
      j = j + 1;
    endif;
  enddo;
 SetValueSet( @GetSymbol(), codes, labels );
PROC RNB09A2
 clean_labels();
 codes(0)  = 0;
 labels(0) = "UN OU DEUX OU TROIS ";

 j = 1;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98     then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );  { person's name must be added to the household schedule roster }
      j = j + 1;
    endif;
  enddo;
 SetValueSet( @GetSymbol(), codes, labels );
PROC RNB09B2
if $>12 then errmsg(" Le nombre de mois doit etre inferieur à 12"); reenter endif;
PROC RNB09C2
if $>31 then errmsg(" Le nombre de Jours doit etre inferieur à 31"); reenter endif;
PROC RNB09D2
if $>24 then errmsg(" Le nombre d'heures doit etre inferieur à 24h"); reenter endif;
PROC RNB10
if $=1 then skip to RNB12 endif;
PROC RNB12
if $=9 then editnote(); endif;

PROC RND0F
Preproc
   $ = curocc();
	  if RNB13(curocc())= 1 then skip to next RND0F endif;
      if curocc() > NANAG then endsect endif;
PROC RND0M
if $ = 1 then skip to RND04 endif;
savepartial();

PROC RND0B
if $>6 then skip to RND0C endif;
PROC RND0C
if $=2 then skip to RND07AF endif;
PROC RND0H
Preproc
  $ = curocc();
    if RNB13(curocc())=2 then skip to next RND0H endif;
    if curocc() > NANAG then endsect
  endif;

PROC RND0D
if $ = 1 then skip to RND0I endif;
savepartial();
PROC RND0E
if $>6 then skip to RND0J endif;
PROC RND0I
skip to RND0K ;
PROC RND0J
if $=2 then skip to RND07CA endif;
PROC RND0K
if $=2 then skip to next RND0H endif;
PROC RNA0A
Preproc
$ = curocc();

if curocc() > NANAG then endsect
endif;
PROC RND0G1
preproc
if RNB13(curocc()) = 1 then skip to RNA0A endif;

postproc

Skip to RND0GB;
savepartial();
PROC RND0GA
preproc
if RNB13(curocc()) = 2 then skip to RND0GB endif;
PROC RND0GJ
savepartial();
PROC RNF01
Preproc
$ = curocc();

if curocc() > NANAG then endsect
endif;
PROC RNF01A
if $ = 0 then skip to RNF01E endif;
savepartial();
PROC RNF0A
Preproc
$ = curocc();

if curocc() > NANAG then endsect
endif;
PROC RNF0B
if $ = 2 then skip to RNF0D  endif;
savepartial();
PROC RNF0D
if $ = 2 then skip to RNF0F  endif;
savepartial();
PROC RNF0F
if $ = 2 then skip to RNF0H  endif;
savepartial();

PROC RNF0H
if $ = 2 then skip to RNF0J  endif;
savepartial();
PROC RNF0J
if $ = 2 then skip to next RNF0A endif;
savepartial();
PROC RNH00
Preproc

$ = curocc();

if curocc() > NANAG then endsect
endif;
PROC RNH01
if $ = 2 then skip to next RNH00 endif;
savepartial();
PROC RNH05A
if pos("X",$) then editnote() endif;
PROC RNH05B
if pos("X",$) then editnote() endif;
PROC RNH05C
if pos("X",$) then editnote() endif;
PROC RNH10A
savepartial();
PROC RNH10C
savepartial();
PROC ALI01
if $=2 then skip to ALI02; endif;
PROC ALIM01
preproc
  $ = curocc();
PROC ALIM02
Preproc
ALIM02(1)="PRODUITS CÉRÉALIERS, LAITS ET PRODUITS LAITIERS, FRUITS ET LÉGUMES";
ALIM02(2)="VIANDES, ŒUFS, POISSONS";
ALIM02(3)="HUILES ET GRAISSES, SUCRES ET PRODUITS SUCRÉS, ÉPICES, SEL ET CONDIMENTS";
ALIM02(4)="BOISSONS NON ALCOOLISÉES, CAFÉ, THÉ ET STIMULANTS";
ALIM02(5)="PAUTRES DÉPENSES ALIMENTAIRES";
PROC ALIM03
if $=9 then skip to ALIM06 endif;
PROC ALI02
if $=2 then skip to ALI03; endif;
PROC ALIMA00
preproc
  $ = curocc();
PROC ALIMA01
Preproc
ALIMA01(1)="PRODUITS CÉRÉALIERS, LAITS ET PRODUITS LAITIERS, FRUITS ET LÉGUMES";
ALIMA01(2)="VIANDES, ŒUFS, POISSONS";
ALIMA01(3)="HUILES ET GRAISSES, SUCRES ET PRODUITS SUCRÉS, ÉPICES, SEL ET CONDIMENTS";
ALIMA01(4)="BOISSONS NON ALCOOLISÉES, CAFÉ, THÉ ET STIMULANTS";
ALIMA01(5)="PAUTRES DÉPENSES ALIMENTAIRES";
PROC ALIMA02
if $=9 then skip to ALIMA05 endif;
PROC ALIMA0A
preproc
  $ = curocc();
PROC ALIMA0B
Preproc
ALIMA0B(1)="HABILLEMENT – CHAUSSURES";
ALIMA0B(2)="LOGEMENT - CHARGES – SANTÉ – HYGIÈNE CORPORELLE";
ALIMA0B(3)="TRANSPORT – COMMUNICATIONS – ÉDUCATION – CULTURE ET LOISIRS ";
ALIMA0B(4)="BIENS D'ÉQUIPEMENT";
ALIMA0B(5)="DIVERS";
PROC ALIMA0C
if $=9 then skip to ALIMA0F endif;
PROC ALI04
if $=2 then skip to PRESS; endif;

PROC ALIMA001
preproc
  $ = curocc();
PROC ALIMA002
Preproc
ALIMA002(1)="HABILLEMENT – CHAUSSURES";
ALIMA002(2)="LOGEMENT - CHARGES – SANTÉ – HYGIÈNE CORPORELLE";
ALIMA002(3)="TRANSPORT – COMMUNICATIONS – ÉDUCATION – CULTURE ET LOISIRS ";
ALIMA002(4)="BIENS D'ÉQUIPEMENT";
ALIMA002(5)="DIVERS";
PROC ALIMA003
if $=9 then skip to ALIMA006 endif;
PROC PRESS
if $=0  then skip to PREST endif;
PROC DEPC00
preproc
  $ = curocc();
if curocc() > PRESS then
   endsect
endif;
PROC DEPC03
if $=9 then skip to DEPC06 endif;
PROC PREST
if $=0  then skip to PRESR endif;

PROC DEPM01
preproc
  $ = curocc();
if curocc() > PREST then
   endsect
endif;
PROC PRESR
if $<0 then errmsg("Impossible, cette valeur doit etre positive") endif;
if $=0 then skip to TRIDE endif;
PROC QHSEC10G000

PROC PRES00
preproc
$=curocc();
if curocc() > PRESR then
   endsect
endif;
PROC TRIDE
// if $ = 2 then skip to TR12F endif;
PROC NPAR
if $<=0 then errmsg("Impossible, cette valeur doit etre positive") endif;

PROC QHSEC11A000

PROC RNBA00
preproc
$=curocc();
if curocc() > NPAR then
   endsect
endif;
PROC RNB0E
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();
PROC RNB0F
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();
PROC RNB0G
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();
PROC QHSEC11B1000
postproc
  if TRIDF=0 then skip to AGRC02C(1); endif;
PROC AGRC02B
Preproc
$ = curocc();


PROC AGRC02B1
Preproc
AGRC02B1(1)="1. Blé dur قمح صلب";
AGRC02B1(2)="2. Blé tendre قمح لیِّن";
AGRC02B1(3)="3. Orge شعیر";
AGRC02B1(4)="4. Avoine خرطال";
AGRC02B1(5)="5. Sorgho ذرَّة بیضاء";
AGRC02B1(6)="6. Maïs grain ذرة صفراء";
AGRC02B1(7)="7. Autres céréales حبوب أخرى";
AGRC02B1(8)="8. Lentilles عدس";
AGRC02B1(9)="9. Pois-chiche حمص";
AGRC02B1(31)="10. Pois sec بزیلا";
AGRC02B1(11)="11. Haricot sec فصولیة جافة";
AGRC02B1(12)="12. Fève sèche فول جاف";
AGRC02B1(13)="13. Vesce et Vesce-avoine بیقة و بیقة-خرطال";
AGRC02B1(14)="14. Luzerne فصّة";
AGRC02B1(15)="15. Maïs fourrager ذرة كلئیة";
AGRC02B1(16)="16. Autres fourrages أعلاف أخرى";
AGRC02B1(17)="17. Pomme de terre بطاطا";
AGRC02B1(18)="18. Oignon sec et vert بصل أخضر و جاف";
AGRC02B1(19)="19. Ail ثوم";
AGRC02B1(20)="20. Tomate طماطم";
AGRC02B1(21)="21. Piment فلفل حار";
AGRC02B1(22)="22. Poivron (frais et séché) فلفل حلو";
AGRC02B1(23)="23. Carotte جزر";
AGRC02B1(24)="24. Courgette كوسة";
AGRC02B1(25)="25. Navet لفت";
AGRC02B1(26)="26. Concombre خیار";
AGRC02B1(27)="27. Chou et Chou-fleur كرمب و ملفوف";
AGRC02B1(28)="28. Artichaut قرنون";
AGRC02B1(29)="29. Betterave بنجر";
AGRC02B1(30)="30. Fève verte فول أخضر";
AGRC02B1(31)="31. Haricot vert فصولیة خضرة";
AGRC02B1(32)="32. Petit pois بسلة";
AGRC02B1(33)="33. Fraises فرولة";
AGRC02B1(34)="34. Tomate industrielle طماطم صناعیة";
AGRC02B1(35)="35. Betterave à sucre شمندر سكري";
AGRC02B1(36)="36. Oléagineux (arachide, soja, maïs,...) ( زیتونیات (فول سوداني ٬ صویا ٬ ذرة         ";
AGRC02B1(37)="37. Tabac تبغ";
AGRC02B1(38)="38. Agrumes (orange, citron, mandarine, lime,...) (... حمضیات (برتقال ٬ لیمون ٬";
AGRC02B1(39)="39. Abricot مشمش";
AGRC02B1(40)="40. Pêche et nectarine خوخ";
AGRC02B1(41)="41. Coing سفرجل";
AGRC02B1(42)="42. Poire إجاس";
AGRC02B1(43)="43. Pomme تفاح";
AGRC02B1(44)="44. Prune برقوق";
AGRC02B1(45)="45. Olive de table زیتو الأكل";
AGRC02B1(46)="46. Olive à huile زیتون العصیر";
AGRC02B1(47)="47. Figue تین";
AGRC02B1(48)="48. Amande لوز";
AGRC02B1(49)="49. Noix جوز";
AGRC02B1(50)="50. Cerise كرز";
AGRC02B1(51)="51. Palmier dattier (Deglet Nour) ( نخیل (دقلة نور";
AGRC02B1(52)="52. Palmier dattier (Ghars et autres) ( نخیل (غرس و غیره";
AGRC02B1(53)="53. Raisin de table عنب الأكل";
AGRC02B1(54)="54. Raisin de cuve عنب العصیر";
AGRC02B1(55)="55. Herbes et épices أعشاب و توابل";
AGRC02B1(56)="56. Plantes ornementales / aromatiques / médicinales نباتات الزینة / العطریة / الطبیة";
AGRC02B1(57)="57. Pépinières (plants arboricoles, horticoles, maraîchages, ...) مشتلة";
AGRC02B1(58)="58. Autres محاصیل أخرى";
PROC AGRC02B1A
if $=2 then skip to next AGRC02B endif;
PROC AGRC02BAK
if $=2 then skip to next AGRC02B endif;
PROC AGRC01B2
Preproc
$=curocc();
PROC AGRC02B2
Preproc
AGRC02B2(1)= "ENGRAIS (INORGANIQUES / CHIMIQUES (URÉE, PHOSPHATES, NPK/FORMULE...)";
AGRC02B2(2)= "PRODUITS PHYTOSANITAIRES (PESTICIDES, FONGICIDES, HERBICIDES...)";
AGRC02B2(3)= " LES JEUNES PLANTS";
AGRC02B2(4)= "SACS, FEUILLES DE PLASTIQUE, DES OUTILS DE TERRAIN, LES INSTALLATIONS DE STOCKAGE, D'ENTRETIEN ET DE FIXATION";
AGRC02B2(5)= " IRRIGATION (NE LISTE PAS DE CARBURANT)";
AGRC02B2(6)= "CARBURANT POUR SÉCHER LES CULTURES OU POUR LES M;*OYENS OU L'IRRIGATION DE TRANSPORT, ETC ..";
AGRC02B2(7)= " LOCATION DE TERRE / TERRAIN";
AGRC02B2(8)= "LOCATION DE MACHINES (Y COMPRIS LE COÛT DU CARBURANT), LA LOCATION DE MATÉRIEL D'IRRIGATION ... ETC";
AGRC02B2(9)= "TRANSPORT";
AGRC02B2(10)= " MAIN D'OUVRE AGRICOLE";
AGRC02B2(11)= "EAU ET ÉLECTRICITÉ";
AGRC02B2(12)= "ACHAT DE MATÉRIEL ET DE MACHINES AGRICOLES";
AGRC02B2(13)= "LES ACHATS DE MOYENS DE TRANSPORT";
AGRC02B2(14)= "BÂTIMENT GRANGE, CREUSEMENT DE CANAUX ET DE PUITS, LA CONSTRUCTION D'ENTREPÔTS";
AGRC02B2(15)= " AUTRES";
AGRC02B2(16)= " TOTAL (SI LA PERSONNE NE PEUT PAS REPONDRE, ELLE DONNE UNE ESTIMATION TOTALE";
PROC QHSEC11C1000
Preproc
  if TRIDG=0 then skip to AGRC01C2(1); endif;
PROC AGRC02C
Preproc
$=curocc();
PROC AGRC02BA
Preproc
AGRC02BA(1)="BOVINS (BŒUF)";
AGRC02BA(2)="OVINS (MOUTONS)";
AGRC02BA(3)="CAPRINS (CHÈVRES)";
AGRC02BA(4)="CAMELINS (CHAMEAUX)";
AGRC02BA(5)="EQUINS (CHEVAUX)";
AGRC02BA(6)="ASINS (ANES)";
AGRC02BA(7)="LAPINS";
AGRC02BA(8)="POULETS ";
AGRC02BA(9)="CANARDS";
AGRC02BA(10)="DINDONS";
AGRC02BA(11)="CAILLES";
AGRC02BA(12)="PINTADES";
AGRC02BA(13)="AUTRES VOLAILLES";
PROC AGRC02B1K
if $=2 then skip to next AGRC02C endif;
PROC AGRC02B1U
if $=2 then skip to next AGRC02C endif;
PROC AGRC01BA
Preproc
$=curocc();
PROC AGRC02BB
Preproc
AGRC02BB(1)="ABATTAGE";
AGRC02BB(2)="FRAIS DE TRANSPORT";
AGRC02BB(3)="FOURRAGE POUR LES ANIMAUX";
AGRC02BB(4)="EAU ";
AGRC02BB(5)="ÉLECTRCITÉ ET GAZ";
AGRC02BB(6)="FRAIS DE SANTÉ (VÉTÉRINAIRE, VACCINER, DÉPARASITER OU SOIGNER)";
AGRC02BB(7)="SALAIRES DU TRAVAIL (EN ESPÈCES ET EN NATURE)";
AGRC02BB(8)="TAXES";
AGRC02BB(9)="AUTRES DÉPENSES LIÉES À L'ÉLEVAGE";
AGRC02BB(10)="AUTRE 1 (À PRÉCISER)";
AGRC02BB(11)="AUTRE 2 (À PRÉCISER)";
AGRC02BB(12)="AUTRE 3 (À PRÉCISER)";
AGRC02BB(13)="TOTAL (SI LA PERSONNE NE PEUT PAS REPONDRE, ELLE DONNE UNE ESTIMATION TOTALE)";
PROC AGRC01C2
Preproc
$=curocc();
PROC AGRC02C2
Preproc
AGRC02C2(1)="VIANDE ROUGE";
AGRC02C2(2)="VIANDE DE VOLAILLE";
AGRC02C2(3)="PEAUX ET CUIRS ";
AGRC02C2(4)="LAIT";
AGRC02C2(5)="PRODUITS LAITIERS";
AGRC02C2(6)="OEUFS";
AGRC02C2(7)="MIEL";
AGRC02C2(8)="HUILE D'OLIVE";
AGRC02C2(9)="AUTRES HUILES D'ARBRE (ARGAN, PALMIER ..ETC)";
AGRC02C2(10)="PRODUIT DIRIVÉS DES DATTES ( DEBSS, GHARESS …ETC)";
AGRC02C2(11)="CONFITURE";
AGRC02C2(12)="LEGUMES EN CONSERVE";
AGRC02C2(13)="PRODUITS DÉRIVÉS DES CÉRÉALES (FARINE , SEMOULE,COUSCOUS,ORGE …ETC)";
AGRC02C2(14)="AUTRE";
PROC AGRC02B1V
if $=2 then skip to next AGRC01C2 endif;
PROC AGRC02B1Y
if $=0 then skip to next AGRC01C2 endif;
PROC AGRC08F1_1
Preproc
For i in QHSEC11A000 do
     $(i)=RNBA00(i);
enddo;

if RNB0H(curocc())<> 2 then skip to next AGRC08F1_1 endif;
PROC AGRC08F1_3
if $=2 then skip to AGRC08F1_8 endif;
PROC AGRC08F1_6
Preproc
if RNB0B(curocc())<> 1 then skip to AGRC08F1_7 endif;
PROC AGRC08F1_7
Preproc
if RNB0B(curocc())<> 5 then skip to AGRC08F1_9 endif;
PROC AGRC08F1_9
Preproc
if RNB0B(curocc())<> 2 then skip to next AGRC08F1_1 endif;
PROC AGRC08F1_10
if $=2 then endsect endif;
PROC AGRCF2
Preproc
$=curocc();
PROC AGRCF2_2
Preproc
AGRCF2_2(1)="DISTRIBUTEUR OU ÉPANDEUR D'ENGRAIS";
AGRCF2_2(2)="TRACTEUR";
AGRCF2_2(3)="CHARRUE À TRACTION ANIMALE";
AGRCF2_2(4)="CHARRUE À CHIZEL";
AGRCF2_2(5)="COVER-CROP";
AGRCF2_2(6)="SEMOIR";

AGRCF2_2(7)="RÉCOLTEURS DE CANNE À SUCRE OU DE BETTERAVE";
AGRCF2_2(8)="DISTRIBUTEUR OU ÉPANDEUR DE PESTICIDES";
AGRCF2_2(9)="MOISSONNEUSE, LIEUSE ET BATTEUSE";
AGRCF2_2(10)="MOISSONNEUSE";
AGRCF2_2(11)="POMPES D’EAU";
AGRCF2_2(12)="EQUIPEMENTS D’ÉNERGIE SOLAIRE";

AGRCF2_2(13)="MOULIN D’OLIVES";
AGRCF2_2(14)="APPAREIL DE TRAITE DES VACHES";
AGRCF2_2(15)="CHARRETTE-CHARIOT";
AGRCF2_2(16)="JEEPS-CAMION ET CAMIONNETTES..";
AGRCF2_2(17)="AUTRES ACTIFS NON FONCIERS";


PROC AGRCF2_3
if $=2 then skip to next AGRCF2 endif;
PROC EFORET1
Preproc
$=curocc();
PROC EFORET01
Preproc
EFORET01(1)="BOIS";
EFORET01(2)="CHARBON";
EFORET01(3)="FRUITS FORESTIERS";
EFORET01(4)="CHAMPIGNONS";
EFORET01(5)="PLANTES MÉDICINALES";
EFORET01(6)="AUTRE A PRECISER";
PROC TR12E
If $=2 then skip to TR12D endif;
PROC PECHPS111
Preproc
$=curocc();
PROC PECHES110
Preproc
PECHES110(1)="POISSONS DE LA MER (SARDINE, THON, ETC.)";
PECHES110(2)="POISSONS DE L'ELEVAGE (DORADE, TILAPIA, ETC.)";
PECHES110(3)="CRUSTACÉS (CREVETTES, CRABES, ETC.)";
PECHES110(4)="MOLLUSQUES (MOULES, HUÎTRES, ETC.)";
PECHES110(5)="AUTRES PRODUITS DE LA MER";
PROC TR12D
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();
PROC PEN00
Preproc
$=curocc();
PROC PEN00A
Preproc
PEN00A(1)="PENSION DE RETRAITE/ ALLOCATION DE RETRAITE";
PEN00A(2)="PENSION DE RÉVERSION";
PEN00A(3)="PENSION D'INVALIDITÉ";
PEN00A(4)="PENSION DE CHÔMAGE ";
PEN00A(5)="AUTRE A SPÉCIFIER";


PROC PEN01
if $=2 then skip to next PEN00 endif;
PROC PEN02A1
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 19:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();
PROC PEN02B1
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 19:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();
PROC PEN02C1
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 19:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();
PROC PEN02D1
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 19:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();
PROC PEN02E1
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 19:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();
PROC TR12B
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();
PROC FILSOC001
Preproc
$=curocc();
PROC FILSOC002
Preproc
FILSOC002(1)="ALLOCATION FORFAITAIRE DE SOLIDARITÉ (AFS)";
FILSOC002(2)="ALLOCATION POUR PERSONNES HANDICAPÉES";
FILSOC002(3)="INDEMNITÉ POUR ACTIVITÉ D’INTÉRÊT GÉNÉRAL (IAIG)";
FILSOC002(4)="DISPOSITIFS D’ACTIVITÉS D’INSERTION SOCIALE (DAIS)";
FILSOC002(5)="ALLOCATION  CHÔMAGE ";
FILSOC002(6)="PENSION DE TRAGÉDIE NATIONALE (VICTIMES DU TERRORISME)";
FILSOC002(7)="PENSION DES MOUDJAHIDINES ET DES INVALIDES DE LA GUERRE DE RÉVOLUTION";
FILSOC002(8)="PENSION NAFAKA";
FILSOC002(9)="ALLOCATION  DE SOLIDARITÉ RAMADAN";
FILSOC002(10)="AUTRE A SPÉCIFIER";
Postproc

PROC FILSOC02
if $=1 then skip to FILSOC04A endif;
PROC FILSOC3A1
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC FILSOC3B1
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC FILSOC3C1
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC TR12C
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( "Pas membre du ménage" );
  endif;
savepartial();

PROC REV00A
Preproc
$=curocc();
PROC REV00B
Preproc
REV00B(1)="LOYERS DES MAISONS D'HABITATIONS";
REV00B(2)="LOYERS DES LOCAUX À USAGE PROFESSIONNEL";
REV00B(3)="LOYERS DE TERRAIN";
REV00B(4)="LOYERS DE MATÉRIEL NON-AGRICOLE (CHAISES, BÂCHES, VAISSELLES, BOUTIQUE, ...ETC.)";
REV00B(5)="LOYERS DE MATÉRIEL AGRICOLE (TRACTEURS, ETC.)";
REV00B(6)="REVENUS MOBILIERS ET FINANCIERS(DIVIDENDES D'ACTIONS, INTÉRÊTS SUR PLACEMENTS, ETC.)";
REV00B(7)="PRIME DE DÉPART";
REV00B(8)="CAPITAL DÉCÈS";
REV00B(9)="LICENCE DE TAXI AUX MOUDJAHIDINES ";
REV00B(10)="AUTRE A SPÉCIFIER";
Postproc
PROC REV01
if $=2 then skip to next REV00A endif;
PROC REV02A
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC REV02B
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC REV02C
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC REV02D
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC REV02E
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC TR12
if $=2 then skip to TR12A endif;
PROC TRANS00
Preproc
$=curocc();
PROC TRANS1
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC TRAUT
if $=2 then
endsect
endif;
PROC TR12A
if $=2 then skip to LIST001(1) endif;
PROC TRANEV00A
Preproc
$=curocc();
PROC TRANSE1
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 0:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

postproc
  { Check line number of household respondent in range }
  if !$ in 1: QHMEMBER then
    errmsg( 0017 );
  endif;
savepartial();
PROC TRANSE5
if $=9 then editnote() endif;
PROC TRAUU
if $=2 then
endsect
endif;
PROC QHSEC13A_FORM
preproc
  { Initialize household members' questions with information already collected or known }
  do i = 1 while i <= maxmemb
    if i <= QHMEMBER then
      LIST001(i) = i;
         else
      LIST001(i) = notappl;      { blank out the entries not needed }
    endif;
  enddo;
PROC LIST001
preproc
   if curocc() > QHMEMBER then
    endsect
  endif;

 if QHAGE(curocc()) < 15 then skip to next LIST001 endif;
PROC QHNOV
preproc
QHNOV(curocc())= concat( strip(QHFIRSTN(curocc())), " ", strip(QHLASTN(curocc())) );
PROC EPCR1
if !pos( "A", EPCR1 ) and !pos( "B", EPCR1 ) and !pos( "C", EPCR1 )and !pos( "D", EPCR1 ) then skip to EPCR5 endif;
PROC EPCR2
if $=2 then skip to EPCR5 endif;
PROC EPCR5
if $=2 then skip to next LIST001 endif;
PROC EPCR8
if $=2 then skip to next LIST001 endif;
PROC EPCR10A
if EPCR9 = 1 then skip to EPCR11A endif;
PROC EPCR10B
if EPCR9 = 2 then skip to EPCR11A endif;
PROC EPCR11A
if EPCR9 = 1 then skip to EPCR12A endif;
PROC EPCR11B
if EPCR9 = 2 then skip to EPCR12A endif;
PROC EPCR12A
if EPCR9 = 1 then skip to EPCR13A endif;
if $=9 then editnote() endif;
PROC EPCR12B
if EPCR9 = 2 then skip to EPCR13A endif;
if $=9 then editnote() endif;
PROC EPCR12C
if $=9 then editnote() endif;
PROC EPCR13A
if EPCR9 = 1 then skip to EPCR14A endif;
if $=9 then editnote() endif;
PROC EPCR13B
if EPCR9 = 2 then skip to EPCR14A endif;
if $=9 then editnote() endif;
PROC EPCR13C
if $=9 then editnote() endif;
PROC EPCR14A
if EPCR9 = 1 then skip to EPCR15A endif;
PROC EPCR14B
if EPCR9 = 2 then skip to EPCR15A endif;
PROC EPCR15A
if EPCR9 = 1 then skip to EPCR16A endif;
PROC EPCR15B
if EPCR9 = 2 then skip to EPCR16A endif;
PROC EPCR15C
if EPCR15A=2 and EPCR15B=2 and EPCR15C=2 then
   skip to next LIST001
 endif;
PROC EPCR16A
if EPCR9 = 1 then skip to EPCR17A endif;
PROC EPCR16B
if EPCR9 = 2 then skip to EPCR17A endif;
PROC QHFINISH
onfocus
  $ = " ";

postproc
  if demode() = add then
    strnotes = editnote();
    if endmess() then
      reenter
    else
      endlevel;
   endif;
 endif;
