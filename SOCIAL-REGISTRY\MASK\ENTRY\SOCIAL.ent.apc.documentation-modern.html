<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOCIAL.ent.apc Documentation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js" integrity="sha512-GsLlZN/3F2ErC5ifS5QtgpiJtWd43JWSuIgh7mbzZ8zBps+dvLusV+eNQATqgA/HdeKFVgA5v3S/cIrLF7QnIg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet">
    <style>
        /* Modern, Simple Design System */
        :root {
            /* Colors - Simplified palette */
            --primary: #3b82f6;
            --primary-light: #93c5fd;
            --primary-dark: #1d4ed8;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Light Theme */
            --bg: var(--gray-50);
            --bg-panel: white;
            --text: var(--gray-900);
            --text-secondary: var(--gray-600);
            --border: var(--gray-200);

            /* Spacing - Simplified */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;

            /* Typography */
            --font-sans: 'Inter', system-ui, sans-serif;
            --font-mono: 'Fira Code', monospace;

            /* Radius - Simplified */
            --radius: 0.375rem;

            /* Layout */
            --sidebar-width: 260px;
            --header-height: 56px;
        }

        /* Dark Theme */
        [data-theme="dark"] {
            --bg: var(--gray-900);
            --bg-panel: var(--gray-800);
            --text: var(--gray-100);
            --text-secondary: var(--gray-400);
            --border: var(--gray-700);
        }

        /* Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
        }

        body {
            font-family: var(--font-sans);
            color: var(--text);
            background-color: var(--bg);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
        }

        /* Layout */
        .app {
            display: grid;
            grid-template-areas:
                "header header"
                "sidebar main";
            grid-template-columns: var(--sidebar-width) 1fr;
            grid-template-rows: var(--header-height) 1fr;
            height: 100vh;
        }

        /* Header */
        .header {
            grid-area: header;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--space-4);
            background-color: var(--bg-panel);
            border-bottom: 1px solid var(--border);
            height: var(--header-height);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 600;
            color: var(--primary);
            text-decoration: none;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        /* Sidebar */
        .sidebar {
            grid-area: sidebar;
            background-color: var(--bg-panel);
            border-right: 1px solid var(--border);
            overflow-y: auto;
            padding: var(--space-4) 0;
        }

        .sidebar-section {
            margin-bottom: var(--space-4);
        }

        .sidebar-heading {
            padding: 0 var(--space-4);
            margin-bottom: var(--space-2);
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--text-secondary);
            font-weight: 600;
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-link {
            display: flex;
            align-items: center;
            padding: var(--space-2) var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.875rem;
            gap: var(--space-2);
        }

        .sidebar-link:hover {
            background-color: var(--bg);
        }

        .sidebar-link.active {
            color: var(--primary);
            background-color: var(--bg);
            font-weight: 500;
            border-left: 2px solid var(--primary);
        }

        /* Main Content */
        .main {
            grid-area: main;
            overflow-y: auto;
            padding: var(--space-6);
        }

        /* Content Blocks */
        .section {
            max-width: 800px;
            margin: 0 0 var(--space-8) 0;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: var(--space-4);
            color: var(--text);
        }

        /* Code Block */
        .code-block {
            background-color: var(--bg-panel);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            margin: var(--space-4) 0;
            overflow: hidden;
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            padding: var(--space-2) var(--space-4);
            background-color: var(--bg);
            border-bottom: 1px solid var(--border);
        }

        .code-content {
            padding: var(--space-4);
            font-family: var(--font-mono);
            font-size: 0.875rem;
            overflow-x: auto;
            white-space: pre;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-2) var(--space-4);
            background-color: var(--bg-panel);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            color: var(--text);
            font-size: 0.875rem;
            cursor: pointer;
        }

        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: var(--radius);
            background-color: var(--bg-panel);
            border: 1px solid var(--border);
            color: var(--text);
            cursor: pointer;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        /* Mobile Menu Toggle */
        .menu-toggle {
            display: none;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .app {
                grid-template-areas:
                    "header"
                    "main";
                grid-template-columns: 1fr;
            }

            .sidebar {
                position: fixed;
                top: var(--header-height);
                left: 0;
                height: calc(100vh - var(--header-height));
                transform: translateX(-100%);
                z-index: 10;
                transition: transform 0.2s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .menu-toggle {
                display: block;
            }

            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 5;
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.2s ease;
            }

            .overlay.active {
                opacity: 1;
                pointer-events: auto;
            }
        }
    </style>
</head>
<body>
    <div class="app">
        <header class="header">
            <a href="#" class="logo">
                <i class="fas fa-file-code"></i>
                <span>SOCIAL.ent.apc</span>
            </a>
            <div class="header-actions">
                <button class="btn-icon menu-toggle" id="menuToggle" aria-label="Toggle menu">
                    <i class="fas fa-bars"></i>
                </button>
                <button class="btn-icon" id="themeToggle" aria-label="Toggle theme">
                    <i class="fas fa-moon"></i>
                </button>
                <button class="btn" id="exportPdf">
                    <i class="fas fa-file-pdf"></i>
                    <span>Export PDF</span>
                </button>
                <button class="btn" id="exportCodebook">
                    <i class="fas fa-book"></i>
                    <span>Codebook</span>
                </button>
            </div>
        </header>

        <div class="overlay" id="overlay"></div>

        <aside class="sidebar" id="sidebar">
            <div class="sidebar-section">
                <h3 class="sidebar-heading">Getting Started</h3>
                <ul class="sidebar-nav">
                    <li><a href="#overview" class="sidebar-link active"><i class="fas fa-home"></i> Overview</a></li>
                    <li><a href="#structure" class="sidebar-link"><i class="fas fa-sitemap"></i> File Structure</a></li>
                </ul>
            </div>

            <div class="sidebar-section">
                <h3 class="sidebar-heading">Core Components</h3>
                <ul class="sidebar-nav">
                    <li><a href="#variables" class="sidebar-link"><i class="fas fa-cube"></i> Variables</a></li>
                    <li><a href="#functions" class="sidebar-link"><i class="fas fa-code"></i> Functions</a></li>
                    <li><a href="#validation" class="sidebar-link"><i class="fas fa-check-circle"></i> Validation Rules</a></li>
                </ul>
            </div>

            <div class="sidebar-section">
                <h3 class="sidebar-heading">Sections</h3>
                <ul class="sidebar-nav">
                    <li><a href="#household" class="sidebar-link"><i class="fas fa-users"></i> Household Roster</a></li>
                    <li><a href="#housing" class="sidebar-link"><i class="fas fa-home"></i> Housing</a></li>
                    <li><a href="#assets" class="sidebar-link"><i class="fas fa-tv"></i> Assets</a></li>
                    <li><a href="#gps" class="sidebar-link"><i class="fas fa-map-marker-alt"></i> GPS</a></li>
                </ul>
            </div>
        </aside>

        <main class="main" id="main">
            <section id="overview" class="section">
                <h1 class="section-title">SOCIAL.ent.apc Documentation</h1>
                <p>This is the main application logic file for the Algerian National Consumption Survey data collection application. It contains all the logic for data validation, skip patterns, and dynamic questionnaire behavior.</p>

                <div class="code-block">
                    <div class="code-header">
                        <span>File Information</span>
                        <button class="btn-icon" id="copyInfo"><i class="fas fa-copy"></i></button>
                    </div>
                    <div class="code-content">
Version: 1.0
Created: 2025
Language: CSPro
Purpose: Household survey data collection
                    </div>
                </div>
            </section>

            <section id="structure" class="section">
                <h2 class="section-title">File Structure</h2>
                <p>The SOCIAL.ent.apc file is organized into logical sections corresponding to different parts of the questionnaire. Each section contains procedures for validation and skip logic specific to that section.</p>

                <div class="code-block">
                    <div class="code-header">
                        <span>Main Sections</span>
                    </div>
                    <div class="code-content">
PROC GLOBAL           // Global variables and functions
PROC SOCIAL_FF        // Entry point for the application
PROC SOCIAL_LEVEL     // Application level events
PROC SECTA_FORM       // Identification and basic information
PROC QHSEC01X_FORM    // Household roster section
PROC QHSEC02_FORM     // Housing section
PROC EQUIP_00         // Household assets section
PROC QHSEC05000       // Education section
PROC SA00             // Health section
PROC QHFINISH         // End of questionnaire
                    </div>
                </div>
            </section>

            <section id="variables" class="section">
                <h2 class="section-title">Global Variables</h2>
                <p>The application uses several global variables to manage state and store temporary values throughout the questionnaire flow.</p>

                <div class="code-block">
                    <div class="code-header">
                        <span>Key Variables</span>
                    </div>
                    <div class="code-content">
{ Global Variables }
numeric tot;        { Total counter for various calculations }
numeric OK;         { Used for storing user confirmation responses }
numeric i, j, n, p; { Loop counters and indices }
numeric err;        { Error flag }
numeric x, e;       { General purpose numeric variables }
numeric tim;        { Time-related variable }
numeric maxmemb;    { Maximum number of household members }

{ GPS-related variables }
numeric id_marker;   { ID for map marker }
numeric initialLat;  { Initial latitude coordinate }
numeric initialLon;  { Initial longitude coordinate }

{ Arrays for dynamic value sets }
array codes(30);            { Array to store numeric codes for value sets }
alpha(900) strnotes;        { Storage for notes text }
array alpha(40) labels(30); { Array to store labels for value sets }

{ Map object for GPS functionality }
map mymap;

{ Value set for dynamic dropdown menus }
valueset MyValueset;
                    </div>
                </div>
            </section>

            <section id="functions" class="section">
                <h2 class="section-title">Global Functions</h2>
                <p>The application defines several global functions that provide reusable functionality throughout the questionnaire.</p>

                <div class="code-block">
                    <div class="code-header">
                        <span>Key Functions</span>
                    </div>
                    <div class="code-content">
function closeMap(map m)
    // Hides the map object when GPS capture is complete or cancelled
    m.hide();
end;

function retakeGPS(map m)
    // Allows the user to retake GPS coordinates
    m.hide();
    A10gps = 2;
    reenter A10GPS;
end;

function clean_labels();
    // Resets the codes and labels arrays used for dynamic value sets
    numeric z;
    do z = 1 while z &lt;= 30
      codes(z)  = notappl;
      labels(z) = "";
    enddo;
end;

function TestValidityName(string name)
    // Validates person names and text entries according to survey rules
    if strip(name) = "" then
        errmsg("Le nom/texte ne peut pas être vide");
        reenter;
    elseif length(strip(name)) &lt; 2 then
        errmsg("Le nom/texte %s est trop court",strip(name));
        reenter;
    elseif pos(name[1:1], " 0123456789@,;:!?/#-+*.=()\_{}°[]")&gt;0 then
        errmsg("Le nom/texte ne peut pas commencer par %s",name[1:1]);
        reenter;
    endif;
end;
                    </div>
                </div>
            </section>

            <section id="household" class="section">
                <h2 class="section-title">Household Roster Section</h2>
                <p>This section handles the collection of demographic information for each household member, including names, relationships, age, and sex. The roster is a critical component as it establishes the household composition that will be referenced throughout the questionnaire.</p>

                <div class="code-block">
                    <div class="code-header">
                        <span>Household Roster Logic</span>
                    </div>
                    <div class="code-content">
PROC QHLINE
preproc
  $ = curocc();  // Set to current occurrence (row number)

PROC QHFIRSTN
  // Validates the first name of each household member
  TestValidityName(QHFIRSTN);

PROC QHRELAT
 // Enforces household composition rules
 if curocc() = 1 &lt;=&gt;  $ &lt;&gt; 1 then
    errmsg( 0031 );  // First person must be household head
    reenter
 elseif $ = 2 &amp; curocc() &lt;&gt; 2 then
    errmsg( 0033 );  // Spouse must be second person
    reenter
 endif;

PROC QHAGE
 // Validates age against birth year
 if (2025-QHANNEE-1)&gt;QHAGE then
    errmsg(" Erreur sur l'age...");
 endif;
                    </div>
                </div>
            </section>

            <section id="housing" class="section">
                <h2 class="section-title">Housing Section</h2>
                <p>This section collects information about the household's dwelling, including type, ownership status, construction materials, amenities, and number of rooms.</p>

                <div class="code-block">
                    <div class="code-header">
                        <span>Housing Section Logic</span>
                    </div>
                    <div class="code-content">
PROC H00
onfocus
  // Create dynamic value set of eligible respondents (aged 12+)
  clean_labels();
  j = 0;
  do i = 1 while i &lt;= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );

PROC H01
// Allow notes for "Other" dwelling type
if $ = 9 then editnote() endif;

PROC H14
// Validates that rooms occupied doesn't exceed total rooms
if $&gt;H13 then
  errmsg("ERREUR: Le nombre de pièces occupées par le ménage...");
  reenter
endif;
                    </div>
                </div>
            </section>

            <section id="assets" class="section">
                <h2 class="section-title">Household Assets Section</h2>
                <p>This section collects information about household assets and durable goods. It captures ownership of various items including kitchen appliances, heating and cooling equipment, furniture, electronics, and vehicles.</p>

                <div class="code-block">
                    <div class="code-header">
                        <span>Asset Enumeration</span>
                    </div>
                    <div class="code-content">
PROC EM1
Preproc
// Kitchen appliances
EM1(1)="CUISINIÈRE";                           // Stove
EM1(2)="FOUR ÉLECTRIQUE/ PLAQUE DE CUISSON";   // Electric oven/cooktop
EM1(3)="FOUR À MICRO-ONDES ";                  // Microwave oven

// Heating and cooling equipment
EM1(10)="CLIMATISEUR";                         // Air conditioner
EM1(11)="RADIATEUR À GAZ DE VILLE ";           // City gas heater

// Furniture
EM1(21)="SALLE À MANGER";                      // Dining room set
EM1(22)="SALON ";                              // Living room set

// Electronics and entertainment
EM1(25)="TÉLÉVISION COULEUR";                  // Color TV
EM1(26)="PARABOLE";                            // Satellite dish

// Vehicles and transportation
EM1(40)="VOITURE";                             // Car
EM1(41)="CAMION";                              // Truck
                    </div>
                </div>
            </section>

            <section id="validation" class="section">
                <h2 class="section-title">Validation Rules</h2>
                <p>The application implements comprehensive validation rules to ensure data quality and consistency. These rules are applied at various levels, from individual field validation to cross-field and cross-section validation.</p>

                <div class="code-block">
                    <div class="code-header">
                        <span>Key Validation Patterns</span>
                    </div>
                    <div class="code-content">
// Field-level validation
if strip(name) = "" then
    errmsg("Le nom/texte ne peut pas être vide");
    reenter;
endif;

// Cross-field validation
if $&gt;H13 then
    errmsg("ERREUR: Le nombre de pièces occupées...");
    reenter
endif;

// Household composition validation
if curocc() = 1 &lt;=&gt; $ &lt;&gt; 1 then
    errmsg( 0031 );  // First person must be household head
    reenter
endif;

// Age consistency validation
if (2025-QHANNEE-1)&gt;QHAGE then
    errmsg("Erreur sur l'age...");
endif;
                    </div>
                </div>
            </section>

            <section id="gps" class="section">
                <h2 class="section-title">GPS Functionality</h2>
                <p>The application implements GPS coordinate capture for household location. It uses CSPro's built-in GPS and mapping capabilities to capture coordinates, display them on an interactive map, and allow adjustment of the location if needed.</p>

                <div class="code-block">
                    <div class="code-header">
                        <span>GPS Capture Logic</span>
                    </div>
                    <div class="code-content">
PROC A10GPS
preproc
    if $ = 1 then
        // Get GPS coordinates
        gps(coordinates, A10B, A10A);  // A10B = latitude, A10A = longitude
        initialLat = A10B;             // Store initial latitude for validation
        initialLon = A10A;             // Store initial longitude for validation

        // Create map
        mymap.create();                // Initialize map object
        mymap.setCenter(A10B, A10A);   // Center map on captured coordinates
        mymap.setZoom(18);             // Set zoom level for appropriate detail

        // Add marker
        id_marker = mymap.addMarker(A10B, A10A);  // Place marker at coordinates

        // Show map
        mymap.show();                  // Display the map to the user

        // Add interactive buttons
        mymap.addButton("Fermer", closeMap(mymap));       // Close map button
        mymap.addButton("Reprendre", retakeGPS(mymap));   // Retake GPS button
        mymap.addButton("Ajuster", adjustPoint(mymap));   // Adjust location button
    endif;
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const themeToggle = document.getElementById('themeToggle');
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('overlay');
            const sidebarLinks = document.querySelectorAll('.sidebar-link');
            const copyButtons = document.querySelectorAll('.code-header .btn-icon');
            const exportPdfBtn = document.getElementById('exportPdf');
            const exportCodebookBtn = document.getElementById('exportCodebook');

            // Theme Management
            const initTheme = () => {
                const savedTheme = localStorage.getItem('theme') ||
                    (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

                document.body.setAttribute('data-theme', savedTheme);
                const icon = themeToggle.querySelector('i');
                icon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            };

            themeToggle.addEventListener('click', () => {
                const currentTheme = document.body.getAttribute('data-theme') || 'light';
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                document.body.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                // Update icon
                const icon = themeToggle.querySelector('i');
                icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            });

            // Mobile Navigation
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('open');
                overlay.classList.toggle('active');

                // Update icon
                const icon = menuToggle.querySelector('i');
                icon.className = sidebar.classList.contains('open') ? 'fas fa-times' : 'fas fa-bars';
            });

            overlay.addEventListener('click', () => {
                sidebar.classList.remove('open');
                overlay.classList.remove('active');

                // Reset icon
                const icon = menuToggle.querySelector('i');
                icon.className = 'fas fa-bars';
            });

            // Smooth Scrolling Navigation
            sidebarLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();

                    // Get target section
                    const targetId = link.getAttribute('href');
                    const targetSection = document.querySelector(targetId);

                    if (targetSection) {
                        // Update active link
                        sidebarLinks.forEach(l => l.classList.remove('active'));
                        link.classList.add('active');

                        // Smooth scroll to target
                        targetSection.scrollIntoView({ behavior: 'smooth' });

                        // Close mobile menu if open
                        if (window.innerWidth <= 768 && sidebar.classList.contains('open')) {
                            sidebar.classList.remove('open');
                            overlay.classList.remove('active');
                            menuToggle.querySelector('i').className = 'fas fa-bars';
                        }
                    }
                });
            });

            // Copy Code Functionality
            copyButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const codeBlock = button.closest('.code-block').querySelector('.code-content');
                    if (codeBlock) {
                        navigator.clipboard.writeText(codeBlock.textContent);

                        // Show success feedback
                        const originalIcon = button.innerHTML;
                        button.innerHTML = '<i class="fas fa-check"></i>';

                        // Reset after 2 seconds
                        setTimeout(() => {
                            button.innerHTML = originalIcon;
                        }, 2000);
                    }
                });
            });

            // Export PDF Functionality
            if (exportPdfBtn) {
                exportPdfBtn.addEventListener('click', () => {
                    // Show loading state
                    exportPdfBtn.disabled = true;
                    const originalText = exportPdfBtn.querySelector('span').textContent;
                    exportPdfBtn.querySelector('span').textContent = 'Generating...';

                    // Create a clone of the content for PDF generation
                    const content = document.querySelector('.main');
                    const clone = content.cloneNode(true);

                    // Apply PDF-specific styling
                    const style = document.createElement('style');
                    style.textContent = `
                        body { font-family: Arial, sans-serif; color: #333; }
                        .section { page-break-after: always; }
                        .code-content { white-space: pre-wrap; }
                    `;
                    clone.prepend(style);

                    // Configure PDF options
                    const options = {
                        margin: 10,
                        filename: 'SOCIAL.ent.apc-documentation.pdf',
                        image: { type: 'jpeg', quality: 0.98 },
                        html2canvas: { scale: 2 },
                        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                    };

                    // Generate PDF
                    html2pdf().from(clone).set(options).save().then(() => {
                        // Reset button state
                        exportPdfBtn.disabled = false;
                        exportPdfBtn.querySelector('span').textContent = originalText;
                    });
                });
            }

            // Export Codebook Functionality
            if (exportCodebookBtn) {
                exportCodebookBtn.addEventListener('click', () => {
                    // Show loading state
                    exportCodebookBtn.disabled = true;
                    const originalText = exportCodebookBtn.querySelector('span').textContent;
                    exportCodebookBtn.querySelector('span').textContent = 'Generating...';

                    // Create codebook content
                    const codebookContent = document.createElement('div');
                    codebookContent.innerHTML = `
                        <h1 style="text-align: center; margin-bottom: 30px;">SOCIAL.ent.apc Codebook</h1>
                        <p style="text-align: center; margin-bottom: 50px;">Generated on ${new Date().toLocaleDateString()}</p>

                        <h2>Variable Definitions</h2>
                        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                            <thead>
                                <tr style="background-color: #f3f4f6;">
                                    <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Variable</th>
                                    <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Type</th>
                                    <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 10px;">QHREGION</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Numeric</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Region code</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 10px;">QHPROV</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Numeric</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Province code</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 10px;">QHDISTRICT</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Numeric</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">District code</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 10px;">QHHMEMBER</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Numeric</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Number of household members</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 10px;">RELATIONSHIP</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Numeric</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Relationship to household head</td>
                                </tr>
                            </tbody>
                        </table>
                    `;

                    // Configure PDF options
                    const options = {
                        margin: 15,
                        filename: 'SOCIAL.ent.apc-codebook.pdf',
                        image: { type: 'jpeg', quality: 0.98 },
                        html2canvas: { scale: 2 },
                        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                    };

                    // Generate PDF
                    html2pdf().from(codebookContent).set(options).save().then(() => {
                        // Reset button state
                        exportCodebookBtn.disabled = false;
                        exportCodebookBtn.querySelector('span').textContent = originalText;
                    });
                });
            }

            // Scroll Spy for Navigation
            const scrollSpy = () => {
                const sections = document.querySelectorAll('.section');
                const scrollPosition = window.scrollY + 100; // Offset for header

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionBottom = sectionTop + section.offsetHeight;

                    if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                        const id = section.getAttribute('id');
                        sidebarLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === `#${id}`) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            };

            window.addEventListener('scroll', scrollSpy);

            // Initialize
            initTheme();
            scrollSpy(); // Initial call to highlight the current section
        });
    </script>
</body>
</html>
