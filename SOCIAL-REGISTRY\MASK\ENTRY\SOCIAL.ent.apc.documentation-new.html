<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOCIAL.ent.apc | Interactive Documentation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js" integrity="sha512-GsLlZN/3F2ErC5ifS5QtgpiJtWd43JWSuIgh7mbzZ8zBps+dvLusV+eNQATqgA/HdeKFVgA5v3S/cIrLF7QnIg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        /* Core Variables */
        :root {
            /* Colors */
            --primary: #0ea5e9;
            --primary-light: #38bdf8;
            --primary-dark: #0284c7;
            --secondary: #8b5cf6;
            --accent: #f43f5e;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;

            /* Neutrals */
            --neutral-50: #f8fafc;
            --neutral-100: #f1f5f9;
            --neutral-200: #e2e8f0;
            --neutral-300: #cbd5e1;
            --neutral-400: #94a3b8;
            --neutral-500: #64748b;
            --neutral-600: #475569;
            --neutral-700: #334155;
            --neutral-800: #1e293b;
            --neutral-900: #0f172a;
            --neutral-950: #020617;

            /* Light Theme */
            --bg-main: var(--neutral-50);
            --bg-surface: white;
            --bg-surface-hover: var(--neutral-100);
            --text-primary: var(--neutral-900);
            --text-secondary: var(--neutral-600);
            --border: var(--neutral-200);

            /* Spacing */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-12: 3rem;
            --space-16: 4rem;

            /* Typography */
            --font-sans: 'Inter', system-ui, sans-serif;
            --font-mono: 'JetBrains Mono', monospace;

            /* Shadows */
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.04);
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

            /* Transitions */
            --transition: 0.2s cubic-bezier(0.4, 0, 0.2, 1);

            /* Radius */
            --radius-sm: 0.25rem;
            --radius: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-full: 9999px;

            /* Z-index */
            --z-drawer: 40;
            --z-sticky: 20;
            --z-float: 10;
            --z-modal: 50;
            --z-toast: 60;
        }

        /* Dark Theme */
        [data-theme="dark"] {
            --bg-main: var(--neutral-950);
            --bg-surface: var(--neutral-900);
            --bg-surface-hover: var(--neutral-800);
            --text-primary: var(--neutral-100);
            --text-secondary: var(--neutral-400);
            --border: var(--neutral-800);

            /* Adjust shadows for dark mode */
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
            --shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        /* Theme Transition */
        .theme-transition,
        .theme-transition * {
            transition: background-color 0.3s ease,
                        color 0.3s ease,
                        border-color 0.3s ease,
                        box-shadow 0.3s ease !important;
        }

        /* Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
            scroll-behavior: smooth;
            height: 100%;
        }

        body {
            font-family: var(--font-sans);
            color: var(--text-primary);
            background-color: var(--bg-main);
            line-height: 1.5;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            transition: background-color var(--transition), color var(--transition);
        }

        /* App Layout */
        .app {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
        }

        /* Header */
        .header {
            position: sticky;
            top: 0;
            z-index: var(--z-sticky);
            background-color: var(--bg-surface);
            border-bottom: 1px solid var(--border);
            padding: var(--space-4);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-sm);
            transition: background-color var(--transition), border-color var(--transition);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 600;
            font-size: 1.125rem;
            color: var(--primary);
            text-decoration: none;
        }

        .logo-icon {
            font-size: 1.25rem;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        /* Main Container */
        .container {
            display: flex;
            flex: 1;
        }

        /* Navigation Drawer */
        .nav-drawer {
            width: 280px;
            background-color: var(--bg-surface);
            border-right: 1px solid var(--border);
            height: calc(100vh - 60px);
            position: sticky;
            top: 60px;
            overflow-y: auto;
            transition: transform var(--transition), background-color var(--transition), border-color var(--transition);
            scrollbar-width: thin;
            scrollbar-color: var(--neutral-400) transparent;
            display: flex;
            flex-direction: column;
        }

        .nav-drawer::-webkit-scrollbar {
            width: 4px;
        }

        .nav-drawer::-webkit-scrollbar-thumb {
            background-color: var(--neutral-400);
            border-radius: var(--radius-full);
        }

        /* Navigation Header */
        .nav-header {
            padding: var(--space-4);
            border-bottom: 1px solid var(--border);
            position: sticky;
            top: 0;
            background-color: var(--bg-surface);
            z-index: 1;
        }

        /* Search Input */
        .search-container {
            margin-bottom: var(--space-2);
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-icon {
            position: absolute;
            left: var(--space-3);
            color: var(--neutral-500);
            font-size: 0.875rem;
        }

        .search-input {
            width: 100%;
            padding: var(--space-2) var(--space-2) var(--space-2) var(--space-8);
            border-radius: var(--radius);
            border: 1px solid var(--border);
            background-color: var(--bg-main);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: all var(--transition);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.1);
        }

        .search-input::placeholder {
            color: var(--neutral-500);
        }

        /* Navigation Sections */
        .nav-sections {
            flex: 1;
            padding: var(--space-2) 0;
        }

        .nav-section {
            margin-bottom: var(--space-4);
        }

        .nav-section-header {
            padding: var(--space-2) var(--space-4);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: var(--neutral-500);
        }

        .nav-list {
            list-style: none;
        }

        .nav-item {
            margin: 1px 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: var(--space-2) var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.875rem;
            border-radius: 0;
            transition: all var(--transition);
            position: relative;
            gap: var(--space-3);
        }

        .nav-link:hover {
            background-color: var(--bg-surface-hover);
            color: var(--text-primary);
        }

        .nav-link.active {
            background-color: rgba(14, 165, 233, 0.08);
            color: var(--primary);
            font-weight: 500;
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background-color: var(--primary);
            border-radius: 0 var(--radius) var(--radius) 0;
        }

        .nav-icon {
            font-size: 0.875rem;
            width: 20px;
            text-align: center;
            opacity: 0.8;
        }

        /* Navigation Footer */
        .nav-footer {
            padding: var(--space-4);
            border-top: 1px solid var(--border);
            font-size: 0.75rem;
            color: var(--neutral-500);
            text-align: center;
        }

        /* Content Area */
        .content {
            flex: 1;
            padding: var(--space-6);
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        /* Content Sections */
        .content-section {
            margin-bottom: var(--space-12);
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-header {
            margin-bottom: var(--space-6);
        }

        .section-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: var(--space-2);
            color: var(--text-primary);
        }

        .section-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: var(--space-2);
            color: var(--text-primary);
        }

        .section-description {
            color: var(--text-secondary);
            font-size: 1.125rem;
            max-width: 700px;
        }

        /* Card Grid */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: var(--space-4);
            margin-bottom: var(--space-6);
        }

        /* Cards */
        .card {
            background-color: var(--bg-surface);
            border-radius: var(--radius-md);
            border: 1px solid var(--border);
            overflow: hidden;
            transition: all var(--transition);
            box-shadow: var(--shadow-sm);
            margin-bottom: var(--space-4);
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .card-header {
            padding: var(--space-4);
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .card-icon {
            font-size: 1.25rem;
            color: var(--primary);
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
        }

        .card-content {
            padding: var(--space-4);
        }

        .card-content p {
            margin-bottom: var(--space-4);
            line-height: 1.6;
        }

        /* Tags */
        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-2);
        }

        .tag {
            background-color: rgba(14, 165, 233, 0.1);
            color: var(--primary);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Feature List */
        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            margin-bottom: var(--space-2);
        }

        .feature-icon {
            color: var(--success);
        }

        /* File Structure */
        .file-structure {
            font-family: var(--font-mono);
            font-size: 0.875rem;
        }

        .file-structure-item {
            margin-bottom: var(--space-2);
        }

        .file-structure-header {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-1) 0;
        }

        .file-icon {
            color: var(--primary);
            width: 16px;
        }

        .file-name {
            color: var(--text-primary);
        }

        .file-structure-children {
            margin-left: var(--space-6);
            padding-left: var(--space-4);
            border-left: 1px dashed var(--border);
        }

        /* Alerts */
        .alert {
            display: flex;
            padding: var(--space-4);
            border-radius: var(--radius-md);
            margin-bottom: var(--space-4);
            gap: var(--space-4);
        }

        .alert-info {
            background-color: rgba(14, 165, 233, 0.1);
            border-left: 4px solid var(--primary);
        }

        .alert-warning {
            background-color: rgba(245, 158, 11, 0.1);
            border-left: 4px solid var(--warning);
        }

        .alert-icon {
            font-size: 1.5rem;
            color: var(--primary);
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: var(--space-1);
        }

        /* Code Blocks */
        .code-block {
            background-color: var(--bg-surface);
            border-radius: var(--radius-md);
            border: 1px solid var(--border);
            overflow: hidden;
            margin-bottom: var(--space-4);
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-3) var(--space-4);
            background-color: var(--bg-main);
            border-bottom: 1px solid var(--border);
        }

        .code-title {
            font-weight: 500;
            font-size: 0.875rem;
        }

        .code-actions {
            display: flex;
            gap: var(--space-2);
        }

        .code-action-button {
            display: flex;
            align-items: center;
            gap: var(--space-1);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius);
            background-color: var(--bg-surface);
            border: 1px solid var(--border);
            font-size: 0.75rem;
            cursor: pointer;
            transition: all var(--transition);
        }

        .code-action-button:hover {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .code-content {
            padding: var(--space-4);
            margin: 0;
            overflow-x: auto;
            font-family: var(--font-mono);
            font-size: 0.875rem;
            line-height: 1.6;
            background-color: var(--bg-surface);
            color: var(--text-primary);
        }

        code {
            font-family: var(--font-mono);
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius);
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all var(--transition);
            border: none;
            background: none;
            color: var(--text-primary);
        }

        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: var(--radius);
            cursor: pointer;
            transition: all var(--transition);
            border: none;
            background: none;
            color: var(--text-primary);
        }

        .btn-icon:hover, .btn:hover {
            background-color: var(--bg-surface-hover);
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
        }

        /* Mobile Menu Toggle */
        .menu-toggle {
            display: none;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .nav-drawer {
                position: fixed;
                top: 60px;
                left: 0;
                z-index: var(--z-drawer);
                transform: translateX(-100%);
                box-shadow: var(--shadow-lg);
            }

            .nav-drawer.open {
                transform: translateX(0);
            }

            .menu-toggle {
                display: inline-flex;
            }

            .overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: calc(var(--z-drawer) - 1);
                opacity: 0;
                pointer-events: none;
                transition: opacity var(--transition);
            }

            .overlay.active {
                opacity: 1;
                pointer-events: auto;
            }
        }
    </style>
</head>
<body>
    <div class="app">
        <header class="header">
            <a href="#" class="logo">
                <i class="fas fa-file-code logo-icon"></i>
                <span>SOCIAL.ent.apc</span>
            </a>
            <div class="header-actions">
                <button class="btn-icon menu-toggle" id="menuToggle" aria-label="Toggle menu">
                    <i class="fas fa-bars"></i>
                </button>
                <button class="btn-icon" id="themeToggle" aria-label="Toggle theme">
                    <i class="fas fa-moon"></i>
                </button>
                <button class="btn" id="exportPdf">
                    <i class="fas fa-file-pdf"></i>
                    <span>Export PDF</span>
                </button>
                <button class="btn" id="exportCodebook">
                    <i class="fas fa-book"></i>
                    <span>Codebook</span>
                </button>
            </div>
        </header>

        <div class="container">
            <div class="overlay" id="overlay"></div>
            <nav class="nav-drawer" id="navDrawer">
                <div class="nav-header">
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" id="searchBox" class="search-input" placeholder="Search documentation...">
                        </div>
                    </div>
                </div>

                <div class="nav-sections">
                    <div class="nav-section">
                        <div class="nav-section-header">
                            <span>Getting Started</span>
                        </div>
                        <ul class="nav-list">
                            <li class="nav-item">
                                <a href="#overview" class="nav-link active">
                                    <i class="fas fa-home nav-icon"></i>
                                    <span>Overview</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#structure" class="nav-link">
                                    <i class="fas fa-sitemap nav-icon"></i>
                                    <span>File Structure</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-header">
                            <span>Core Components</span>
                        </div>
                        <ul class="nav-list">
                            <li class="nav-item">
                                <a href="#variables" class="nav-link">
                                    <i class="fas fa-cube nav-icon"></i>
                                    <span>Variables</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#functions" class="nav-link">
                                    <i class="fas fa-code nav-icon"></i>
                                    <span>Functions</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#validation" class="nav-link">
                                    <i class="fas fa-check-circle nav-icon"></i>
                                    <span>Validation Rules</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="nav-section">
                        <div class="nav-section-header">
                            <span>Advanced Topics</span>
                        </div>
                        <ul class="nav-list">
                            <li class="nav-item">
                                <a href="#patterns" class="nav-link">
                                    <i class="fas fa-puzzle-piece nav-icon"></i>
                                    <span>Design Patterns</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#limitations" class="nav-link">
                                    <i class="fas fa-exclamation-triangle nav-icon"></i>
                                    <span>Limitations</span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#conclusion" class="nav-link">
                                    <i class="fas fa-flag-checkered nav-icon"></i>
                                    <span>Conclusion</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="nav-footer">
                    <div class="version-info">
                        <span>Documentation v1.0</span>
                    </div>
                </div>
            </nav>

            <main class="content" id="content">
                <section id="overview" class="content-section">
                    <div class="section-header">
                        <h1>SOCIAL.ent.apc Documentation</h1>
                        <p class="section-description">
                            Comprehensive documentation for the CSPro application logic file used in the Algerian National Consumption Survey.
                        </p>
                    </div>

                    <div class="card-grid">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-info-circle card-icon"></i>
                                <h2 class="card-title">Overview</h2>
                            </div>
                            <div class="card-content">
                                <p>The SOCIAL.ent.apc file is the main application logic file for a household survey application developed in CSPro. It contains all the logic for data validation, skip patterns, and dynamic questionnaire behavior.</p>
                                <div class="tag-container">
                                    <span class="tag">CSPro</span>
                                    <span class="tag">Survey</span>
                                    <span class="tag">Household Data</span>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-cogs card-icon"></i>
                                <h2 class="card-title">Key Features</h2>
                            </div>
                            <div class="card-content">
                                <ul class="feature-list">
                                    <li><i class="fas fa-check feature-icon"></i> Multi-level data validation</li>
                                    <li><i class="fas fa-check feature-icon"></i> Dynamic questionnaire flow</li>
                                    <li><i class="fas fa-check feature-icon"></i> GPS integration</li>
                                    <li><i class="fas fa-check feature-icon"></i> Household roster management</li>
                                    <li><i class="fas fa-check feature-icon"></i> Complex skip patterns</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="structure" class="content-section">
                    <div class="section-header">
                        <h2>File Structure</h2>
                        <p class="section-description">
                            The organization and structure of the SOCIAL.ent.apc file.
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-content">
                            <div class="file-structure">
                                <div class="file-structure-item">
                                    <div class="file-structure-header">
                                        <i class="fas fa-folder file-icon"></i>
                                        <span class="file-name">SOCIAL.ent.apc</span>
                                    </div>
                                    <div class="file-structure-children">
                                        <div class="file-structure-item">
                                            <div class="file-structure-header">
                                                <i class="fas fa-code file-icon"></i>
                                                <span class="file-name">PROC GLOBAL</span>
                                            </div>
                                        </div>
                                        <div class="file-structure-item">
                                            <div class="file-structure-header">
                                                <i class="fas fa-code file-icon"></i>
                                                <span class="file-name">PROC HOUSEHOLD</span>
                                            </div>
                                        </div>
                                        <div class="file-structure-item">
                                            <div class="file-structure-header">
                                                <i class="fas fa-code file-icon"></i>
                                                <span class="file-name">PROC MEMBER</span>
                                            </div>
                                        </div>
                                        <div class="file-structure-item">
                                            <div class="file-structure-header">
                                                <i class="fas fa-code file-icon"></i>
                                                <span class="file-name">PROC EDUCATION</span>
                                            </div>
                                        </div>
                                        <div class="file-structure-item">
                                            <div class="file-structure-header">
                                                <i class="fas fa-code file-icon"></i>
                                                <span class="file-name">PROC HEALTH</span>
                                            </div>
                                        </div>
                                        <div class="file-structure-item">
                                            <div class="file-structure-header">
                                                <i class="fas fa-code file-icon"></i>
                                                <span class="file-name">PROC HOUSING</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle alert-icon"></i>
                        <div class="alert-content">
                            <h3 class="alert-title">File Organization</h3>
                            <p>The file is organized into logical sections corresponding to different parts of the questionnaire. Each section contains procedures for validation and skip logic specific to that section.</p>
                        </div>
                    </div>
                </section>

                <!-- Code example section with syntax highlighting -->
                <section id="code-example" class="content-section">
                    <div class="section-header">
                        <h2>Code Example</h2>
                        <p class="section-description">
                            Example of validation logic from the SOCIAL.ent.apc file.
                        </p>
                    </div>

                    <div class="card">
                        <div class="card-content">
                            <div class="code-block">
                                <div class="code-header">
                                    <span class="code-title">Household Member Validation</span>
                                    <div class="code-actions">
                                        <button class="code-action-button" id="copyCode">
                                            <i class="fas fa-copy"></i>
                                            <span>Copy</span>
                                        </button>
                                    </div>
                                </div>
                                <pre class="code-content"><code>PROC MEMBER

preproc

// Initialize household member count
numeric memberCount = 0;

postproc

// Validate household composition
if memberCount = 0 then
    errmsg("Household must have at least one member");
    reenter;
endif;

// Check for household head
numeric hasHead = 0;
do numeric i = 1 while i &lt;= memberCount
    if RELATIONSHIP(i) = 1 then
        hasHead = hasHead + 1;
    endif;
enddo;

if hasHead &lt;&gt; 1 then
    errmsg("Household must have exactly one head");
    reenter;
endif;</code></pre>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const themeToggle = document.getElementById('themeToggle');
            const menuToggle = document.getElementById('menuToggle');
            const navDrawer = document.getElementById('navDrawer');
            const overlay = document.getElementById('overlay');
            const navLinks = document.querySelectorAll('.nav-link');
            const searchBox = document.getElementById('searchBox');
            const copyCodeBtn = document.getElementById('copyCode');
            const exportPdfBtn = document.getElementById('exportPdf');
            const exportCodebookBtn = document.getElementById('exportCodebook');

            // Theme Management
            const initTheme = () => {
                const savedTheme = localStorage.getItem('theme') ||
                    (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

                document.body.setAttribute('data-theme', savedTheme);
                const icon = themeToggle.querySelector('i');
                icon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            };

            themeToggle.addEventListener('click', () => {
                const currentTheme = document.body.getAttribute('data-theme') || 'light';
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                // Add transition class for smooth theme change
                document.documentElement.classList.add('theme-transition');

                // Set the new theme
                document.body.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                // Update icon with animation
                const icon = themeToggle.querySelector('i');
                icon.style.transform = 'rotate(360deg)';
                setTimeout(() => {
                    icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
                    icon.style.transform = '';
                }, 200);

                // Remove transition class after transition completes
                setTimeout(() => {
                    document.documentElement.classList.remove('theme-transition');
                }, 300);
            });

            // Mobile Navigation
            menuToggle.addEventListener('click', () => {
                navDrawer.classList.toggle('open');
                overlay.classList.toggle('active');

                // Update icon
                const icon = menuToggle.querySelector('i');
                icon.className = navDrawer.classList.contains('open') ? 'fas fa-times' : 'fas fa-bars';
            });

            overlay.addEventListener('click', () => {
                navDrawer.classList.remove('open');
                overlay.classList.remove('active');

                // Reset icon
                const icon = menuToggle.querySelector('i');
                icon.className = 'fas fa-bars';
            });

            // Smooth Scrolling Navigation
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();

                    // Get target section
                    const targetId = link.getAttribute('href');
                    const targetSection = document.querySelector(targetId);

                    if (targetSection) {
                        // Update active link
                        navLinks.forEach(l => l.classList.remove('active'));
                        link.classList.add('active');

                        // Smooth scroll to target
                        window.scrollTo({
                            top: targetSection.offsetTop - 80, // Account for header
                            behavior: 'smooth'
                        });

                        // Close mobile menu if open
                        if (window.innerWidth <= 768 && navDrawer.classList.contains('open')) {
                            navDrawer.classList.remove('open');
                            overlay.classList.remove('active');
                            menuToggle.querySelector('i').className = 'fas fa-bars';
                        }
                    }
                });
            });

            // Search Functionality
            if (searchBox) {
                searchBox.addEventListener('input', () => {
                    const searchTerm = searchBox.value.toLowerCase().trim();

                    if (searchTerm.length > 1) {
                        // Search in navigation items
                        navLinks.forEach(link => {
                            const text = link.textContent.toLowerCase();
                            const navItem = link.closest('.nav-item');

                            if (text.includes(searchTerm)) {
                                navItem.style.display = 'block';
                                // Highlight matching text
                                const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                                const linkText = link.querySelector('span');
                                if (linkText) {
                                    linkText.innerHTML = linkText.textContent.replace(
                                        regex,
                                        '<mark>$1</mark>'
                                    );
                                }
                            } else {
                                navItem.style.display = 'none';
                            }
                        });
                    } else {
                        // Reset display and remove highlights
                        navLinks.forEach(link => {
                            const navItem = link.closest('.nav-item');
                            navItem.style.display = 'block';
                            const linkText = link.querySelector('span');
                            if (linkText) {
                                linkText.innerHTML = linkText.textContent;
                            }
                        });
                    }
                });
            }

            // Copy Code Button
            if (copyCodeBtn) {
                copyCodeBtn.addEventListener('click', () => {
                    const codeBlock = document.querySelector('.code-content');
                    if (codeBlock) {
                        const code = codeBlock.textContent;
                        navigator.clipboard.writeText(code).then(() => {
                            // Show success feedback
                            const originalText = copyCodeBtn.querySelector('span').textContent;
                            copyCodeBtn.querySelector('span').textContent = 'Copied!';
                            copyCodeBtn.querySelector('i').className = 'fas fa-check';

                            // Reset after 2 seconds
                            setTimeout(() => {
                                copyCodeBtn.querySelector('span').textContent = originalText;
                                copyCodeBtn.querySelector('i').className = 'fas fa-copy';
                            }, 2000);
                        });
                    }
                });
            }

            // Export PDF Functionality
            if (exportPdfBtn) {
                exportPdfBtn.addEventListener('click', () => {
                    // Show loading state
                    exportPdfBtn.disabled = true;
                    const originalText = exportPdfBtn.querySelector('span').textContent;
                    exportPdfBtn.querySelector('span').textContent = 'Generating...';

                    // Create a clone of the content for PDF generation
                    const content = document.querySelector('.content');
                    const clone = content.cloneNode(true);

                    // Apply PDF-specific styling
                    const style = document.createElement('style');
                    style.textContent = `
                        body { font-family: Arial, sans-serif; color: #333; }
                        .content-section { page-break-after: always; }
                        .card { box-shadow: none; border: 1px solid #ddd; }
                        .code-content { white-space: pre-wrap; }
                    `;
                    clone.prepend(style);

                    // Configure PDF options
                    const options = {
                        margin: 10,
                        filename: 'SOCIAL.ent.apc-documentation.pdf',
                        image: { type: 'jpeg', quality: 0.98 },
                        html2canvas: { scale: 2 },
                        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                    };

                    // Generate PDF
                    html2pdf().from(clone).set(options).save().then(() => {
                        // Reset button state
                        exportPdfBtn.disabled = false;
                        exportPdfBtn.querySelector('span').textContent = originalText;
                    });
                });
            }

            // Export Codebook Functionality
            if (exportCodebookBtn) {
                exportCodebookBtn.addEventListener('click', () => {
                    // Show loading state
                    exportCodebookBtn.disabled = true;
                    const originalText = exportCodebookBtn.querySelector('span').textContent;
                    exportCodebookBtn.querySelector('span').textContent = 'Generating...';

                    // Create codebook content
                    const codebookContent = document.createElement('div');
                    codebookContent.innerHTML = `
                        <h1 style="text-align: center; margin-bottom: 30px;">SOCIAL.ent.apc Codebook</h1>
                        <p style="text-align: center; margin-bottom: 50px;">Generated on ${new Date().toLocaleDateString()}</p>

                        <h2>Variable Definitions</h2>
                        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                            <thead>
                                <tr style="background-color: #f3f4f6;">
                                    <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Variable</th>
                                    <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Type</th>
                                    <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 10px;">QHREGION</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Numeric</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Region code</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 10px;">QHPROV</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Numeric</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Province code</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 10px;">QHDISTRICT</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Numeric</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">District code</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 10px;">QHHMEMBER</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Numeric</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Number of household members</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #ddd; padding: 10px;">RELATIONSHIP</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Numeric</td>
                                    <td style="border: 1px solid #ddd; padding: 10px;">Relationship to household head</td>
                                </tr>
                            </tbody>
                        </table>
                    `;

                    // Configure PDF options
                    const options = {
                        margin: 15,
                        filename: 'SOCIAL.ent.apc-codebook.pdf',
                        image: { type: 'jpeg', quality: 0.98 },
                        html2canvas: { scale: 2 },
                        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                    };

                    // Generate PDF
                    html2pdf().from(codebookContent).set(options).save().then(() => {
                        // Reset button state
                        exportCodebookBtn.disabled = false;
                        exportCodebookBtn.querySelector('span').textContent = originalText;
                    });
                });
            }

            // Scroll Spy for Navigation
            const scrollSpy = () => {
                const sections = document.querySelectorAll('.content-section');
                const scrollPosition = window.scrollY + 100; // Offset for header

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionBottom = sectionTop + section.offsetHeight;

                    if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                        const id = section.getAttribute('id');
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === `#${id}`) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            };

            window.addEventListener('scroll', scrollSpy);

            // Initialize
            initTheme();
            scrollSpy(); // Initial call to highlight the current section
        });
    </script>
</body>
</html>
