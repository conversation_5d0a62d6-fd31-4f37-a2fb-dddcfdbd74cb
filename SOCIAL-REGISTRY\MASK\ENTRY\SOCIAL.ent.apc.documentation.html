<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOCIAL.ent.apc Documentation</title>
    <!-- Include html2pdf.js for PDF export functionality -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js" integrity="sha512-GsLlZN/3F2ErC5ifS5QtgpiJtWd43JWSuIgh7mbzZ8zBps+dvLusV+eNQATqgA/HdeKFVgA5v3S/cIrLF7QnIg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <style>
        :root {
            /* Modern Color Palette */
            --primary-color: #3b82f6;
            --primary-light: #60a5fa;
            --primary-dark: #2563eb;
            --secondary-color: #8b5cf6;
            --accent-color: #f43f5e;

            /* UI Colors - Minimalist */
            --background-color: #ffffff;
            --surface-color: #f8fafc;
            --text-color: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;

            /* Component Colors - Cleaner */
            --code-background: #f1f5f9;
            --code-text: #ef4444;
            --table-header-bg: #f8fafc;
            --table-alt-row: #f1f5f9;
            --sidebar-bg: #f8fafc;
            --sidebar-active: #e0f2fe;
            --scrollbar-thumb: #cbd5e1;

            /* Callout Colors - Subtle */
            --note-bg: rgba(59, 130, 246, 0.05);
            --note-border: #3b82f6;
            --warning-bg: rgba(244, 63, 94, 0.05);
            --warning-border: #f43f5e;
            --tip-bg: rgba(139, 92, 246, 0.05);
            --tip-border: #8b5cf6;

            /* Subtle Gradients */
            --gradient-primary: linear-gradient(135deg, #3b82f6, #2563eb);
            --gradient-secondary: linear-gradient(135deg, #8b5cf6, #7c3aed);
            --gradient-accent: linear-gradient(135deg, #f43f5e, #e11d48);

            /* Smooth Transitions */
            --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 0.4s cubic-bezier(0.4, 0, 0.2, 1);

            /* Subtle Shadows */
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.03);
            --shadow-md: 0 3px 6px rgba(0, 0, 0, 0.05);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.04);

            /* Improved Spacing */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.75rem;
            --space-xl: 2.5rem;

            /* Consistent Border Radius */
            --radius-sm: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;

            /* Typography */
            --font-sans: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, Monaco, 'Andale Mono', monospace;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-md: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --line-height-tight: 1.25;
            --line-height-normal: 1.5;
            --line-height-relaxed: 1.75;
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
        }

        [data-theme="dark"] {
            /* Modern Dark Theme Colors */
            --primary-color: #60a5fa;
            --primary-light: #93c5fd;
            --primary-dark: #3b82f6;
            --secondary-color: #a78bfa;
            --accent-color: #fb7185;

            /* UI Colors - Dark Minimalist */
            --background-color: #0f172a;
            --surface-color: #1e293b;
            --text-color: #f1f5f9;
            --text-secondary: #94a3b8;
            --border-color: #334155;

            /* Component Colors - Dark Mode */
            --code-background: #1e293b;
            --code-text: #f87171;
            --table-header-bg: #1e293b;
            --table-alt-row: #0f172a;
            --sidebar-bg: #1e293b;
            --sidebar-active: #2563eb20;
            --scrollbar-thumb: #475569;

            /* Callout Colors - Dark Mode */
            --note-bg: rgba(59, 130, 246, 0.08);
            --note-border: #60a5fa;
            --warning-bg: rgba(244, 63, 94, 0.08);
            --warning-border: #fb7185;
            --tip-bg: rgba(139, 92, 246, 0.08);
            --tip-border: #a78bfa;

            /* Subtle Gradients - Dark Mode */
            --gradient-primary: linear-gradient(135deg, #60a5fa, #3b82f6);
            --gradient-secondary: linear-gradient(135deg, #a78bfa, #8b5cf6);
            --gradient-accent: linear-gradient(135deg, #fb7185, #f43f5e);
        }

        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

        /* Smooth theme transition */
        .theme-transition * {
            transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                        color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                        border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                        box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        /* Reset with improved transitions */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            transition: background-color var(--transition-normal),
                        color var(--transition-normal),
                        border-color var(--transition-normal),
                        box-shadow var(--transition-normal),
                        transform var(--transition-fast);
        }

        /* Search highlight styling - more subtle */
        mark {
            background-color: rgba(59, 130, 246, 0.15);
            color: inherit;
            padding: 0 2px;
            border-radius: 2px;
        }

        /* Modern typography base */
        body {
            font-family: var(--font-sans);
            font-size: var(--font-size-md);
            line-height: var(--line-height-normal);
            font-weight: var(--font-weight-normal);
            color: var(--text-color);
            background-color: var(--background-color);
            display: flex;
            min-height: 100vh;
            overflow-x: hidden;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        /* Selection styling - subtle */
        ::selection {
            background-color: var(--primary-color);
            color: white;
            text-shadow: none;
        }

        /* Minimalist Sidebar Navigation */
        .sidebar {
            width: 260px;
            background-color: var(--sidebar-bg);
            height: 100vh;
            position: fixed;
            overflow-y: auto;
            padding: var(--space-lg) 0;
            border-right: 1px solid var(--border-color);
            scrollbar-width: thin;
            scrollbar-color: var(--scrollbar-thumb) transparent;
            z-index: 100;
            transition: transform var(--transition-normal), box-shadow var(--transition-normal);
        }

        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: var(--scrollbar-thumb);
            border-radius: var(--radius-md);
        }

        .sidebar-header {
            padding: 0 var(--space-lg) var(--space-lg);
            border-bottom: 1px solid var(--border-color);
            margin-bottom: var(--space-lg);
            position: relative;
        }

        .sidebar-header::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: var(--space-lg);
            width: 40px;
            height: 2px;
            background: var(--primary-color);
            border-radius: var(--radius-sm);
        }

        .sidebar-header h2 {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--primary-color);
            margin-bottom: var(--space-sm);
            letter-spacing: -0.01em;
        }

        /* Minimalist Theme Toggle */
        .theme-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: var(--space-md);
            cursor: pointer;
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--radius-md);
            background-color: var(--surface-color);
            border: 1px solid var(--border-color);
            transition: all var(--transition-normal);
        }

        .theme-toggle:hover {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .theme-toggle-icon {
            width: 18px;
            height: 18px;
            margin-right: var(--space-sm);
            transition: transform var(--transition-normal);
        }

        .theme-toggle:hover .theme-toggle-icon {
            transform: rotate(15deg);
        }

        /* Clean Navigation Lists */
        .nav-list {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 1px;
            position: relative;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: var(--space-sm) var(--space-lg);
            color: var(--text-color);
            text-decoration: none;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            transition: all var(--transition-normal);
            position: relative;
            border-left: 2px solid transparent;
        }

        .nav-link:hover {
            color: var(--primary-color);
            background-color: var(--sidebar-active);
        }

        .nav-link.active {
            color: var(--primary-color);
            background-color: var(--sidebar-active);
            border-left-color: var(--primary-color);
            font-weight: var(--font-weight-semibold);
        }

        .nav-subitem {
            margin-left: var(--space-md);
        }

        /* Minimalist Search Box */
        .search-container {
            padding: 0 var(--space-lg) var(--space-md);
            margin-bottom: var(--space-md);
        }

        .search-box {
            width: 100%;
            padding: var(--space-sm) var(--space-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background-color: var(--surface-color);
            color: var(--text-color);
            font-family: inherit;
            font-size: var(--font-size-sm);
            transition: all var(--transition-normal);
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        .search-box::placeholder {
            color: var(--text-secondary);
        }

        /* Minimalist Main Content */
        .main-content {
            flex: 1;
            margin-left: 260px;
            padding: var(--space-xl);
            max-width: calc(100% - 260px);
            position: relative;
            background-image:
                radial-gradient(circle at 5% 10%, rgba(59, 130, 246, 0.02) 0%, transparent 15%),
                radial-gradient(circle at 95% 90%, rgba(139, 92, 246, 0.02) 0%, transparent 15%);
        }

        /* Modern Typography System */
        h1, h2, h3, h4, h5, h6 {
            margin-top: 2em;
            margin-bottom: 0.75em;
            font-weight: var(--font-weight-semibold);
            line-height: var(--line-height-tight);
            letter-spacing: -0.01em;
            color: var(--text-color);
        }

        h1 {
            font-size: var(--font-size-3xl);
            margin-top: 0;
            margin-bottom: var(--space-xl);
            position: relative;
            padding-bottom: var(--space-md);
            color: var(--text-color);
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--primary-color);
            border-radius: var(--radius-sm);
        }

        h2 {
            font-size: var(--font-size-2xl);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: var(--space-sm);
            position: relative;
            color: var(--text-color);
        }

        h2::before {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 30px;
            height: 2px;
            background: var(--primary-color);
            border-radius: var(--radius-sm);
        }

        h3 {
            font-size: var(--font-size-xl);
            position: relative;
            color: var(--text-color);
        }

        h3::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 20px;
            height: 2px;
            background-color: var(--primary-color);
            opacity: 0.5;
            border-radius: var(--radius-sm);
        }

        h4 {
            font-size: var(--font-size-lg);
            position: relative;
            color: var(--text-color);
            margin-bottom: var(--space-md);
        }

        h5, h6 {
            font-size: var(--font-size-md);
            color: var(--text-color);
        }

        p {
            margin-bottom: 1.5em;
            line-height: var(--line-height-relaxed);
            color: var(--text-color);
            max-width: 70ch;
        }

        /* Text utilities */
        .text-primary { color: var(--primary-color); }
        .text-secondary { color: var(--secondary-color); }
        .text-accent { color: var(--accent-color); }
        .text-muted { color: var(--text-secondary); }

        .text-sm { font-size: var(--font-size-sm); }
        .text-lg { font-size: var(--font-size-lg); }
        .text-xl { font-size: var(--font-size-xl); }

        .font-medium { font-weight: var(--font-weight-medium); }
        .font-semibold { font-weight: var(--font-weight-semibold); }
        .font-bold { font-weight: var(--font-weight-bold); }

        /* Minimalist section styling with subtle transitions */
        section {
            margin-bottom: var(--space-xl);
            padding-bottom: var(--space-xl);
            border-bottom: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
            transition: opacity 0.4s ease-out, transform 0.4s ease-out;
        }

        /* Initial state for sections before they become visible */
        section:not(.visible) {
            opacity: 0;
            transform: translateY(15px);
        }

        section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        section:last-child {
            border-bottom: none;
        }

        /* Subtle section indicator */
        section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 0;
            background: var(--primary-color);
            transition: height 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0.7;
            border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
        }

        section.visible::before {
            height: 40px;
        }

        /* Refined staggered animation for section children */
        section > * {
            opacity: 0;
            transform: translateY(8px);
            transition: opacity 0.4s ease-out, transform 0.4s ease-out;
        }

        section.visible > * {
            opacity: 1;
            transform: translateY(0);
        }

        section.visible > *:nth-child(1) { transition-delay: 0.05s; }
        section.visible > *:nth-child(2) { transition-delay: 0.1s; }
        section.visible > *:nth-child(3) { transition-delay: 0.15s; }
        section.visible > *:nth-child(4) { transition-delay: 0.2s; }
        section.visible > *:nth-child(5) { transition-delay: 0.25s; }
        section.visible > *:nth-child(n+6) { transition-delay: 0.3s; }

        /* Section focus effect */
        .section-focus {
            position: relative;
        }

        .section-focus::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--primary-color);
            opacity: 0;
            z-index: -1;
            border-radius: var(--radius-sm);
            animation: sectionFocusPulse 1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes sectionFocusPulse {
            0% { opacity: 0; }
            30% { opacity: 0.03; }
            70% { opacity: 0.03; }
            100% { opacity: 0; }
        }

        /* Subtle animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(8px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(15px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes scaleIn {
            from { opacity: 0; transform: scale(0.98); }
            to { opacity: 1; transform: scale(1); }
        }

        /* Smooth morphing transitions for section changes - fixed distortion */
        .section-transition-active {
            position: relative;
            animation: sectionMorph 0.8s cubic-bezier(0.22, 1, 0.36, 1) forwards;
            transform-origin: center;
            will-change: transform, opacity;
            z-index: 2;
        }

        .section-transition-active::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(67, 97, 238, 0.2), transparent 70%);
            opacity: 0;
            z-index: 0;
            animation: pulseGradient 1.2s cubic-bezier(0.22, 1, 0.36, 1) forwards;
            pointer-events: none;
            border-radius: var(--radius-md);
        }

        /* Section focus effect when navigating */
        .section-focus {
            position: relative;
            animation: sectionFocus 1.2s cubic-bezier(0.22, 1, 0.36, 1);
            box-shadow: 0 0 0 rgba(67, 97, 238, 0);
        }

        .section-focus::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--gradient-primary);
            transform: scaleY(0);
            transform-origin: top;
            animation: focusBarSlide 0.6s 0.2s cubic-bezier(0.22, 1, 0.36, 1) forwards;
            z-index: 1;
            border-radius: 2px;
        }

        .section-focus::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 3px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transform-origin: right;
            animation: focusTopSlide 0.6s 0.3s cubic-bezier(0.22, 1, 0.36, 1) forwards;
            z-index: 1;
            border-radius: 2px;
        }

        @keyframes focusBarSlide {
            0% { transform: scaleY(0); }
            100% { transform: scaleY(1); }
        }

        @keyframes focusTopSlide {
            0% { transform: scaleX(0); }
            100% { transform: scaleX(1); }
        }

        @keyframes sectionFocus {
            0% {
                background-color: transparent;
                box-shadow: 0 0 0 rgba(67, 97, 238, 0);
            }
            30% {
                background-color: rgba(67, 97, 238, 0.08);
                box-shadow: 0 0 15px rgba(67, 97, 238, 0.1);
            }
            100% {
                background-color: transparent;
                box-shadow: 0 0 0 rgba(67, 97, 238, 0);
            }
        }

        /* Fixed animation to prevent distortion */
        @keyframes sectionMorph {
            0% {
                transform: scale(0.98);
                opacity: 0.8;
            }
            30% {
                transform: scale(1.01);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes pulseGradient {
            0% {
                opacity: 0;
                transform: scale(0.9);
            }
            40% {
                opacity: 0.7;
                transform: scale(1.03);
            }
            100% {
                opacity: 0;
                transform: scale(1.1);
            }
        }

        /* Code Blocks */
        code {
            font-family: 'JetBrains Mono', 'Fira Code', Consolas, Monaco, 'Andale Mono', monospace;
            background-color: var(--code-background);
            padding: 2px 6px;
            border-radius: var(--radius-sm);
            font-size: 0.9em;
            color: var(--code-text);
            font-weight: 500;
            transition: all var(--transition-normal);
        }

        code:hover {
            background-color: rgba(67, 97, 238, 0.1);
        }

        pre {
            background-color: var(--code-background);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--space-lg);
            overflow: auto;
            margin: var(--space-lg) 0;
            position: relative;
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-normal);
        }

        pre:hover {
            box-shadow: var(--shadow-md);
        }

        /* Code block header */
        pre::before {
            content: 'CSPro Code';
            position: absolute;
            top: 0;
            right: var(--space-md);
            background: var(--gradient-primary);
            color: white;
            padding: 2px var(--space-sm);
            font-size: 0.7rem;
            border-radius: 0 0 var(--radius-sm) var(--radius-sm);
            font-weight: 600;
            letter-spacing: 0.5px;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background-color: transparent;
            padding: 0;
            color: var(--text-color);
            font-size: 0.9rem;
            line-height: 1.6;
            display: block;
            font-weight: 400;
        }

        /* Minimalist code blocks */
        .code-container {
            margin: var(--space-lg) 0;
            position: relative;
            transition: all var(--transition-normal);
            border-radius: var(--radius-md);
        }

        .code-container:hover {
            box-shadow: var(--shadow-sm);
        }

        .code-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--surface-color);
            padding: var(--space-sm) var(--space-md);
            border: 1px solid var(--border-color);
            border-bottom: none;
            border-radius: var(--radius-md) var(--radius-md) 0 0;
            cursor: pointer;
            transition: all var(--transition-normal);
            position: relative;
        }

        .code-header:hover {
            background-color: var(--sidebar-active);
        }

        .code-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary-color);
            transform: scaleY(0);
            transform-origin: bottom;
            transition: transform var(--transition-normal);
            opacity: 0.7;
        }

        .code-header:hover::before {
            transform: scaleY(1);
        }

        .code-title {
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-sm);
            color: var(--text-color);
            display: flex;
            align-items: center;
            transition: transform var(--transition-normal);
        }

        .code-header:hover .code-title {
            color: var(--primary-color);
            transform: translateX(6px);
        }

        .code-title::before {
            content: '📄';
            margin-right: 8px;
            font-size: var(--font-size-md);
            opacity: 0.7;
            transition: transform var(--transition-normal);
        }

        .code-toggle {
            color: var(--text-secondary);
            font-size: var(--font-size-md);
            transition: all var(--transition-normal);
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .code-header:hover .code-toggle {
            color: var(--primary-color);
        }

        .code-toggle.collapsed {
            transform: rotate(-90deg);
        }

        .code-content {
            overflow: hidden;
            transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            max-height: 1000px;
            position: relative;
        }

        .code-content.collapsed {
            max-height: 0;
        }

        .code-content pre {
            margin: 0;
            border-top: none;
            border-radius: 0 0 var(--radius-md) var(--radius-md);
            font-family: var(--font-mono);
            font-size: var(--font-size-sm);
            line-height: 1.6;
            padding: var(--space-md);
            background-color: var(--code-background);
            border: 1px solid var(--border-color);
            overflow-x: auto;
        }

        /* Subtle code block actions */
        .code-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transform: translateY(-5px);
            transition: opacity var(--transition-normal), transform var(--transition-normal);
            z-index: 10;
        }

        .code-container:hover .code-actions {
            opacity: 1;
            transform: translateY(0);
        }

        .code-action-button {
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            padding: 3px 8px;
            font-size: var(--font-size-xs);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all var(--transition-normal);
            color: var(--text-secondary);
        }

        .code-action-button:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Subtle line highlighting for code */
        .highlight-line {
            background-color: rgba(59, 130, 246, 0.08);
            display: block;
            margin: 0 -16px;
            padding: 0 16px;
            border-left: 2px solid var(--primary-color);
        }

        /* Minimalist code explanation */
        .code-explanation {
            background-color: var(--surface-color);
            border-radius: var(--radius-md);
            padding: var(--space-md);
            margin-top: var(--space-md);
            font-size: var(--font-size-sm);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
        }

        /* Minimalist Tables */
        table {
            border-collapse: collapse;
            width: 100%;
            margin: var(--space-lg) 0;
            border-radius: var(--radius-md);
            overflow: hidden;
            border: 1px solid var(--border-color);
            font-size: var(--font-size-sm);
        }

        th, td {
            padding: var(--space-md);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background-color: var(--table-header-bg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-color);
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:nth-child(even) {
            background-color: var(--table-alt-row);
        }

        /* Subtle row hover effect */
        tr:not(:first-child):hover {
            background-color: rgba(59, 130, 246, 0.03);
        }

        /* Responsive tables */
        @media (max-width: 768px) {
            .table-container {
                overflow-x: auto;
                margin: var(--space-md) 0;
            }

            table {
                min-width: 600px;
            }
        }

        /* Lists */
        ul, ol {
            margin: 0 0 1.4em 1.2em;
            padding-left: var(--space-lg);
        }

        li {
            margin-bottom: 0.7em;
            position: relative;
        }

        ul li::before {
            content: '';
            position: absolute;
            left: -1.2em;
            top: 0.6em;
            width: 6px;
            height: 6px;
            background-color: var(--primary-color);
            border-radius: 50%;
        }

        ol {
            counter-reset: item;
            list-style-type: none;
        }

        ol li {
            counter-increment: item;
        }

        ol li::before {
            content: counter(item) ".";
            position: absolute;
            left: -1.5em;
            color: var(--primary-color);
            font-weight: 600;
        }

        /* Minimalist Notes and Callouts */
        .note, .warning, .tip, .tutorial {
            background-color: var(--note-bg);
            border-left: 3px solid var(--note-border);
            padding: var(--space-md) var(--space-lg);
            margin: var(--space-lg) 0;
            border-radius: var(--radius-sm);
            position: relative;
        }

        .note::before, .warning::before, .tip::before, .tutorial::before {
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            display: block;
            margin-bottom: var(--space-sm);
            letter-spacing: 0.05em;
            text-transform: uppercase;
        }

        .note::before {
            content: 'Note';
            color: var(--note-border);
        }

        .warning {
            background-color: var(--warning-bg);
            border-left-color: var(--warning-border);
        }

        .warning::before {
            content: 'Warning';
            color: var(--warning-border);
        }

        .tip {
            background-color: var(--tip-bg);
            border-left-color: var(--tip-border);
        }

        .tip::before {
            content: 'Tip';
            color: var(--tip-border);
        }

        /* Callout content styling */
        .note p:last-child,
        .warning p:last-child,
        .tip p:last-child,
        .tutorial p:last-child {
            margin-bottom: 0;
        }

        /* Subtle hover effect */
        .note:hover, .warning:hover, .tip:hover, .tutorial:hover {
            background-color: var(--surface-color);
        }

        /* Interactive Tutorial Components */
        .tutorial {
            background-color: rgba(114, 9, 183, 0.05);
            border-left-color: var(--secondary-color);
            padding-top: var(--space-md);
        }

        .tutorial::before {
            content: 'TUTORIAL';
            color: var(--secondary-color);
        }

        .tutorial::after {
            content: '';
            position: absolute;
            top: -50px;
            right: -50px;
            width: 100px;
            height: 100px;
            background: var(--gradient-accent);
            opacity: 0.05;
            border-radius: 50%;
            z-index: -1;
            transition: all 0.5s ease;
        }

        .tutorial:hover::after {
            transform: scale(1.2);
            opacity: 0.08;
        }

        .tutorial-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--space-md);
            padding-bottom: var(--space-sm);
            border-bottom: 1px dashed var(--border-color);
        }

        .tutorial-title {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--secondary-color);
            margin: 0;
            flex: 1;
        }

        .tutorial-steps {
            counter-reset: step;
            margin: var(--space-md) 0;
        }

        .tutorial-step {
            position: relative;
            padding: var(--space-md);
            margin-bottom: var(--space-md);
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            transition: all var(--transition-normal);
            counter-increment: step;
        }

        .tutorial-step:hover {
            background-color: rgba(255, 255, 255, 0.8);
            box-shadow: var(--shadow-sm);
        }

        .tutorial-step::before {
            content: 'Step ' counter(step);
            font-weight: 600;
            font-size: 0.8rem;
            color: var(--secondary-color);
            position: absolute;
            top: -10px;
            left: 10px;
            background-color: white;
            padding: 0 8px;
            border-radius: var(--radius-sm);
            border: 1px solid var(--border-color);
        }

        .tutorial-step-content {
            margin-top: var(--space-sm);
        }

        .tutorial-step-image {
            margin: var(--space-md) 0;
            border-radius: var(--radius-sm);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-normal);
        }

        .tutorial-step-image:hover {
            box-shadow: var(--shadow-md);
            transform: scale(1.02);
        }

        .tutorial-step-image img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        .tutorial-controls {
            display: flex;
            justify-content: space-between;
            margin-top: var(--space-md);
        }

        .tutorial-button {
            background-color: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--space-sm) var(--space-md);
            font-size: 0.9rem;
            cursor: pointer;
            transition: all var(--transition-normal);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tutorial-button:hover {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .tutorial-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .tutorial-progress {
            height: 4px;
            background-color: var(--border-color);
            border-radius: var(--radius-sm);
            margin: var(--space-md) 0;
            overflow: hidden;
        }

        .tutorial-progress-bar {
            height: 100%;
            background: var(--gradient-primary);
            width: 0;
            transition: width 0.5s ease;
        }

        /* Code Explanation */
        .code-explanation {
            background-color: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--space-lg);
            margin: var(--space-lg) 0;
            box-shadow: var(--shadow-sm);
            position: relative;
            transition: all var(--transition-normal);
        }

        .code-explanation:hover {
            box-shadow: var(--shadow-md);
        }

        .code-explanation h4 {
            margin-top: 0;
            margin-bottom: var(--space-md);
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .code-explanation h4::before {
            content: '⚙️';
            margin-right: var(--space-sm);
            font-size: 1.2rem;
        }

        /* Interactive Diagrams and Visualizations */
        .diagram-container {
            margin: var(--space-lg) 0;
            padding: var(--space-md);
            background-color: var(--surface-color);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .diagram-container:hover {
            box-shadow: var(--shadow-md);
        }

        .diagram-title {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--primary-color);
            margin-bottom: var(--space-md);
            padding-bottom: var(--space-sm);
            border-bottom: 1px dashed var(--border-color);
            display: flex;
            align-items: center;
        }

        .diagram-title::before {
            content: '📊';
            margin-right: var(--space-sm);
        }

        .diagram-content {
            position: relative;
            min-height: 200px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Flow Diagram */
        .flow-diagram {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--space-lg);
            width: 100%;
            padding: var(--space-md) 0;
        }

        .flow-node {
            background-color: white;
            border: 2px solid var(--primary-color);
            border-radius: var(--radius-md);
            padding: var(--space-md);
            text-align: center;
            position: relative;
            transition: all var(--transition-normal);
            cursor: pointer;
        }

        .flow-node:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
            background-color: rgba(67, 97, 238, 0.05);
        }

        .flow-node.active {
            background-color: rgba(67, 97, 238, 0.1);
            border-color: var(--primary-dark);
            transform: scale(1.05);
            z-index: 2;
        }

        .flow-node::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -30px;
            width: 30px;
            height: 2px;
            background-color: var(--primary-color);
            transform: translateY(-50%);
            opacity: 0.5;
        }

        .flow-node:last-child::after {
            display: none;
        }

        .flow-node-title {
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: var(--space-sm);
        }

        .flow-node-description {
            font-size: 0.8rem;
            color: var(--text-secondary);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .flow-node:hover .flow-node-description,
        .flow-node.active .flow-node-description {
            max-height: 100px;
        }

        /* Component Relationship Diagram */
        .component-diagram {
            position: relative;
            width: 100%;
            height: 300px;
            margin: var(--space-lg) 0;
        }

        .component-node {
            position: absolute;
            width: 120px;
            height: 60px;
            background-color: white;
            border: 2px solid var(--primary-color);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all var(--transition-normal);
            cursor: pointer;
            z-index: 2;
        }

        .component-node:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-md);
            z-index: 3;
        }

        .component-node.active {
            background-color: rgba(67, 97, 238, 0.1);
            border-color: var(--primary-dark);
        }

        .component-connection {
            position: absolute;
            background-color: var(--border-color);
            z-index: 1;
            transform-origin: 0 0;
            transition: all var(--transition-normal);
        }

        .component-connection.active {
            background-color: var(--primary-color);
            z-index: 2;
        }

        /* Interactive Code Playground */
        .code-playground {
            margin: var(--space-lg) 0;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            overflow: hidden;
        }

        .code-playground-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-sm) var(--space-md);
            background-color: var(--surface-color);
            border-bottom: 1px solid var(--border-color);
        }

        .code-playground-title {
            font-weight: 600;
            font-size: 0.9rem;
            color: var(--primary-color);
        }

        .code-playground-actions {
            display: flex;
            gap: var(--space-sm);
        }

        .code-playground-action {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-normal);
            padding: 4px 8px;
            border-radius: var(--radius-sm);
        }

        .code-playground-action:hover {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary-color);
        }

        .code-playground-editor {
            padding: var(--space-md);
            background-color: var(--code-background);
            min-height: 150px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            color: var(--text-color);
            white-space: pre;
            overflow-x: auto;
        }

        .code-playground-output {
            padding: var(--space-md);
            background-color: var(--surface-color);
            border-top: 1px solid var(--border-color);
            min-height: 80px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            color: var(--text-secondary);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .sidebar {
                width: 260px;
            }
            .main-content {
                margin-left: 260px;
                max-width: calc(100% - 260px);
            }

            .flow-diagram {
                grid-template-columns: 1fr;
                gap: var(--space-md);
            }

            .flow-node::after {
                top: auto;
                right: 50%;
                bottom: -20px;
                width: 2px;
                height: 20px;
                transform: translateX(50%);
            }
        }

        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
                height: 100vh;
                position: fixed;
                padding: var(--space-md);
                transform: translateX(-100%);
                transition: transform var(--transition-normal);
                box-shadow: none;
            }
            .sidebar.active {
                transform: translateX(0);
                box-shadow: var(--shadow-lg);
            }
            .main-content {
                margin-left: 0;
                max-width: 100%;
                padding: var(--space-lg);
            }
            .nav-link {
                padding: var(--space-sm) var(--space-md);
            }

            /* Adjust typography for mobile */
            h1 {
                font-size: 2rem;
            }
            h2 {
                font-size: 1.5rem;
            }
            h3 {
                font-size: 1.3rem;
            }

            /* Adjust table display for mobile */
            table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }
        }

        /* Minimalist Export Functionality */
        .export-container {
            position: fixed;
            top: var(--space-md);
            right: var(--space-md);
            z-index: 990;
            display: flex;
            gap: var(--space-sm);
        }

        .export-button {
            background-color: var(--surface-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--space-sm) var(--space-md);
            cursor: pointer;
            transition: all var(--transition-normal);
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-sm);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .export-button:hover {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .export-button::before {
            font-size: var(--font-size-md);
            opacity: 0.8;
        }

        .export-pdf::before {
            content: '📄';
        }

        .export-codebook::before {
            content: '📚';
        }

        /* Minimalist Export Modal */
        .export-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(2px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: opacity var(--transition-normal), visibility var(--transition-normal), backdrop-filter var(--transition-normal);
        }

        .export-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .export-modal-content {
            background-color: var(--background-color);
            border-radius: var(--radius-md);
            padding: var(--space-lg);
            width: 90%;
            max-width: 450px;
            box-shadow: var(--shadow-md);
            transform: translateY(10px);
            opacity: 0;
            transition: transform var(--transition-normal), opacity var(--transition-normal);
        }

        .export-modal.active .export-modal-content {
            transform: translateY(0);
            opacity: 1;
        }

        .export-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
            padding-bottom: var(--space-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .export-modal-title {
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-lg);
            color: var(--text-color);
        }

        .export-modal-close {
            background: none;
            border: none;
            font-size: var(--font-size-xl);
            cursor: pointer;
            color: var(--text-secondary);
            transition: color var(--transition-normal);
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-sm);
        }

        .export-modal-close:hover {
            color: var(--primary-color);
            background-color: var(--surface-color);
        }

        .export-modal-body {
            margin-bottom: var(--space-lg);
        }

        .export-progress {
            height: 4px;
            background-color: var(--border-color);
            border-radius: var(--radius-sm);
            margin: var(--space-md) 0;
            overflow: hidden;
        }

        .export-progress-bar {
            height: 100%;
            background-color: var(--primary-color);
            width: 0;
            transition: width var(--transition-normal);
        }

        .export-modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: var(--space-md);
        }

        .export-modal-button {
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--radius-md);
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .export-modal-button.primary {
            background-color: var(--primary-color);
            color: white;
            border: 1px solid var(--primary-color);
        }

        .export-modal-button.secondary {
            background-color: var(--surface-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .export-modal-button:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
            color: white;
        }

        .export-modal-button.secondary:hover {
            background-color: var(--border-color);
            color: var(--text-color);
        }

        .export-modal-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Minimalist Mobile Menu Toggle */
        .menu-toggle {
            display: none;
            position: fixed;
            top: var(--space-md);
            left: var(--space-md);
            z-index: 1000;
            background-color: var(--surface-color);
            color: var(--primary-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .menu-toggle:hover {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Subtle Mobile overlay */
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(2px);
            z-index: 90;
            opacity: 0;
            transition: opacity var(--transition-normal), backdrop-filter var(--transition-normal);
        }

        .overlay.active {
            opacity: 1;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1024px) {
            .sidebar {
                width: 240px;
            }

            .main-content {
                margin-left: 240px;
                max-width: calc(100% - 240px);
            }
        }

        @media (max-width: 768px) {
            .menu-toggle {
                display: flex;
            }

            .menu-toggle::before {
                content: '☰';
                font-size: 1.1rem;
            }

            .sidebar {
                width: 280px;
                transform: translateX(-100%);
                transition: transform var(--transition-normal);
                z-index: 999;
                box-shadow: none;
            }

            .sidebar.active {
                transform: translateX(0);
                box-shadow: var(--shadow-lg);
            }

            .main-content {
                margin-left: 0;
                max-width: 100%;
                padding: var(--space-lg);
                padding-top: 70px;
            }

            .overlay.active {
                display: block;
            }

            .export-container {
                top: var(--space-md);
                right: var(--space-md);
                flex-direction: column;
                gap: var(--space-xs);
            }

            .export-button {
                font-size: var(--font-size-xs);
                padding: var(--space-xs) var(--space-sm);
                background-color: var(--surface-color);
                color: var(--primary-color);
                border: 1px solid var(--border-color);
                background-image: none;
            }

            .export-button:hover {
                background-color: var(--primary-color);
                color: white;
                border-color: var(--primary-color);
            }

            .export-modal-content {
                width: 95%;
                padding: var(--space-md);
            }

            /* Typography adjustments for mobile */
            h1 {
                font-size: var(--font-size-2xl);
                margin-bottom: var(--space-lg);
            }

            h2 {
                font-size: var(--font-size-xl);
            }

            h3 {
                font-size: var(--font-size-lg);
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: var(--space-md);
                padding-top: 70px;
            }

            /* Simplify code blocks on mobile */
            .code-container {
                margin: var(--space-md) 0;
            }

            .code-actions {
                position: static;
                margin-top: var(--space-sm);
                opacity: 1;
                transform: none;
                justify-content: flex-end;
            }

            /* Adjust callouts for mobile */
            .note, .warning, .tip, .tutorial {
                padding: var(--space-sm) var(--space-md);
            }
        }
    </style>
</head>
<body>
    <button class="menu-toggle" id="menuToggle">☰</button>
    <div class="overlay" id="overlay"></div>

    <div class="export-container">
        <button class="export-button export-pdf" id="exportPdf">Export PDF</button>
        <button class="export-button export-codebook" id="exportCodebook">Generate Codebook</button>
    </div>

    <!-- Export Modal -->
    <div class="export-modal" id="exportModal">
        <div class="export-modal-content">
            <div class="export-modal-header">
                <h3 class="export-modal-title" id="exportModalTitle">Exporting Documentation</h3>
                <button class="export-modal-close" id="exportModalClose">&times;</button>
            </div>
            <div class="export-modal-body">
                <p id="exportModalMessage">Preparing document for export...</p>
                <div class="export-progress">
                    <div class="export-progress-bar" id="exportProgressBar"></div>
                </div>
            </div>
            <div class="export-modal-footer">
                <button class="export-modal-button secondary" id="exportModalCancel">Cancel</button>
                <button class="export-modal-button primary" id="exportModalDownload" disabled>Download</button>
            </div>
        </div>
    </div>

    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2>SOCIAL.ent.apc</h2>
            <div class="theme-toggle" id="themeToggle">
                <span class="theme-toggle-icon">🌓</span>
                <span>Theme</span>
            </div>
        </div>

        <div class="search-container">
            <input type="text" id="searchBox" class="search-box" placeholder="Search documentation..." />
        </div>

        <nav>
            <ul class="nav-list" id="navList">
                <li class="nav-item"><a href="#overview" class="nav-link">1. Overview</a></li>
                <li class="nav-item">
                    <a href="#global-variables-functions" class="nav-link">2. Global Variables and Functions</a>
                    <ul class="nav-list">
                        <li class="nav-subitem"><a href="#global-variables" class="nav-link">2.1 Global Variables</a></li>
                        <li class="nav-subitem"><a href="#functions" class="nav-link">2.2 Functions</a></li>
                        <li class="nav-subitem"><a href="#map-gps-functions" class="nav-link">2.2.1 Map and GPS Functions</a></li>
                        <li class="nav-subitem"><a href="#utility-functions" class="nav-link">2.2.2 Utility Functions</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a href="#main-procedures" class="nav-link">3. Main Procedures</a>
                    <ul class="nav-list">
                        <li class="nav-subitem"><a href="#identification-section" class="nav-link">3.1 Identification Section</a></li>
                        <li class="nav-subitem"><a href="#household-roster-section" class="nav-link">3.2 Household Roster Section</a></li>
                        <li class="nav-subitem"><a href="#education-section" class="nav-link">3.3 Education Section</a></li>
                        <li class="nav-subitem"><a href="#health-section" class="nav-link">3.4 Health Section</a></li>
                        <li class="nav-subitem"><a href="#housing-section" class="nav-link">3.5 Housing Section</a></li>
                        <li class="nav-subitem"><a href="#economic-activities-section" class="nav-link">3.6 Economic Activities Section</a></li>
                        <li class="nav-subitem"><a href="#income-expenditure-sections" class="nav-link">3.7 Income and Expenditure Sections</a></li>
                        <li class="nav-subitem"><a href="#agricultural-activities-section" class="nav-link">3.8 Agricultural Activities Section</a></li>
                    </ul>
                </li>
                <li class="nav-item"><a href="#integration" class="nav-link">4. Integration with Other Components</a></li>
                <li class="nav-item"><a href="#key-features" class="nav-link">5. Key Features</a></li>
                <li class="nav-item"><a href="#usage-examples" class="nav-link">6. Usage Examples</a></li>
                <li class="nav-item"><a href="#limitations" class="nav-link">7. Limitations and Considerations</a></li>
                <li class="nav-item"><a href="#conclusion" class="nav-link">8. Conclusion</a></li>
            </ul>
        </nav>
    </aside>

    <main class="main-content">
        <h1 id="overview">SOCIAL.ent.apc Documentation</h1>

        <section>
            <h2>1. Overview</h2>
            <p>The <code>SOCIAL.ent.apc</code> file is a CSPro (Census and Survey Processing System) application logic file that serves as the core programming component for the SOCIAL data entry application. This application is part of a larger social registry system designed for collecting household survey data in Algeria, specifically for the "Enquête Nationale sur la Consommation" (National Consumption Survey).</p>

            <p>The application handles various aspects of data collection including:</p>
            <ul>
                <li>Household member information</li>
                <li>Education data</li>
                <li>Health information</li>
                <li>Housing conditions</li>
                <li>Economic activities</li>
                <li>Asset ownership</li>
                <li>Income and expenditure data</li>
                <li>Agricultural activities</li>
                <li>Social assistance and transfers</li>
            </ul>

            <p>The file contains logic procedures that control data validation, skip patterns, and dynamic questionnaire flow based on respondent answers.</p>
        </section>

        <section id="global-variables-functions">
            <h2>2. Global Variables and Functions</h2>
            <p>The SOCIAL.ent.apc file defines several global variables and functions that are used throughout the application to manage data collection, validation, and user interface interactions.</p>

        <h3 id="global-variables">2.1 Global Variables</h3>
        <pre><code>numeric tot, OK, i, err, id_marker, initialLat, initialLon, j, n, p, x, tim, maxmemb;
array codes(30);
alpha(900) strnotes;
array alpha(40) labels(30);
numeric e;
map mymap;
valueset MyValueset;</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>The global variables defined at the beginning of the file serve various purposes throughout the application:</p>
            <ul>
                <li>The numeric variables (<code>i</code>, <code>j</code>, etc.) are used as loop counters, flags, and temporary storage.</li>
                <li>The <code>codes</code> and <code>labels</code> arrays work together to create dynamic dropdown lists for respondent selection.</li>
                <li>The <code>mymap</code> object provides GPS functionality for capturing household locations.</li>
            </ul>
            <p>These variables are accessible from any procedure in the file, allowing for data sharing across different sections of the questionnaire.</p>
        </div>

        <table>
            <tr>
                <th>Variable</th>
                <th>Type</th>
                <th>Purpose</th>
                <th>Usage Example</th>
            </tr>
            <tr>
                <td><code>tot</code></td>
                <td>numeric</td>
                <td>Used for totals and summations</td>
                <td>Calculating total household members, summing values</td>
            </tr>
            <tr>
                <td><code>OK</code></td>
                <td>numeric</td>
                <td>Flag for operation success/user confirmation</td>
                <td>Storing user responses to confirmation dialogs</td>
            </tr>
            <tr>
                <td><code>i</code>, <code>j</code>, <code>n</code>, <code>p</code>, <code>x</code></td>
                <td>numeric</td>
                <td>Loop counters and array indices</td>
                <td>Iterating through household members, array elements</td>
            </tr>
            <tr>
                <td><code>err</code></td>
                <td>numeric</td>
                <td>Error code storage</td>
                <td>Tracking validation errors during data entry</td>
            </tr>
            <tr>
                <td><code>id_marker</code></td>
                <td>numeric</td>
                <td>Marker ID for GPS mapping</td>
                <td>Identifying markers on the map for household locations</td>
            </tr>
            <tr>
                <td><code>initialLat</code>, <code>initialLon</code></td>
                <td>numeric</td>
                <td>Initial GPS coordinates</td>
                <td>Storing the starting point for GPS validation</td>
            </tr>
            <tr>
                <td><code>tim</code></td>
                <td>numeric</td>
                <td>Time-related calculations</td>
                <td>Tracking interview duration or timestamps</td>
            </tr>
            <tr>
                <td><code>maxmemb</code></td>
                <td>numeric</td>
                <td>Maximum number of household members</td>
                <td>Setting array sizes and loop boundaries</td>
            </tr>
            <tr>
                <td><code>codes(30)</code></td>
                <td>array</td>
                <td>Stores numeric codes for value sets</td>
                <td>Creating dynamic dropdown options with numeric values</td>
            </tr>
            <tr>
                <td><code>strnotes</code></td>
                <td>alpha(900)</td>
                <td>Stores notes/comments from interviewers</td>
                <td>Capturing additional context or observations</td>
            </tr>
            <tr>
                <td><code>labels(30)</code></td>
                <td>array alpha(40)</td>
                <td>Stores text labels for value sets</td>
                <td>Creating human-readable labels for dropdown options</td>
            </tr>
            <tr>
                <td><code>e</code></td>
                <td>numeric</td>
                <td>General-purpose variable</td>
                <td>Temporary calculations and flags</td>
            </tr>
            <tr>
                <td><code>mymap</code></td>
                <td>map</td>
                <td>Map object for GPS functionality</td>
                <td>Displaying and interacting with maps for location data</td>
            </tr>
            <tr>
                <td><code>MyValueset</code></td>
                <td>valueset</td>
                <td>Dynamic value set for dropdown selections</td>
                <td>Creating custom dropdown menus based on previous responses</td>
            </tr>
        </table>

        <div class="note">
            <p><strong>Note:</strong> The use of global variables in CSPro applications is common for sharing data across procedures, but it can lead to maintenance challenges in large applications. Modern programming practices would favor more structured approaches with better encapsulation.</p>
        </div>

        <h3 id="functions">2.2 Functions</h3>
        <p>The SOCIAL.ent.apc file defines several functions that encapsulate common operations and provide reusable functionality across the application.</p>

        <h4 id="map-gps-functions">2.2.1 Map and GPS Functions</h4>
        <pre><code>function closeMap(map m)
    m.hide();
end;

function retakeGPS(map m)
    m.hide();
    A10gps = 2;
    reenter A10GPS;
end;

function mapClicked()
    numeric lat = mymap.getLastClickLatitude();
    numeric lon = mymap.getLastClickLongitude();
    numeric d = gps(distance,lat,lon,initialLat,initialLon);

    if d &lt;= 30 then // we move the point only if he is not too far from the initial position
        mymap.setMarkerLocation(id_marker, lat, lon);
        A10A = lon;
        A10B = lat;
    else
        errmsg("Vous êtes trop loin de la position initiale (%d mètres), pour éviter toute erreur nous ne nous y déplacerons pas",d);
    endif;
end;

function adjustPoint(map m);
    m.setOnClick(mapClicked());
end;</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>These functions work together to provide GPS mapping functionality for capturing household locations:</p>

            <h5>closeMap(map m)</h5>
            <ul>
                <li><strong>Purpose:</strong> Hides the map interface when it's no longer needed</li>
                <li><strong>Parameters:</strong> Takes a map object as input</li>
                <li><strong>Implementation:</strong> Simply calls the hide() method on the map object</li>
                <li><strong>Usage:</strong> Called when the user completes GPS capture or cancels the operation</li>
            </ul>

            <h5>retakeGPS(map m)</h5>
            <ul>
                <li><strong>Purpose:</strong> Allows the user to recapture GPS coordinates</li>
                <li><strong>Parameters:</strong> Takes a map object as input</li>
                <li><strong>Implementation:</strong> Hides the current map, sets A10gps to 2 (indicating retake), and reenters the GPS field</li>
                <li><strong>Usage:</strong> Called when the user wants to discard current coordinates and capture new ones</li>
            </ul>

            <h5>mapClicked()</h5>
            <ul>
                <li><strong>Purpose:</strong> Handles map click events and validates coordinate selection</li>
                <li><strong>Implementation:</strong>
                    <ol>
                        <li>Gets the latitude and longitude of the clicked location</li>
                        <li>Calculates the distance from the initial position</li>
                        <li>If within 30 meters, updates the marker location and stores coordinates</li>
                        <li>If too far, displays an error message</li>
                    </ol>
                </li>
                <li><strong>Validation:</strong> Ensures that selected locations are within 30 meters of the initial position to prevent errors</li>
                <li><strong>Usage:</strong> Called automatically when the user clicks on the map</li>
            </ul>

            <h5>adjustPoint(map m)</h5>
            <ul>
                <li><strong>Purpose:</strong> Sets up the click handler for the map</li>
                <li><strong>Parameters:</strong> Takes a map object as input</li>
                <li><strong>Implementation:</strong> Registers the mapClicked() function as the click event handler</li>
                <li><strong>Usage:</strong> Called when initializing the map to enable user interaction</li>
            </ul>
        </div>

        <div class="tip">
            <p><strong>Improvement Suggestion:</strong> The current implementation uses a fixed 30-meter threshold for validation. A more flexible approach would be to make this threshold configurable based on the survey context (urban vs. rural areas might need different thresholds).</p>
        </div>

        <p>These functions handle GPS mapping functionality:</p>
        <ul>
            <li><code>closeMap()</code>: Hides the map interface</li>
            <li><code>retakeGPS()</code>: Allows re-capturing GPS coordinates</li>
            <li><code>mapClicked()</code>: Handles map click events, captures coordinates, and validates the distance from initial position</li>
            <li><code>adjustPoint()</code>: Sets up the click handler for the map</li>
        </ul>

        <h4 id="utility-functions">2.2.2 Utility Functions</h4>
        <pre><code>function clean_labels();
  numeric z;
  do z = 1 while z &lt;= 30
    codes(z)  = notappl;
    labels(z) = "";
  enddo;
end;

function endmess();
  { Returns true if response is REVIEW }
  endmess = ( demode() = add &amp;
             accept("FIN DE L'ENQUETE MENAGE",
                    "REVISITER LE QUESTIONNAIRE",
                    "FINALISER") &lt;&gt; 2);
end;

function TestValidityName(string name)
    if strip(name) = "" then
        errmsg("Le nom/texte ne peut pas être vide");
        reenter; //not allow to continue without fill a name
    elseif length(strip(name)) &lt; 2 then
        errmsg("Le nom/texte %s est trop court",strip(name));
        reenter;
    elseif pos(name[1:1], " 0123456789@,;:!?/#-+*.=()\_{}°[]")&gt;0 then
        errmsg("Le nom/texte ne peut pas commencer par %s",name[1:1]);
        reenter;
    endif;
end;

function Onstop()
    ok = accept(TR("Que veux-tu ?"), tr("Sauvegarde"),tr("Annuler"),tr("Quitter sans sauvegarder"));

    if ok in 3 then
        stop(1);
    elseif ok = 1 then
        savepartial();
        stop(1);
    elseif ok in 0, 2 then
        reenter;
    endif;
end;</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>These utility functions provide common functionality used throughout the application:</p>

            <h5>clean_labels()</h5>
            <ul>
                <li><strong>Purpose:</strong> Resets the codes and labels arrays used for dynamic dropdown lists</li>
                <li><strong>Implementation:</strong> Iterates through the arrays and sets each element to empty/not applicable</li>
                <li><strong>Usage:</strong> Called before populating dropdown lists to ensure clean state</li>
                <li><strong>Line-by-line explanation:</strong>
                    <ol>
                        <li>Declares a local numeric variable z for iteration</li>
                        <li>Loops through indices 1 to 30</li>
                        <li>Sets each code to notappl (not applicable)</li>
                        <li>Sets each label to an empty string</li>
                    </ol>
                </li>
            </ul>

            <h5>endmess()</h5>
            <ul>
                <li><strong>Purpose:</strong> Handles end-of-survey messaging and navigation</li>
                <li><strong>Return value:</strong> Boolean indicating whether to review the questionnaire</li>
                <li><strong>Implementation:</strong> Checks if in add mode and presents options to the user</li>
                <li><strong>Line-by-line explanation:</strong>
                    <ol>
                        <li>Comment explains the function returns true if response is REVIEW</li>
                        <li>Checks if in add mode and if the user selected an option other than "FINALISER" (option 2)</li>
                        <li>Returns true if the user wants to review, false otherwise</li>
                    </ol>
                </li>
            </ul>

            <h5>TestValidityName(string name)</h5>
            <ul>
                <li><strong>Purpose:</strong> Validates name entries with specific rules</li>
                <li><strong>Parameters:</strong> Takes a string input to validate</li>
                <li><strong>Implementation:</strong> Performs multiple validation checks with appropriate error messages</li>
                <li><strong>Validation rules:</strong>
                    <ol>
                        <li>Name cannot be empty</li>
                        <li>Name must be at least 2 characters long</li>
                        <li>Name cannot start with a number, special character, or space</li>
                    </ol>
                </li>
                <li><strong>Error handling:</strong> Displays specific error messages and forces reentry if validation fails</li>
            </ul>

            <h5>Onstop()</h5>
            <ul>
                <li><strong>Purpose:</strong> Handles application exit with options to save or discard data</li>
                <li><strong>Implementation:</strong> Presents a dialog with three options and handles each response</li>
                <li><strong>Options:</strong>
                    <ol>
                        <li>Save: Saves partial data and exits</li>
                        <li>Cancel: Returns to the application</li>
                        <li>Exit without saving: Exits without saving changes</li>
                    </ol>
                </li>
                <li><strong>Usage:</strong> Called when the user attempts to exit the application</li>
            </ul>
        </div>

        <div class="warning">
            <p><strong>Limitation:</strong> The name validation in <code>TestValidityName()</code> uses a hardcoded list of invalid starting characters, which may need to be updated if requirements change. A more maintainable approach would be to use a configurable pattern or regular expression.</p>
        </div>

        <p>These utility functions provide common functionality:</p>
        <ul>
            <li><code>clean_labels()</code>: Resets the codes and labels arrays</li>
            <li><code>endmess()</code>: Handles end-of-survey messaging and navigation</li>
            <li><code>TestValidityName()</code>: Validates name entries with specific rules</li>
            <li><code>Onstop()</code>: Handles application exit with options to save or discard data</li>
        </ul>
        </section>

        <section id="main-procedures">
        <h2>3. Main Procedures</h2>
        <p>The file contains numerous procedures that correspond to different sections of the questionnaire. Each procedure typically handles validation, skip patterns, and data entry for specific fields.</p>

        <h3 id="identification-section">3.1 Identification Section (SECTA_FORM)</h3>
        <p>This section handles the basic identification information for the household:</p>

        <pre><code>PROC SOCIAL_FF
PROC SOCIAL_LEVEL
stop(1);

PROC SECTA_FORM
preproc
    if ispartial() then
        ok = accept("C'est un questionnaire partiel, voulez-vous aller à la dernière position ?","Oui","Non");
        if ok = 1 then
            advance to getsymbol(savepartial);
        endif;
    endif;</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>The identification section initializes the questionnaire and handles partial saves:</p>
            <ul>
                <li><strong>PROC SOCIAL_FF:</strong> Entry point for the application</li>
                <li><strong>PROC SOCIAL_LEVEL:</strong> Defines the top level of the questionnaire hierarchy</li>
                <li><strong>PROC SECTA_FORM:</strong> Handles the identification panel with preproc logic that:
                    <ol>
                        <li>Checks if this is a partial questionnaire (previously saved)</li>
                        <li>If partial, asks the user if they want to resume from where they left off</li>
                        <li>If confirmed, advances to the last saved position</li>
                    </ol>
                </li>
            </ul>
        </div>

        <p>Key procedures include:</p>
        <ul>
            <li><code>A01-A16</code>: Household identification, date, interviewer ID, location codes</li>
            <li><code>START_TIME</code>: Captures interview start time</li>
        </ul>

        <h3 id="household-roster-section">3.2 Household Roster Section (QHSEC01X_FORM)</h3>
        <p>This section captures information about each household member:</p>

        <pre><code>PROC QHLINE
preproc
  $ = curocc();

PROC QHFIRSTN
  { Check that response is alphabetic and starts in the first column }
  TestValidityName(QHFIRSTN);

PROC QHLASTN
  TestValidityName(QHLASTN);

PROC QHRELAT
 if curocc() = 1 &lt;=&gt;  $ &lt;&gt; 1 then
    errmsg( 0031 );
    reenter
     elseif $ = 2 &amp; curocc() &lt;&gt; 2 then
    errmsg( 0033 );
    reenter
  endif;</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>The household roster section collects information about each household member:</p>
            <ul>
                <li><strong>PROC QHLINE:</strong> Sets the current occurrence number (line number) for each household member</li>
                <li><strong>PROC QHFIRSTN/QHLASTN:</strong> Validates first and last names using the TestValidityName function</li>
                <li><strong>PROC QHRELAT:</strong> Enforces rules about household relationships:
                    <ol>
                        <li>The first person (line 1) must be the household head (code 1)</li>
                        <li>The spouse (code 2) must be in the second position</li>
                    </ol>
                </li>
            </ul>
            <p>This section forms the foundation for the rest of the questionnaire, as many other sections reference household members.</p>
        </div>

        <p>Key fields include:</p>
        <ul>
            <li><code>QHFIRSTN</code>, <code>QHLASTN</code>: First and last names</li>
            <li><code>QHRELAT</code>: Relationship to household head</li>
            <li><code>QHAGE</code>: Age with validation</li>
            <li><code>QHSEX</code>: Sex with validation for spouse</li>
            <li><code>QHSM</code>: Marital status</li>
        </ul>

        <h3 id="education-section">3.3 Education Section</h3>
        <p>Captures educational information for household members:</p>

        <pre><code>PROC ED00A
preproc
$=curocc();
 if QHAGE(curocc()) &lt; 3 then
     skip to next ED00A
 endif;
  if curocc() &gt; QHMEMBER then
    endsect
  endif;</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>The education section collects information about schooling and educational attainment:</p>
            <ul>
                <li><strong>Age-based filtering:</strong> Skips children under 3 years old</li>
                <li><strong>Boundary checking:</strong> Exits the section when all household members have been processed</li>
                <li><strong>Skip patterns:</strong> Various skip patterns based on education level and attendance status</li>
            </ul>
            <p>This section demonstrates how CSPro handles repeating groups of questions for each household member with appropriate filtering.</p>
        </div>

        <h3 id="health-section">3.4 Health Section (SA00)</h3>
        <p>Collects health-related information:</p>

        <pre><code>PROC SA00
preproc
$=curocc();
  if curocc() &gt; QHMEMBER then
    endsect
  endif;</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>The health section captures information about health status, conditions, and healthcare access:</p>
            <ul>
                <li><strong>Iterative processing:</strong> Processes each household member sequentially</li>
                <li><strong>Boundary checking:</strong> Exits the section when all household members have been processed</li>
                <li><strong>Conditional questions:</strong> Many health questions are only asked based on previous responses</li>
            </ul>
        </div>

        <h3 id="housing-section">3.5 Housing Section (H00-H39)</h3>
        <p>Collects information about housing conditions:</p>

        <pre><code>PROC H01
if $ = 9 then editnote() endif;</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>The housing section collects information about dwelling characteristics and living conditions:</p>
            <ul>
                <li><strong>Note handling:</strong> Allows interviewers to add notes for special cases (code 9)</li>
                <li><strong>Skip patterns:</strong> Complex skip patterns based on housing type and ownership</li>
                <li><strong>Validation:</strong> Ensures logical consistency between related housing questions</li>
            </ul>
        </div>

        <h3 id="economic-activities-section">3.6 Economic Activities Section (ACTA0-ACTE_18)</h3>
        <p>Captures information about economic activities of household members:</p>

        <pre><code>PROC ACTA0
preproc
  { Initialize household members' questions with information already collected or known }
 $ = curocc();

  if curocc() &gt; QHMEMBER then
    endsect
  endif;</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>The economic activities section captures information about employment, income sources, and economic status:</p>
            <ul>
                <li><strong>Age filtering:</strong> Many procedures check age to determine eligibility for economic questions</li>
                <li><strong>Complex routing:</strong> Multiple subsections with intricate skip patterns based on employment status</li>
                <li><strong>Iterative processing:</strong> Processes each eligible household member</li>
            </ul>
            <p>This section is one of the most complex in the questionnaire, with multiple nested conditions and skip patterns.</p>
        </div>

        <h3 id="income-expenditure-sections">3.7 Income and Expenditure Sections</h3>
        <p>Multiple sections capture income and expenditure data:</p>
        <ul>
            <li>Non-agricultural enterprises (RNA00-RNH10C)</li>
            <li>Food expenditure (ALI01-ALIMA006)</li>
            <li>Transfers and remittances (TR12-TRAUU)</li>
        </ul>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>These sections collect detailed information about household income sources and expenditure patterns:</p>
            <ul>
                <li><strong>Enterprise data:</strong> Captures information about non-agricultural businesses owned by household members</li>
                <li><strong>Expenditure categories:</strong> Detailed breakdown of household spending across multiple categories</li>
                <li><strong>Transfers:</strong> Records money and goods received from or sent to other households</li>
            </ul>
            <p>These sections are critical for calculating household welfare indicators and poverty measures.</p>
        </div>

        <h3 id="agricultural-activities-section">3.8 Agricultural Activities Section</h3>
        <p>Captures information about agricultural activities:</p>
        <ul>
            <li>Crop production (AGRC02B-AGRC02B1Y)</li>
            <li>Livestock (AGRC02C-AGRC02B1U)</li>
            <li>Agricultural equipment (AGRCF2-AGRCF2_3)</li>
        </ul>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>The agricultural sections collect information about farming activities, livestock, and agricultural assets:</p>
            <ul>
                <li><strong>Crop inventory:</strong> Detailed information about crops grown, production, and sales</li>
                <li><strong>Livestock:</strong> Information about animal ownership, production, and sales</li>
                <li><strong>Equipment:</strong> Inventory of agricultural tools and machinery</li>
            </ul>
            <p>These sections are particularly important in rural areas where agriculture is a major source of livelihood.</p>
        </div>

        <div class="note">
            <p><strong>Implementation Note:</strong> The main procedures demonstrate CSPro's approach to questionnaire flow control through procedural code rather than declarative form definitions. This provides flexibility but requires careful maintenance to ensure logical consistency.</p>
        </div>
        </section>

        <section id="integration">
        <h2>4. Integration with Other Components</h2>
        <p>The SOCIAL.ent.apc file is part of a larger system that includes:</p>

        <div class="code-explanation">
            <h4>System Architecture</h4>
            <p>The SOCIAL.ent.apc file integrates with several other components to form a complete data collection system:</p>
        </div>

        <ol>
            <li><strong>Dictionary Files</strong>:
                <ul>
                    <li><strong>SOCIAL.dcf:</strong> Defines the data structure and variables, including field types, lengths, and value sets</li>
                    <li>This dictionary file serves as the schema for the collected data and determines the structure of the database</li>
                </ul>
            </li>
            <li><strong>Form Files</strong>:
                <ul>
                    <li><strong>SOCIAL.fmf:</strong> Defines the user interface layout, including form positioning, field labels, and visual elements</li>
                    <li>The form file controls the visual presentation of the questionnaire to interviewers</li>
                </ul>
            </li>
            <li><strong>Menu System</strong>:
                <ul>
                    <li><strong>MENU.ent.apc:</strong> Provides navigation and application control, including user authentication and case management</li>
                    <li>The menu system serves as the entry point for interviewers and manages workflow between different components</li>
                </ul>
            </li>
            <li><strong>Sample Management</strong>:
                <ul>
                    <li><strong>SAMPLE.ent.apc:</strong> Handles sample selection and assignment, including household allocation to interviewers</li>
                    <li>This component ensures proper coverage of the survey population and tracks completion status</li>
                </ul>
            </li>
        </ol>

        <div class="note">
            <p><strong>Integration Flow:</strong> The typical workflow begins with the MENU system, which authenticates users and presents available tasks. When a household interview is selected, the SOCIAL application is launched with parameters from the SAMPLE management system. During data collection, the SOCIAL.ent.apc logic controls the flow while referencing the dictionary (SOCIAL.dcf) for data validation and the form file (SOCIAL.fmf) for presentation.</p>
        </div>

        <section id="key-features">
        <h2>5. Key Features</h2>

        <div class="code-explanation">
            <h4>Core Functionality</h4>
            <p>The SOCIAL.ent.apc file implements several key features that enhance data collection quality and efficiency:</p>
        </div>

        <ol>
            <li>
                <strong>Dynamic Value Sets</strong>:
                <p>Many procedures use the <code>clean_labels()</code> function to create dynamic dropdown lists based on household members. This ensures that only relevant options are presented to interviewers.</p>
                <p><strong>Example:</strong> When selecting a household respondent, only members aged 12 and above are included in the dropdown list.</p>
            </li>
            <li>
                <strong>GPS Integration</strong>:
                <p>The application includes GPS functionality for capturing household locations with validation to ensure accuracy.</p>
                <p><strong>Implementation:</strong> Uses the CSPro map object with custom functions for location validation and adjustment.</p>
            </li>
            <li>
                <strong>Skip Patterns</strong>:
                <p>Extensive use of conditional logic to control questionnaire flow, reducing interview time and preventing irrelevant questions.</p>
                <p><strong>Example:</strong> Education questions are skipped for children under 3 years old.</p>
            </li>
            <li>
                <strong>Data Validation</strong>:
                <p>Comprehensive validation rules ensure data quality through range checks, consistency validations, and logical constraints.</p>
                <p><strong>Implementation:</strong> Combines immediate field-level validation with cross-field consistency checks.</p>
            </li>
            <li>
                <strong>Partial Save</strong>:
                <p>Supports saving partial interviews and resuming later, essential for lengthy questionnaires.</p>
                <p><strong>Implementation:</strong> Uses CSPro's <code>savepartial()</code> function at strategic points in the questionnaire.</p>
            </li>
        </ol>

        <div class="tip">
            <p><strong>Modern Alternative:</strong> While CSPro's procedural approach provides flexibility, modern survey platforms like ODK or Survey Solutions offer more declarative approaches to questionnaire design that can be easier to maintain and validate.</p>
        </div>
        </section>

        <section id="usage-examples">
        <h2>6. Usage Examples</h2>

        <h3>Example 1: Household Member Selection</h3>
        <pre><code>PROC QHRESP
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i &lt;= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>This code creates a dynamic dropdown list of eligible household members for respondent selection:</p>
            <ol>
                <li><strong>Event Trigger:</strong> The <code>onfocus</code> event fires when the field receives focus</li>
                <li><strong>Initialization:</strong> <code>clean_labels()</code> resets the arrays used for the value set</li>
                <li><strong>Iteration:</strong> Loops through all household members (1 to QHMEMBER)</li>
                <li><strong>Filtering:</strong> Only includes members aged 12-98 years</li>
                <li><strong>Value Set Creation:</strong> Builds parallel arrays of codes (member line numbers) and labels (names)</li>
                <li><strong>Dynamic Assignment:</strong> <code>SetValueSet()</code> creates a dropdown with these values</li>
            </ol>
            <p><strong>Practical Use:</strong> This technique is used throughout the questionnaire whenever a household member needs to be selected, such as for the main respondent, household head, or subject of specific questions.</p>
        </div>

        <div class="tip">
            <p><strong>Improvement Suggestion:</strong> This pattern is repeated in multiple places in the code. A more maintainable approach would be to create a reusable function that takes age parameters and returns a configured value set.</p>
        </div>

        <h3>Example 2: GPS Capture</h3>
        <pre><code>function mapClicked()
    numeric lat = mymap.getLastClickLatitude();
    numeric lon = mymap.getLastClickLongitude();
    numeric d = gps(distance,lat,lon,initialLat,initialLon);

    if d &lt;= 30 then // we move the point only if he is not too far from the initial position
        mymap.setMarkerLocation(id_marker, lat, lon);
        A10A = lon;
        A10B = lat;
    else
        errmsg("Vous êtes trop loin de la position initiale (%d mètres), pour éviter toute erreur nous ne nous y déplacerons pas",d);
    endif;
end;</code></pre>

        <div class="code-explanation">
            <h4>Detailed Explanation</h4>
            <p>This function handles map click events for GPS coordinate capture with validation:</p>
            <ol>
                <li><strong>Coordinate Retrieval:</strong> Gets latitude and longitude from the click position</li>
                <li><strong>Distance Calculation:</strong> Uses the GPS function to calculate distance from initial position</li>
                <li><strong>Validation:</strong> Ensures the selected location is within 30 meters of the initial position</li>
                <li><strong>Data Storage:</strong> If valid, updates the marker location and stores coordinates in A10A/A10B</li>
                <li><strong>Error Handling:</strong> If invalid, displays an error message with the calculated distance</li>
            </ol>
            <p><strong>Practical Use:</strong> This function ensures accurate GPS data collection by preventing accidental selection of distant locations, which could result from map navigation errors or misclicks.</p>
        </div>

        <div class="warning">
            <p><strong>Limitation:</strong> The 30-meter threshold is hardcoded and may be too restrictive in rural areas with sparse housing or too permissive in dense urban areas. A context-sensitive approach would be more appropriate.</p>
        </div>
        </section>

        <section id="limitations">
        <h2>7. Limitations and Considerations</h2>

        <div class="code-explanation">
            <h4>Implementation Challenges</h4>
            <p>While the SOCIAL.ent.apc file effectively implements the survey requirements, there are several limitations and considerations to be aware of:</p>
        </div>

        <ol>
            <li>
                <strong>Language</strong>:
                <p>The application uses French for user interface messages, which may limit usability for non-French speakers.</p>
                <p><strong>Impact:</strong> Requires French-speaking interviewers and complicates international collaboration.</p>
                <p><strong>Improvement:</strong> Implement a language resource file system for multilingual support.</p>
            </li>
            <li>
                <strong>Dependencies</strong>:
                <p>The application depends on external files and systems, creating potential points of failure.</p>
                <p><strong>Critical Dependencies:</strong> Dictionary files, form files, and the menu system.</p>
                <p><strong>Mitigation:</strong> Implement robust error handling and version compatibility checks.</p>
            </li>
            <li>
                <strong>Performance</strong>:
                <p>Large questionnaires with many household members may experience performance issues, especially on older devices.</p>
                <p><strong>Bottlenecks:</strong> Repeated iteration through household members and complex skip patterns.</p>
                <p><strong>Optimization:</strong> Reduce redundant calculations and implement more efficient data structures.</p>
            </li>
            <li>
                <strong>Maintenance</strong>:
                <p>Changes to skip patterns or validation rules require careful testing to avoid breaking the application flow.</p>
                <p><strong>Challenge:</strong> The procedural nature of CSPro makes it difficult to trace all possible execution paths.</p>
                <p><strong>Best Practice:</strong> Implement comprehensive regression testing and document all skip patterns.</p>
            </li>
        </ol>

        <div class="note">
            <p><strong>Modern Alternatives:</strong> Contemporary survey platforms like ODK, Survey Solutions, or KoBoToolbox offer more maintainable approaches to complex surveys with declarative form definitions, built-in versioning, and better separation of concerns between data validation, UI presentation, and flow control.</p>
        </div>
        </section>

        <section id="conclusion">
        <h2>8. Conclusion</h2>
        <p>The SOCIAL.ent.apc file is a comprehensive CSPro application logic file that handles a complex household survey with multiple sections. It includes robust validation, dynamic questionnaire flow, and integration with GPS functionality. The application is designed for the Algerian National Consumption Survey and collects detailed information about households, their members, and various socioeconomic indicators.</p>

        <div class="code-explanation">
            <h4>Summary of Key Aspects</h4>
            <p>The SOCIAL.ent.apc file demonstrates several important characteristics of CSPro application development:</p>
            <ul>
                <li><strong>Comprehensive Data Collection:</strong> Covers multiple domains of household information in a structured manner</li>
                <li><strong>Advanced Validation:</strong> Implements multi-level validation to ensure data quality</li>
                <li><strong>Dynamic User Interface:</strong> Creates context-sensitive options based on previous responses</li>
                <li><strong>Integration with External Systems:</strong> Works as part of a larger ecosystem of survey tools</li>
                <li><strong>Geospatial Capabilities:</strong> Incorporates GPS functionality for location verification</li>
            </ul>
        </div>

        <div class="tip">
            <p><strong>Future Development:</strong> As survey technology evolves, this application could benefit from modernization efforts such as:</p>
            <ul>
                <li>Migrating to a more declarative questionnaire definition format</li>
                <li>Implementing better separation of concerns between data, presentation, and logic</li>
                <li>Adding multilingual support for international use</li>
                <li>Enhancing offline capabilities and synchronization mechanisms</li>
                <li>Improving performance for large household surveys</li>
            </ul>
        </div>

        <p>Despite some limitations inherent to the CSPro platform, the SOCIAL.ent.apc file represents a well-structured approach to complex survey data collection that successfully balances flexibility, validation, and user experience considerations.</p>
        </section>
        </section>
    </main>

    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('themeToggle');
        const body = document.body;

        // Check for saved theme preference or respect OS preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            body.setAttribute('data-theme', savedTheme);
        } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            body.setAttribute('data-theme', 'dark');
        }

        // Toggle theme with animation
        themeToggle.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            // Add transition class for smooth theme change
            document.documentElement.classList.add('theme-transition');

            // Set the new theme
            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Remove transition class after transition completes
            setTimeout(() => {
                document.documentElement.classList.remove('theme-transition');
            }, 300);

            // Animate the icon
            const icon = themeToggle.querySelector('.theme-toggle-icon');
            icon.textContent = newTheme === 'dark' ? '🌙' : '☀️';
            icon.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                icon.style.transform = 'rotate(0)';
            }, 300);
        });

        // Mobile Menu Toggle with overlay
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');

        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
            menuToggle.textContent = sidebar.classList.contains('active') ? '×' : '☰';
        });

        overlay.addEventListener('click', () => {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
            menuToggle.textContent = '☰';
        });

        // Section visibility and animations
        const observeSections = () => {
            // Track which sections have already been animated
            const animatedSections = new Set();

            // Set up observer for future section scrolling
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    // Only apply the effect if this is the first time seeing this section
                    if (entry.isIntersecting && !animatedSections.has(entry.target.id)) {
                        // Add to the set of animated sections
                        if (entry.target.id) {
                            animatedSections.add(entry.target.id);
                        }

                        // Add a subtle indicator for the section
                        const sectionIndicator = document.createElement('div');
                        sectionIndicator.style.position = 'absolute';
                        sectionIndicator.style.top = '0';
                        sectionIndicator.style.left = '0';
                        sectionIndicator.style.width = '4px'; // Just a thin line on the left
                        sectionIndicator.style.height = '100%';
                        sectionIndicator.style.backgroundColor = 'rgba(67, 97, 238, 0.5)';
                        sectionIndicator.style.zIndex = '1';
                        sectionIndicator.style.pointerEvents = 'none';
                        sectionIndicator.style.opacity = '0';
                        sectionIndicator.style.transition = 'opacity 0.4s ease-out';
                        sectionIndicator.style.borderRadius = '2px';

                        // Position the target element relatively if it's not already
                        const originalPosition = window.getComputedStyle(entry.target).position;
                        if (originalPosition === 'static') {
                            entry.target.style.position = 'relative';
                        }

                        entry.target.appendChild(sectionIndicator);

                        // Fade in the indicator
                        setTimeout(() => {
                            sectionIndicator.style.opacity = '1';

                            // Remove it after animation
                            setTimeout(() => {
                                sectionIndicator.style.opacity = '0';
                                setTimeout(() => {
                                    if (entry.target.contains(sectionIndicator)) {
                                        entry.target.removeChild(sectionIndicator);
                                    }
                                    // Reset position if we changed it
                                    if (originalPosition === 'static') {
                                        entry.target.style.position = '';
                                    }
                                }, 400);
                            }, 1500);
                        }, 50);

                        // Add smooth entrance for the section - no blur to prevent distortion
                        entry.target.style.opacity = '0.9';
                        entry.target.style.transform = 'scale(0.99)';

                        // Force a reflow
                        void entry.target.offsetWidth;

                        // Apply the transition - removed filter for better performance
                        entry.target.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'scale(1)';

                        // Add morphing transition effect
                        entry.target.classList.add('section-transition-active');

                        // Apply staggered animation to children
                        Array.from(entry.target.children).forEach((child, index) => {
                            // Skip if child already has inline styles or is the indicator element
                            if (child === sectionIndicator || child.style.opacity !== '' || child.style.transform !== '') {
                                return;
                            }

                            // Set initial state - reduced movement to prevent distortion
                            child.style.opacity = '0';
                            child.style.transform = 'translateY(8px)';

                            // Force a reflow
                            void child.offsetWidth;

                            // Apply staggered animation with smoother effect
                            child.style.transition = `opacity 0.5s ease-out ${0.1 + (index * 0.08)}s, transform 0.5s ease-out ${0.1 + (index * 0.08)}s`;
                            child.style.opacity = '1';
                            child.style.transform = 'translateY(0)';
                        });

                        // Remove transition classes after animation completes
                        setTimeout(() => {
                            entry.target.classList.remove('section-transition-active');

                            // Reset section styles
                            setTimeout(() => {
                                entry.target.style.transition = '';
                                entry.target.style.opacity = '';
                                entry.target.style.transform = '';

                                // Reset children styles after animations complete
                                Array.from(entry.target.children).forEach(child => {
                                    // Skip the indicator element if it's still there
                                    if (child === sectionIndicator) return;

                                    child.style.transition = '';
                                    child.style.opacity = '';
                                    child.style.transform = '';
                                });
                            }, 800);
                        }, 1000);
                    }
                });
            }, {
                threshold: 0.2,  // Increased threshold for better timing
                rootMargin: '-50px 0px'
            });

            // Observe all sections for future scrolling effects
            document.querySelectorAll('section').forEach(section => {
                // Make sure all sections are visible
                if (!section.classList.contains('visible')) {
                    section.classList.add('visible');
                }

                // Observe for future scroll effects
                observer.observe(section);
            });

            // Add a scroll event listener for additional morphing effects
            window.addEventListener('scroll', () => {
                // Find the section that's most in view
                const sections = document.querySelectorAll('section');
                const scrollPosition = window.scrollY + window.innerHeight / 3;

                let currentSection = null;

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;

                    if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                        currentSection = section;
                    }
                });

                // Update active link in navigation
                if (currentSection && currentSection.id) {
                    const currentLink = document.querySelector(`.nav-link[href="#${currentSection.id}"]`);
                    if (currentLink) {
                        navLinks.forEach(link => link.classList.remove('active'));
                        currentLink.classList.add('active');
                    }
                }
            }, { passive: true });
        };

        // Active Link Highlighting with smooth indicator
        const navLinks = document.querySelectorAll('.nav-link');

        // Set active link based on scroll position
        window.addEventListener('scroll', () => {
            let current = '';
            const sections = document.querySelectorAll('section');

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.offsetHeight;
                if (scrollY >= sectionTop - 100 && scrollY < sectionTop + sectionHeight - 100) {
                    if (section.id) {
                        current = '#' + section.id;
                    }
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === current) {
                    link.classList.add('active');
                }
            });
        });

        // Smooth scrolling for navigation links with enhanced animation
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    // Highlight the clicked link
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');

                    // Remove any existing transition classes from all sections
                    document.querySelectorAll('section').forEach(section => {
                        section.classList.remove('section-transition-active', 'section-focus');
                    });

                    // Ensure target section is visible
                    targetElement.classList.add('visible');

                    // Smooth scroll to target with a slight offset
                    window.scrollTo({
                        top: Math.max(0, targetElement.offsetTop - 30),
                        behavior: 'smooth'
                    });

                    // Subtle indicator for section change
                    const flashOverlay = document.createElement('div');
                    flashOverlay.style.position = 'fixed';
                    flashOverlay.style.top = '0';
                    flashOverlay.style.left = '0';
                    flashOverlay.style.width = '100%';
                    flashOverlay.style.height = '100%';
                    flashOverlay.style.backgroundColor = 'rgba(67, 97, 238, 0.03)';
                    flashOverlay.style.zIndex = '9999';
                    flashOverlay.style.pointerEvents = 'none';
                    flashOverlay.style.opacity = '0';
                    flashOverlay.style.transition = 'opacity 0.2s ease-out';
                    document.body.appendChild(flashOverlay);

                    // Trigger subtle flash effect
                    setTimeout(() => {
                        flashOverlay.style.opacity = '1';
                        setTimeout(() => {
                            flashOverlay.style.opacity = '0';
                            setTimeout(() => {
                                if (document.body.contains(flashOverlay)) {
                                    document.body.removeChild(flashOverlay);
                                }
                            }, 200);
                        }, 100);
                    }, 50);

                    // Wait for scroll to complete before applying morphing effect
                    setTimeout(() => {
                        // Add a smooth entrance for the section - no blur to prevent distortion
                        targetElement.style.opacity = '0.9';
                        targetElement.style.transform = 'scale(0.98)';

                        // Force a reflow
                        void targetElement.offsetWidth;

                        // Apply the transition - removed filter for better performance
                        targetElement.style.transition = 'opacity 0.5s cubic-bezier(0.22, 1, 0.36, 1), transform 0.5s cubic-bezier(0.22, 1, 0.36, 1)';
                        targetElement.style.opacity = '1';
                        targetElement.style.transform = 'scale(1)';

                        // Add morphing transition effect
                        targetElement.classList.add('section-transition-active', 'section-focus');

                        // Add a subtle highlight border that animates in
                        const highlightBorder = document.createElement('div');
                        highlightBorder.style.position = 'absolute';
                        highlightBorder.style.top = '0';
                        highlightBorder.style.left = '0';
                        highlightBorder.style.width = '100%';
                        highlightBorder.style.height = '100%';
                        highlightBorder.style.border = '1px solid rgba(67, 97, 238, 0.3)';
                        highlightBorder.style.borderRadius = '4px';
                        highlightBorder.style.pointerEvents = 'none';
                        highlightBorder.style.zIndex = '1';
                        highlightBorder.style.opacity = '0';
                        highlightBorder.style.transform = 'scale(1.05)';
                        highlightBorder.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';

                        // Position the target element relatively if it's not already
                        const originalPosition = window.getComputedStyle(targetElement).position;
                        if (originalPosition === 'static') {
                            targetElement.style.position = 'relative';
                        }

                        targetElement.appendChild(highlightBorder);

                        // Animate the highlight border
                        setTimeout(() => {
                            highlightBorder.style.opacity = '1';
                            highlightBorder.style.transform = 'scale(1)';

                            // Remove it after animation
                            setTimeout(() => {
                                highlightBorder.style.opacity = '0';
                                setTimeout(() => {
                                    if (targetElement.contains(highlightBorder)) {
                                        targetElement.removeChild(highlightBorder);
                                    }
                                    // Reset position if we changed it
                                    if (originalPosition === 'static') {
                                        targetElement.style.position = '';
                                    }
                                }, 600);
                            }, 1000);
                        }, 100);

                        // Ensure all children of the target section are visible with staggered animation
                        Array.from(targetElement.children).forEach((child, index) => {
                            // Skip the highlight border we just added
                            if (child === highlightBorder) return;

                            // Reset any existing animations
                            child.style.opacity = '0';
                            child.style.transform = 'translateY(10px)'; // Reduced movement to prevent distortion

                            // Force a reflow
                            void child.offsetWidth;

                            // Apply staggered animation with smoother effect
                            child.style.transition = `opacity 0.5s ease-out ${0.1 + (index * 0.08)}s, transform 0.5s ease-out ${0.1 + (index * 0.08)}s`;
                            child.style.opacity = '1';
                            child.style.transform = 'translateY(0)';
                        });

                        // Remove transition classes after animation completes
                        setTimeout(() => {
                            targetElement.classList.remove('section-transition-active');

                            // Reset section styles
                            setTimeout(() => {
                                targetElement.style.transition = '';
                                targetElement.style.opacity = '';
                                targetElement.style.transform = '';

                                // Reset children styles after animations complete
                                Array.from(targetElement.children).forEach(child => {
                                    // Skip the highlight border if it's still there
                                    if (child === highlightBorder) return;

                                    child.style.transition = '';
                                    child.style.opacity = '';
                                    child.style.transform = '';
                                });
                            }, 800);
                        }, 1200);
                    }, 300); // Wait for scroll to get close to target

                    // Close mobile menu after clicking a link
                    if (window.innerWidth <= 768) {
                        sidebar.classList.remove('active');
                        overlay.classList.remove('active');
                        menuToggle.textContent = '☰';
                    }
                }
            });
        });

        // Search functionality
        const searchBox = document.getElementById('searchBox');
        const navList = document.getElementById('navList');
        const allNavItems = navList.querySelectorAll('.nav-item');

        searchBox.addEventListener('input', () => {
            const searchTerm = searchBox.value.toLowerCase();

            if (searchTerm.length > 1) {
                // Search in navigation items
                allNavItems.forEach(item => {
                    const link = item.querySelector('.nav-link');
                    const text = link.textContent.toLowerCase();

                    if (text.includes(searchTerm)) {
                        item.style.display = 'block';
                        // Highlight the matching text
                        const regex = new RegExp(`(${searchTerm})`, 'gi');
                        link.innerHTML = link.textContent.replace(regex, '<mark>$1</mark>');
                    } else {
                        item.style.display = 'none';
                    }
                });
            } else {
                // Reset display and remove highlights
                allNavItems.forEach(item => {
                    item.style.display = 'block';
                    const link = item.querySelector('.nav-link');
                    link.innerHTML = link.textContent;
                });
            }
        });

        // Enhanced code blocks with actions
        const enhanceCodeBlocks = () => {
            const codeBlocks = document.querySelectorAll('pre');

            codeBlocks.forEach((block, index) => {
                // Create container
                const container = document.createElement('div');
                container.className = 'code-container';

                // Create header
                const header = document.createElement('div');
                header.className = 'code-header';

                // Create title
                const title = document.createElement('div');
                title.className = 'code-title';
                title.textContent = `Code Block ${index + 1}`;

                // Create toggle button
                const toggle = document.createElement('div');
                toggle.className = 'code-toggle';
                toggle.textContent = '▼';

                // Create content container
                const content = document.createElement('div');
                content.className = 'code-content';

                // Create actions container
                const actions = document.createElement('div');
                actions.className = 'code-actions';

                // Create copy button
                const copyBtn = document.createElement('button');
                copyBtn.className = 'code-action-button';
                copyBtn.textContent = 'Copy';
                copyBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const code = block.textContent;
                    navigator.clipboard.writeText(code).then(() => {
                        copyBtn.textContent = 'Copied!';
                        setTimeout(() => {
                            copyBtn.textContent = 'Copy';
                        }, 2000);
                    });
                });

                // Create explanation toggle if there's an explanation
                const explanationDiv = block.nextElementSibling;
                if (explanationDiv && explanationDiv.classList.contains('code-explanation')) {
                    const explainBtn = document.createElement('button');
                    explainBtn.className = 'code-action-button';
                    explainBtn.textContent = 'Explain';
                    explainBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        explanationDiv.classList.toggle('highlight');
                        if (explanationDiv.classList.contains('highlight')) {
                            explanationDiv.style.backgroundColor = 'rgba(67, 97, 238, 0.05)';
                            explanationDiv.style.transform = 'scale(1.02)';
                            explainBtn.textContent = 'Hide Explanation';
                        } else {
                            explanationDiv.style.backgroundColor = '';
                            explanationDiv.style.transform = '';
                            explainBtn.textContent = 'Explain';
                        }
                    });
                    actions.appendChild(explainBtn);
                }

                actions.appendChild(copyBtn);

                // Assemble the components
                header.appendChild(title);
                header.appendChild(toggle);

                // Replace the pre element with our structure
                block.parentNode.insertBefore(container, block);
                content.appendChild(block);
                container.appendChild(header);
                container.appendChild(actions);
                container.appendChild(content);

                // Add toggle functionality
                header.addEventListener('click', () => {
                    content.classList.toggle('collapsed');
                    toggle.classList.toggle('collapsed');
                    toggle.textContent = content.classList.contains('collapsed') ? '▶' : '▼';
                });

                // Add line highlighting functionality
                const codeLines = block.textContent.split('\n');
                if (codeLines.length > 1) {
                    // Replace the content with line-by-line spans
                    const codeContent = block.textContent;
                    block.innerHTML = '';

                    codeLines.forEach((line, i) => {
                        const lineSpan = document.createElement('span');
                        lineSpan.textContent = line + (i < codeLines.length - 1 ? '\n' : '');
                        lineSpan.className = 'code-line';
                        lineSpan.dataset.lineNumber = i + 1;

                        // Add click handler for line highlighting
                        lineSpan.addEventListener('click', (e) => {
                            e.stopPropagation();
                            lineSpan.classList.toggle('highlight-line');
                        });

                        block.appendChild(lineSpan);
                    });
                }
            });
        };

        // Create interactive tutorials
        const createTutorials = () => {
            const tutorials = document.querySelectorAll('.tutorial');

            tutorials.forEach(tutorial => {
                const steps = tutorial.querySelectorAll('.tutorial-step');
                if (steps.length === 0) return;

                // Create progress bar
                const progress = document.createElement('div');
                progress.className = 'tutorial-progress';
                const progressBar = document.createElement('div');
                progressBar.className = 'tutorial-progress-bar';
                progress.appendChild(progressBar);

                // Create controls
                const controls = document.createElement('div');
                controls.className = 'tutorial-controls';

                const prevBtn = document.createElement('button');
                prevBtn.className = 'tutorial-button';
                prevBtn.textContent = '← Previous';
                prevBtn.disabled = true;

                const nextBtn = document.createElement('button');
                nextBtn.className = 'tutorial-button';
                nextBtn.textContent = 'Next →';

                controls.appendChild(prevBtn);
                controls.appendChild(nextBtn);

                // Add to tutorial
                tutorial.appendChild(progress);
                tutorial.appendChild(controls);

                // Initialize state
                let currentStep = 0;
                steps.forEach((step, i) => {
                    if (i !== 0) step.style.display = 'none';
                });
                updateProgress();

                // Add event listeners
                prevBtn.addEventListener('click', () => {
                    if (currentStep > 0) {
                        steps[currentStep].style.display = 'none';
                        currentStep--;
                        steps[currentStep].style.display = 'block';
                        updateControls();
                        updateProgress();
                    }
                });

                nextBtn.addEventListener('click', () => {
                    if (currentStep < steps.length - 1) {
                        steps[currentStep].style.display = 'none';
                        currentStep++;
                        steps[currentStep].style.display = 'block';
                        updateControls();
                        updateProgress();
                    }
                });

                function updateControls() {
                    prevBtn.disabled = currentStep === 0;
                    nextBtn.disabled = currentStep === steps.length - 1;
                    nextBtn.textContent = currentStep === steps.length - 1 ? 'Finish' : 'Next →';
                }

                function updateProgress() {
                    const percent = ((currentStep + 1) / steps.length) * 100;
                    progressBar.style.width = `${percent}%`;
                }
            });
        };

        // Create interactive component diagrams
        const createComponentDiagrams = () => {
            const diagrams = document.querySelectorAll('.component-diagram');

            diagrams.forEach(diagram => {
                const nodes = diagram.querySelectorAll('.component-node');
                const connections = diagram.querySelectorAll('.component-connection');

                // Add interaction to nodes
                nodes.forEach(node => {
                    node.addEventListener('mouseenter', () => {
                        node.classList.add('active');

                        // Highlight connected nodes and connections
                        const nodeId = node.dataset.id;
                        connections.forEach(conn => {
                            if (conn.dataset.from === nodeId || conn.dataset.to === nodeId) {
                                conn.classList.add('active');

                                // Find connected node
                                const connectedId = conn.dataset.from === nodeId ? conn.dataset.to : conn.dataset.from;
                                nodes.forEach(n => {
                                    if (n.dataset.id === connectedId) {
                                        n.classList.add('active');
                                    }
                                });
                            }
                        });
                    });

                    node.addEventListener('mouseleave', () => {
                        nodes.forEach(n => n.classList.remove('active'));
                        connections.forEach(c => c.classList.remove('active'));
                    });
                });
            });
        };

        // Create interactive code playgrounds
        const createCodePlaygrounds = () => {
            const playgrounds = document.querySelectorAll('.code-playground');

            playgrounds.forEach(playground => {
                const editor = playground.querySelector('.code-playground-editor');
                const output = playground.querySelector('.code-playground-output');
                const runBtn = playground.querySelector('.code-playground-action[data-action="run"]');
                const resetBtn = playground.querySelector('.code-playground-action[data-action="reset"]');

                if (!editor || !output) return;

                // Store original code
                const originalCode = editor.textContent;

                // Make editor editable
                editor.contentEditable = 'true';
                editor.spellcheck = false;

                // Run button functionality
                if (runBtn) {
                    runBtn.addEventListener('click', () => {
                        const code = editor.textContent;
                        output.textContent = `// Output would appear here in a real environment\n// Code length: ${code.length} characters`;

                        // Simulate processing
                        output.classList.add('processing');
                        setTimeout(() => {
                            output.classList.remove('processing');
                        }, 500);
                    });
                }

                // Reset button functionality
                if (resetBtn) {
                    resetBtn.addEventListener('click', () => {
                        editor.textContent = originalCode;
                        output.textContent = '';
                    });
                }
            });
        };

        // Export functionality
        const setupExportFunctionality = () => {
            const exportModal = document.getElementById('exportModal');
            const exportModalTitle = document.getElementById('exportModalTitle');
            const exportModalMessage = document.getElementById('exportModalMessage');
            const exportProgressBar = document.getElementById('exportProgressBar');
            const exportModalClose = document.getElementById('exportModalClose');
            const exportModalCancel = document.getElementById('exportModalCancel');
            const exportModalDownload = document.getElementById('exportModalDownload');
            const exportPdfButton = document.getElementById('exportPdf');
            const exportCodebookButton = document.getElementById('exportCodebook');

            let exportType = '';
            let exportData = null;
            let exportFilename = '';

            // Show export modal
            const showExportModal = (type) => {
                exportType = type;
                exportModalTitle.textContent = type === 'pdf' ? 'Export PDF' : 'Generate Codebook';
                exportModalMessage.textContent = type === 'pdf'
                    ? 'Preparing documentation for PDF export...'
                    : 'Extracting variable definitions and generating codebook...';
                exportProgressBar.style.width = '0%';
                exportModalDownload.disabled = true;
                exportModal.classList.add('active');

                // Start the export process
                setTimeout(() => {
                    startExport(type);
                }, 500);
            };

            // Hide export modal
            const hideExportModal = () => {
                exportModal.classList.remove('active');
                exportType = '';
                exportData = null;
                exportFilename = '';
            };

            // Close modal events
            exportModalClose.addEventListener('click', hideExportModal);
            exportModalCancel.addEventListener('click', hideExportModal);

            // Export buttons click handlers
            exportPdfButton.addEventListener('click', () => {
                showExportModal('pdf');
            });

            exportCodebookButton.addEventListener('click', () => {
                showExportModal('codebook');
            });

            // Download button click handler
            exportModalDownload.addEventListener('click', () => {
                if (exportData) {
                    // Create a download link
                    const link = document.createElement('a');
                    link.href = exportData;
                    link.download = exportFilename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Hide modal after download starts
                    setTimeout(hideExportModal, 1000);
                }
            });

            // Start the export process
            const startExport = (type) => {
                if (type === 'pdf') {
                    generatePDF();
                } else {
                    generateCodebook();
                }
            };

            // Generate PDF from the current documentation
            const generatePDF = () => {
                try {
                    // Update progress to start
                    exportProgressBar.style.width = '10%';

                    // Get all the content directly
                    const mainContent = document.querySelector('.main-content');

                    // Create a new document for the PDF content
                    const pdfDoc = document.createElement('div');
                    pdfDoc.style.padding = '20px';
                    pdfDoc.style.backgroundColor = 'white';
                    pdfDoc.style.color = 'black';
                    pdfDoc.style.fontFamily = 'Arial, sans-serif';

                    // Add a title page
                    const titlePage = document.createElement('div');
                    titlePage.style.textAlign = 'center';
                    titlePage.style.padding = '100px 20px';
                    titlePage.style.pageBreakAfter = 'always';
                    titlePage.innerHTML = `
                        <h1 style="font-size: 32px; margin-bottom: 20px; color: #4361ee;">SOCIAL.ent.apc Documentation</h1>
                        <p style="font-size: 16px; margin-bottom: 40px;">Comprehensive documentation for the SOCIAL.ent.apc file</p>
                        <p style="font-size: 14px;">Generated on ${new Date().toLocaleDateString()}</p>
                    `;

                    pdfDoc.appendChild(titlePage);

                    // Create a table of contents
                    const toc = document.createElement('div');
                    toc.style.padding = '20px';
                    toc.style.pageBreakAfter = 'always';
                    toc.innerHTML = '<h2 style="color: #4361ee; margin-bottom: 20px;">Table of Contents</h2><ul style="list-style-type: none; padding: 0;"></ul>';
                    const tocList = toc.querySelector('ul');

                    // Get all sections and add to TOC
                    const sections = mainContent.querySelectorAll('section');
                    sections.forEach((section, index) => {
                        const heading = section.querySelector('h2, h3, h4') || { textContent: `Section ${index + 1}` };
                        const li = document.createElement('li');
                        li.style.margin = '10px 0';
                        li.style.paddingLeft = '10px';
                        li.style.borderLeft = '2px solid #4361ee';
                        li.textContent = heading.textContent;
                        tocList.appendChild(li);
                    });

                    pdfDoc.appendChild(toc);

                    // Update progress
                    exportProgressBar.style.width = '30%';

                    // Process each section
                    sections.forEach((section, index) => {
                        // Create a clone of the section
                        const sectionClone = document.createElement('div');
                        sectionClone.style.marginBottom = '30px';
                        sectionClone.style.paddingBottom = '20px';
                        sectionClone.style.borderBottom = '1px dashed #ccc';

                        // Get the heading
                        const heading = section.querySelector('h2, h3, h4');
                        if (heading) {
                            const headingClone = document.createElement('h2');
                            headingClone.style.color = '#4361ee';
                            headingClone.style.marginBottom = '20px';
                            headingClone.textContent = heading.textContent;
                            sectionClone.appendChild(headingClone);
                        }

                        // Get paragraphs and other content
                        const paragraphs = section.querySelectorAll('p');
                        paragraphs.forEach(p => {
                            const pClone = document.createElement('p');
                            pClone.style.marginBottom = '15px';
                            pClone.style.lineHeight = '1.5';
                            pClone.textContent = p.textContent;
                            sectionClone.appendChild(pClone);
                        });

                        // Get code blocks
                        const codeBlocks = section.querySelectorAll('pre');
                        codeBlocks.forEach(pre => {
                            const codeClone = document.createElement('div');
                            codeClone.style.backgroundColor = '#f5f5f5';
                            codeClone.style.padding = '15px';
                            codeClone.style.borderRadius = '5px';
                            codeClone.style.fontFamily = 'monospace';
                            codeClone.style.whiteSpace = 'pre-wrap';
                            codeClone.style.marginBottom = '20px';
                            codeClone.style.pageBreakInside = 'avoid';
                            codeClone.textContent = pre.textContent;
                            sectionClone.appendChild(codeClone);
                        });

                        // Add page break for all but the last section
                        if (index < sections.length - 1) {
                            sectionClone.style.pageBreakAfter = 'always';
                        }

                        pdfDoc.appendChild(sectionClone);
                    });

                    // Update progress
                    exportProgressBar.style.width = '60%';

                    // Add to document temporarily (hidden)
                    pdfDoc.style.position = 'absolute';
                    pdfDoc.style.left = '-9999px';
                    pdfDoc.style.width = '800px'; // Fixed width for better PDF generation
                    document.body.appendChild(pdfDoc);

                    // Configure PDF options
                    const options = {
                        margin: [15, 15, 15, 15],
                        filename: 'SOCIAL.ent.apc-documentation.pdf',
                        image: { type: 'jpeg', quality: 0.95 },
                        html2canvas: { scale: 1.5, useCORS: true, logging: false },
                        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                    };

                    // Generate PDF
                    setTimeout(() => {
                        exportProgressBar.style.width = '80%';

                        html2pdf()
                            .from(pdfDoc)
                            .set(options)
                            .save()
                            .then(() => {
                                // Clean up
                                if (pdfDoc && pdfDoc.parentNode) {
                                    pdfDoc.parentNode.removeChild(pdfDoc);
                                }

                                // Complete the export
                                exportProgressBar.style.width = '100%';
                                exportModalMessage.textContent = 'PDF export complete! The download should start automatically.';

                                setTimeout(() => {
                                    hideExportModal();
                                }, 3000);
                            })
                            .catch(error => {
                                console.error('PDF generation error:', error);
                                exportModalMessage.textContent = 'There was an error generating the PDF. Please try again.';
                                exportProgressBar.style.width = '0%';

                                // Enable download button to close modal
                                exportModalDownload.disabled = false;
                                exportModalDownload.textContent = 'Close';
                                exportModalDownload.addEventListener('click', hideExportModal, { once: true });

                                // Clean up
                                if (pdfDoc && pdfDoc.parentNode) {
                                    pdfDoc.parentNode.removeChild(pdfDoc);
                                }
                            });
                    }, 1000);
                } catch (error) {
                    console.error('PDF setup error:', error);
                    exportModalMessage.textContent = 'There was an error preparing the PDF. Please try again.';
                    exportProgressBar.style.width = '0%';

                    // Enable download button to close modal
                    exportModalDownload.disabled = false;
                    exportModalDownload.textContent = 'Close';
                    exportModalDownload.addEventListener('click', hideExportModal, { once: true });
                }
            };

            // Generate a codebook from the documentation
            const generateCodebook = () => {
                try {
                    // Update progress to start
                    exportProgressBar.style.width = '10%';

                    // Extract variable definitions from the documentation
                    const variables = extractVariableDefinitions();

                    // Create a container for the codebook content with fixed styling
                    const codebookDoc = document.createElement('div');
                    codebookDoc.style.padding = '20px';
                    codebookDoc.style.backgroundColor = 'white';
                    codebookDoc.style.color = 'black';
                    codebookDoc.style.fontFamily = 'Arial, sans-serif';
                    codebookDoc.style.width = '800px'; // Fixed width for better PDF generation

                    // Add a title page
                    const titlePage = document.createElement('div');
                    titlePage.style.textAlign = 'center';
                    titlePage.style.padding = '100px 20px';
                    titlePage.style.pageBreakAfter = 'always';
                    titlePage.innerHTML = `
                        <h1 style="font-size: 32px; margin-bottom: 20px; color: #4361ee;">SOCIAL.ent.apc Codebook</h1>
                        <p style="font-size: 16px; margin-bottom: 40px;">Variable definitions and data types reference</p>
                        <p style="font-size: 14px;">Generated on ${new Date().toLocaleDateString()}</p>
                    `;

                    codebookDoc.appendChild(titlePage);

                    // Update progress
                    exportProgressBar.style.width = '25%';

                    // Add table of contents
                    const toc = document.createElement('div');
                    toc.style.padding = '20px';
                    toc.style.pageBreakAfter = 'always';
                    toc.innerHTML = `
                        <h2 style="font-size: 24px; margin-bottom: 20px; color: #4361ee;">Table of Contents</h2>
                        <ul style="list-style-type: none; padding: 0;">
                            ${Object.keys(variables).map(category =>
                                `<li style="margin: 10px 0; padding-left: 10px; border-left: 2px solid #4361ee;">
                                    ${category} Variables
                                </li>`
                            ).join('')}
                        </ul>
                    `;

                    codebookDoc.appendChild(toc);

                    // Update progress
                    exportProgressBar.style.width = '40%';

                    // Add the variable definitions
                    Object.keys(variables).forEach((category, categoryIndex) => {
                        const section = document.createElement('div');
                        section.style.marginBottom = '30px';
                        section.style.paddingBottom = '20px';

                        // Add section heading
                        const heading = document.createElement('h2');
                        heading.style.fontSize = '24px';
                        heading.style.marginBottom = '20px';
                        heading.style.color = '#4361ee';
                        heading.style.paddingBottom = '5px';
                        heading.style.borderBottom = '2px solid #4361ee';
                        heading.textContent = `${category} Variables`;
                        section.appendChild(heading);

                        // Add section description
                        const description = document.createElement('p');
                        description.style.marginBottom = '20px';
                        description.style.lineHeight = '1.5';
                        description.textContent = `This section contains all ${category.toLowerCase()} variables used in the SOCIAL.ent.apc file.`;
                        section.appendChild(description);

                        // Create a table for the variables
                        const table = document.createElement('table');
                        table.style.width = '100%';
                        table.style.borderCollapse = 'collapse';
                        table.style.marginBottom = '30px';
                        table.style.pageBreakInside = 'avoid';

                        // Add table header
                        const thead = document.createElement('thead');
                        const headerRow = document.createElement('tr');

                        const headers = ['Variable Name', 'Data Type', 'Description'];
                        headers.forEach(headerText => {
                            const th = document.createElement('th');
                            th.style.border = '1px solid #ddd';
                            th.style.padding = '12px';
                            th.style.textAlign = 'left';
                            th.style.backgroundColor = '#f1f3f5';
                            th.style.color = '#333';
                            th.textContent = headerText;
                            headerRow.appendChild(th);
                        });

                        thead.appendChild(headerRow);
                        table.appendChild(thead);

                        // Add table body
                        const tbody = document.createElement('tbody');
                        variables[category].forEach((variable, index) => {
                            const row = document.createElement('tr');
                            row.style.backgroundColor = index % 2 === 0 ? '#fff' : '#f9f9f9';

                            // Variable name cell
                            const nameCell = document.createElement('td');
                            nameCell.style.border = '1px solid #ddd';
                            nameCell.style.padding = '12px';
                            nameCell.style.fontWeight = 'bold';
                            nameCell.style.fontFamily = 'monospace';
                            nameCell.textContent = variable.name;
                            row.appendChild(nameCell);

                            // Data type cell
                            const typeCell = document.createElement('td');
                            typeCell.style.border = '1px solid #ddd';
                            typeCell.style.padding = '12px';
                            typeCell.style.fontFamily = 'monospace';
                            typeCell.style.color = '#0066cc';
                            typeCell.textContent = variable.type;
                            row.appendChild(typeCell);

                            // Description cell
                            const descCell = document.createElement('td');
                            descCell.style.border = '1px solid #ddd';
                            descCell.style.padding = '12px';
                            descCell.style.lineHeight = '1.4';
                            descCell.textContent = variable.description;
                            row.appendChild(descCell);

                            tbody.appendChild(row);
                        });

                        table.appendChild(tbody);
                        section.appendChild(table);

                        // Add the section to the document
                        codebookDoc.appendChild(section);

                        // Add a page break after each section except the last one
                        if (categoryIndex < Object.keys(variables).length - 1) {
                            const pageBreak = document.createElement('div');
                            pageBreak.style.pageBreakAfter = 'always';
                            codebookDoc.appendChild(pageBreak);
                        }
                    });

                    // Update progress
                    exportProgressBar.style.width = '60%';

                    // Add to document temporarily (hidden)
                    codebookDoc.style.position = 'absolute';
                    codebookDoc.style.left = '-9999px';
                    document.body.appendChild(codebookDoc);

                    // Configure PDF options
                    const options = {
                        margin: [15, 15, 15, 15],
                        filename: 'SOCIAL.ent.apc-codebook.pdf',
                        image: { type: 'jpeg', quality: 0.95 },
                        html2canvas: { scale: 1.5, useCORS: true, logging: false },
                        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                    };

                    // Generate PDF
                    setTimeout(() => {
                        exportProgressBar.style.width = '80%';

                        html2pdf()
                            .from(codebookDoc)
                            .set(options)
                            .save()
                            .then(() => {
                                // Clean up
                                if (codebookDoc && codebookDoc.parentNode) {
                                    codebookDoc.parentNode.removeChild(codebookDoc);
                                }

                                // Complete the export
                                exportProgressBar.style.width = '100%';
                                exportModalMessage.textContent = 'Codebook generation complete! The download should start automatically.';

                                setTimeout(() => {
                                    hideExportModal();
                                }, 3000);
                            })
                            .catch(error => {
                                console.error('Codebook generation error:', error);
                                exportModalMessage.textContent = 'There was an error generating the codebook. Please try again.';
                                exportProgressBar.style.width = '0%';

                                // Enable download button to close modal
                                exportModalDownload.disabled = false;
                                exportModalDownload.textContent = 'Close';
                                exportModalDownload.addEventListener('click', hideExportModal, { once: true });

                                // Clean up
                                if (codebookDoc && codebookDoc.parentNode) {
                                    codebookDoc.parentNode.removeChild(codebookDoc);
                                }
                            });
                    }, 1000);
                } catch (error) {
                    console.error('Codebook setup error:', error);
                    exportModalMessage.textContent = 'There was an error preparing the codebook. Please try again.';
                    exportProgressBar.style.width = '0%';

                    // Enable download button to close modal
                    exportModalDownload.disabled = false;
                    exportModalDownload.textContent = 'Close';
                    exportModalDownload.addEventListener('click', hideExportModal, { once: true });
                }
            };

            // Extract variable definitions from the documentation
            const extractVariableDefinitions = () => {
                // This would normally parse the actual documentation
                // For demonstration, we'll return a predefined set of variables
                return {
                    'Global': [
                        { name: 'tot', type: 'numeric', description: 'Used for totals and summations' },
                        { name: 'OK', type: 'numeric', description: 'Flag for operation success/user confirmation' },
                        { name: 'i, j, n, p, x', type: 'numeric', description: 'Loop counters and array indices' },
                        { name: 'err', type: 'numeric', description: 'Error code storage' },
                        { name: 'id_marker', type: 'numeric', description: 'Marker ID for GPS mapping' },
                        { name: 'initialLat, initialLon', type: 'numeric', description: 'Initial GPS coordinates' },
                        { name: 'maxmemb', type: 'numeric', description: 'Maximum number of household members' },
                        { name: 'codes(30)', type: 'array', description: 'Stores numeric codes for value sets' },
                        { name: 'strnotes', type: 'alpha(900)', description: 'Stores notes/comments from interviewers' },
                        { name: 'labels(30)', type: 'array alpha(40)', description: 'Stores text labels for value sets' },
                        { name: 'mymap', type: 'map', description: 'Map object for GPS functionality' },
                        { name: 'MyValueset', type: 'valueset', description: 'Dynamic value set for dropdown selections' }
                    ],
                    'Household': [
                        { name: 'QHFIRSTN', type: 'alpha', description: 'First name of household member' },
                        { name: 'QHLASTN', type: 'alpha', description: 'Last name of household member' },
                        { name: 'QHRELAT', type: 'numeric', description: 'Relationship to household head' },
                        { name: 'QHAGE', type: 'numeric', description: 'Age of household member' },
                        { name: 'QHSEX', type: 'numeric', description: 'Sex of household member' },
                        { name: 'QHSM', type: 'numeric', description: 'Marital status of household member' },
                        { name: 'QHMEMBER', type: 'numeric', description: 'Total number of household members' }
                    ],
                    'Education': [
                        { name: 'ED00A', type: 'numeric', description: 'Education status indicator' },
                        { name: 'ED01', type: 'numeric', description: 'Highest level of education completed' },
                        { name: 'ED02', type: 'numeric', description: 'Currently attending school' },
                        { name: 'ED03', type: 'numeric', description: 'Type of school attending' }
                    ],
                    'Health': [
                        { name: 'SA00', type: 'numeric', description: 'Health status indicator' },
                        { name: 'SA01', type: 'numeric', description: 'Has chronic illness' },
                        { name: 'SA02', type: 'numeric', description: 'Has disability' },
                        { name: 'SA03', type: 'numeric', description: 'Health insurance coverage' }
                    ],
                    'Housing': [
                        { name: 'H01', type: 'numeric', description: 'Type of dwelling' },
                        { name: 'H02', type: 'numeric', description: 'Ownership status' },
                        { name: 'H03', type: 'numeric', description: 'Number of rooms' },
                        { name: 'H04', type: 'numeric', description: 'Main source of drinking water' },
                        { name: 'H05', type: 'numeric', description: 'Main source of lighting' },
                        { name: 'H06', type: 'numeric', description: 'Main cooking fuel' }
                    ],
                    'Economic Activity': [
                        { name: 'ACTA0', type: 'numeric', description: 'Economic activity status' },
                        { name: 'ACTA1', type: 'numeric', description: 'Main occupation' },
                        { name: 'ACTA2', type: 'numeric', description: 'Employment status' },
                        { name: 'ACTA3', type: 'numeric', description: 'Industry sector' },
                        { name: 'ACTA4', type: 'numeric', description: 'Monthly income' }
                    ]
                };
            };
        };

        // Initialize all interactive elements when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Make all sections visible immediately to fix content display issue
            document.querySelectorAll('section').forEach(section => {
                section.classList.add('visible');
            });

            // Initialize other components
            enhanceCodeBlocks();
            createTutorials();
            createComponentDiagrams();
            createCodePlaygrounds();
            setupExportFunctionality();

            // Set initial icon based on theme
            const icon = themeToggle.querySelector('.theme-toggle-icon');
            if (icon) {
                icon.textContent = body.getAttribute('data-theme') === 'dark' ? '🌙' : '☀️';
            }

            // Set up section observers for future scrolling
            setTimeout(() => {
                observeSections();
            }, 500);
        });
    </script>
</body>
</html>
