# SOCIAL.ent.apc Documentation

## 1. Overview

The `SOCIAL.ent.apc` file is a CSPro (Census and Survey Processing System) application logic file that serves as the core programming component for the SOCIAL data entry application. This application is part of a larger social registry system designed for collecting household survey data in Algeria, specifically for the "Enquête Nationale sur la Consommation" (National Consumption Survey).

The application handles various aspects of data collection including:
- Household member information
- Education data
- Health information
- Housing conditions
- Economic activities
- Asset ownership
- Income and expenditure data
- Agricultural activities
- Social assistance and transfers

The file contains logic procedures that control data validation, skip patterns, and dynamic questionnaire flow based on respondent answers.

## 2. Global Variables and Functions

### 2.1 Global Variables

```
numeric tot, OK, i, err, id_marker, initialLat, initialLon, j, n, p, x, tim, maxmemb;
array codes(30); 
alpha(900) strnotes; 
array alpha(40) labels(30); 
numeric e;
map mymap;
valueset MyValueset;
```

| Variable | Type | Purpose |
|----------|------|---------|
| `tot`, `OK`, `i`, `err`, `j`, `n`, `p`, `x`, `tim`, `e` | numeric | General-purpose counters and flags |
| `id_marker` | numeric | Marker ID for GPS mapping |
| `initialLat`, `initialLon` | numeric | Initial GPS coordinates |
| `maxmemb` | numeric | Maximum number of household members |
| `codes(30)` | array | Stores numeric codes for value sets |
| `strnotes` | alpha(900) | Stores notes/comments from interviewers |
| `labels(30)` | array alpha(40) | Stores text labels for value sets |
| `mymap` | map | Map object for GPS functionality |
| `MyValueset` | valueset | Dynamic value set for dropdown selections |

### 2.2 Functions

#### 2.2.1 Map and GPS Functions

```
function closeMap(map m) 
    m.hide(); 
end; 

function retakeGPS(map m) 
    m.hide();
    A10gps = 2;
    reenter A10GPS; 
end; 

function mapClicked() 
    numeric lat = mymap.getLastClickLatitude(); 
    numeric lon = mymap.getLastClickLongitude(); 
    numeric d = gps(distance,lat,lon,initialLat,initialLon);
    
    if d <= 30 then // we move the point only if he is not too far from the initial position
        mymap.setMarkerLocation(id_marker, lat, lon); 
        A10A = lon;
        A10B = lat;
    else
        errmsg("Vous êtes trop loin de la position initiale (%d mètres), pour éviter toute erreur nous ne nous y déplacerons pas",d);
    endif;
end;

function adjustPoint(map m);
    m.setOnClick(mapClicked());
end;
```

These functions handle GPS mapping functionality:
- `closeMap()`: Hides the map interface
- `retakeGPS()`: Allows re-capturing GPS coordinates
- `mapClicked()`: Handles map click events, captures coordinates, and validates the distance from initial position
- `adjustPoint()`: Sets up the click handler for the map

#### 2.2.2 Utility Functions

```
function clean_labels();
  numeric z;
  do z = 1 while z <= 30
    codes(z)  = notappl;
    labels(z) = "";
  enddo;
end;

function endmess();
  { Returns true if response is REVIEW }
  endmess = ( demode() = add &
             accept("FIN DE L'ENQUETE MENAGE",
                    "REVISITER LE QUESTIONNAIRE",
                    "FINALISER") <> 2);
end;

function TestValidityName(string name) 
    if strip(name) = "" then
        errmsg("Le nom/texte ne peut pas être vide");
        reenter; //not allow to continue without fill a name
    elseif length(strip(name)) < 2 then
        errmsg("Le nom/texte %s est trop court",strip(name));
        reenter;
    elseif pos(name[1:1], " 0123456789@,;:!?/#-+*.=()\_{}°[]")>0 then
        errmsg("Le nom/texte ne peut pas commencer par %s",name[1:1]);
        reenter;
    endif;    
end;

function Onstop()
    ok = accept(TR("Que veux-tu ?"), tr("Sauvegarde"),tr("Annuler"),tr("Quitter sans sauvegarder"));
    
    if ok in 3 then
        stop(1);
    elseif ok = 1 then
        savepartial();
        stop(1);
    elseif ok in 0, 2 then
        reenter;
    endif;
end;
```

These utility functions provide common functionality:
- `clean_labels()`: Resets the codes and labels arrays
- `endmess()`: Handles end-of-survey messaging and navigation
- `TestValidityName()`: Validates name entries with specific rules
- `Onstop()`: Handles application exit with options to save or discard data

## 3. Main Procedures

The file contains numerous procedures that correspond to different sections of the questionnaire. Each procedure typically handles validation, skip patterns, and data entry for specific fields.

### 3.1 Identification Section (SECTA_FORM)

This section handles the basic identification information for the household:

```
PROC SOCIAL_FF
PROC SOCIAL_LEVEL
stop(1);

PROC SECTA_FORM
preproc
    if ispartial() then
        ok = accept("C'est un questionnaire partiel, voulez-vous aller à la dernière position ?","Oui","Non");
        if ok = 1 then
            advance to getsymbol(savepartial);
        endif;
    endif;
```

Key procedures include:
- `A01-A16`: Household identification, date, interviewer ID, location codes
- `START_TIME`: Captures interview start time

### 3.2 Household Roster Section (QHSEC01X_FORM)

This section captures information about each household member:

```
PROC QHLINE
preproc
  $ = curocc();

PROC QHFIRSTN
  { Check that response is alphabetic and starts in the first column }
  TestValidityName(QHFIRSTN);

PROC QHLASTN
  TestValidityName(QHLASTN);

PROC QHRELAT
 if curocc() = 1 <=>  $ <> 1 then
    errmsg( 0031 );
    reenter
     elseif $ = 2 & curocc() <> 2 then
    errmsg( 0033 );
    reenter
  endif;
```

Key fields include:
- `QHFIRSTN`, `QHLASTN`: First and last names
- `QHRELAT`: Relationship to household head
- `QHAGE`: Age with validation
- `QHSEX`: Sex with validation for spouse
- `QHSM`: Marital status

### 3.3 Education Section

Captures educational information for household members:

```
PROC ED00A
preproc
$=curocc();
 if QHAGE(curocc()) < 3 then 
     skip to next ED00A 
 endif;
  if curocc() > QHMEMBER then   
    endsect
  endif;
```

### 3.4 Health Section (SA00)

Collects health-related information:

```
PROC SA00
preproc
$=curocc();
  if curocc() > QHMEMBER then   
    endsect
  endif;
```

### 3.5 Housing Section (H00-H39)

Collects information about housing conditions:

```
PROC H01
if $ = 9 then editnote() endif;
```

### 3.6 Economic Activities Section (ACTA0-ACTE_18)

Captures information about economic activities of household members:

```
PROC ACTA0
preproc
  { Initialize household members' questions with information already collected or known }
 $ = curocc();

  if curocc() > QHMEMBER then   
    endsect
  endif;
```

### 3.7 Income and Expenditure Sections

Multiple sections capture income and expenditure data:
- Non-agricultural enterprises (RNA00-RNH10C)
- Food expenditure (ALI01-ALIMA006)
- Transfers and remittances (TR12-TRAUU)

### 3.8 Agricultural Activities Section

Captures information about agricultural activities:
- Crop production (AGRC02B-AGRC02B1Y)
- Livestock (AGRC02C-AGRC02B1U)
- Agricultural equipment (AGRCF2-AGRCF2_3)

## 4. Integration with Other Components

The SOCIAL.ent.apc file is part of a larger system that includes:

1. **Dictionary Files**: 
   - SOCIAL.dcf: Defines the data structure and variables
   
2. **Form Files**:
   - SOCIAL.fmf: Defines the user interface layout

3. **Menu System**:
   - MENU.ent.apc: Provides navigation and application control
   
4. **Sample Management**:
   - SAMPLE.ent.apc: Handles sample selection and assignment

## 5. Key Features

1. **Dynamic Value Sets**: Many procedures use the `clean_labels()` function to create dynamic dropdown lists based on household members.

2. **GPS Integration**: The application includes GPS functionality for capturing household locations.

3. **Skip Patterns**: Extensive use of conditional logic to control questionnaire flow.

4. **Data Validation**: Comprehensive validation rules to ensure data quality.

5. **Partial Save**: Supports saving partial interviews and resuming later.

## 6. Usage Examples

### Example 1: Household Member Selection

```
PROC QHRESP
onfocus
  clean_labels();
  j = 0;
  do i = 1 while i <= QHMEMBER
    if QHAGE(i) in 12:98 then
      codes(j)  = i;
      labels(j) = concat( strip(QHFIRSTN(i)), " ", strip(QHLASTN(i)) );
      j = j + 1;
    endif;
  enddo;
  SetValueSet( @GetSymbol(), codes, labels );
```

This code creates a dynamic dropdown list of eligible household members.

### Example 2: GPS Capture

```
function mapClicked() 
    numeric lat = mymap.getLastClickLatitude(); 
    numeric lon = mymap.getLastClickLongitude(); 
    numeric d = gps(distance,lat,lon,initialLat,initialLon);
    
    if d <= 30 then // we move the point only if he is not too far from the initial position
        mymap.setMarkerLocation(id_marker, lat, lon); 
        A10A = lon;
        A10B = lat;
    else
        errmsg("Vous êtes trop loin de la position initiale (%d mètres), pour éviter toute erreur nous ne nous y déplacerons pas",d);
    endif;
end;
```

This function captures GPS coordinates with validation to ensure accuracy.

## 8. Conclusion

The SOCIAL.ent.apc file is a comprehensive CSPro application logic file that handles a complex household survey with multiple sections. It includes robust validation, dynamic questionnaire flow, and integration with GPS functionality. The application is designed for the Algerian National Consumption Survey and collects detailed information about households, their members, and various socioeconomic indicators.
