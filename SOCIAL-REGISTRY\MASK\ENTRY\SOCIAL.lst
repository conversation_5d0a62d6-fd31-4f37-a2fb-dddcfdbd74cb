﻿Application     D:\GitHub\SOCIAL-REGISTRY\MASK\ENTRY\SOCIAL.ent
Type            ENTRY
Input Data      <<Empty>> 

Date            Aug 30, 2024
Start Time      15:17:09
End Time        15:17:27


***  has 1 message (0 E / 0 W / 0 U)
    A    1010 Application error: protected field A02 is out of range - value is  DEFAULT

System messages:

  Number        Freq         Message Text                                                                               
  ------        ----         ------------                                                                               
   88889           <USER>         <GROUP>: Hors valeurs valides! Entrer une valeur valide pour %s%s                        
   99990           1         Operator entered a value out of range                                                      


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     D:\GitHub\SOCIAL-REGISTRY\MASK\ENTRY\SOCIAL.ent
Type            ENTRY
Input Data      <<Empty>> 

Date            Aug 30, 2024
Start Time      15:17:52
End Time        15:19:09


User unnumbered messages:

    Line        Freq    %    Message Text                                                                          Denom
    ----        ----  -----  ------------                                                                          -----
     561           1      -  Incoherence between B12 and B11, please correct                                           -


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     D:\GitHub\SOCIAL-REGISTRY\MASK\ENTRY\SOCIAL.ent
Type            ENTRY
Input Data      <<Empty>> 

Date            Aug 30, 2024
Start Time      15:22:39
End Time        15:24:00


User unnumbered messages:

    Line        Freq    %    Message Text                                                                          Denom
    ----        ----  -----  ------------                                                                          -----
     492           1      -  Wrong number, please correct                                                              -


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     D:\GitHub\SOCIAL-REGISTRY\MASK\ENTRY\SOCIAL.ent
Type            ENTRY
Input Data      <<Empty>> 

Date            Aug 30, 2024
Start Time      15:24:31
End Time        15:25:10


User unnumbered messages:

    Line        Freq    %    Message Text                                                                          Denom
    ----        ----  -----  ------------                                                                          -----
     492           2      -  Wrong number, please correct                                                              -


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     D:\GitHub\SOCIAL-REGISTRY\MASK\ENTRY\SOCIAL.ent
Type            ENTRY
Input Data      <<Empty>> 

Date            Aug 30, 2024
Start Time      15:26:17
End Time        15:32:14


System messages:

  Number        Freq         Message Text                                                                               
  ------        ----         ------------                                                                               
   88889           <USER>         <GROUP>: Hors valeurs valides! Entrer une valeur valide pour %s%s                        
   99990           1         Operator entered a value out of range                                                      

User unnumbered messages:

    Line        Freq    %    Message Text                                                                          Denom
    ----        ----  -----  ------------                                                                          -----
     581           1      -  Wrong number, please correct. Verify that there is no alpha character and no              -
                             space                                                                                      
     663           2      -  Wrong number, please correct. Verify that there is no alpha character and no              -
                             space                                                                                      


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
