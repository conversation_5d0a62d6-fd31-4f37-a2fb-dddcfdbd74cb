﻿{Application 'EXPORT_123' logic file generated by CSPro}
PROC GLOBAL
FILE household, individual;
numeric i, currentSUP;

FUNCTION complete()

	XDAY = tonumber(timestring("%d", START_TIME));
	XMONTH = tonumber(timestring("%m", START_TIME));
	XYEAR = tonumber(timestring("%Y", START_TIME));
	XHOUR = tonumber(timestring("%H", START_TIME));
	XMINUTE = tonumber(timestring("%M", START_TIME));
	XSECOND = tonumber(timestring("%S", START_TIME));

	YDAY = tonumber(timestring("%d", END_TIME));
	YMONTH = tonumber(timestring("%m", END_TIME));
	YYEAR = tonumber(timestring("%Y", END_TIME));
	YHOUR = tonumber(timestring("%H", END_TIME));
	YMINUTE = tonumber(timestring("%M", END_TIME));
	YSECOND = tonumber(timestring("%S", END_TIME));

	INTERVIEW_DURATION_IN_MINUTES = (END_TIME-START_TIME)/60;
	
	 //loadcase(INFO_SAMPLE_DICT,A08); //for the old households
	 //loadcase(SAMPLE_DICT,A08); // for the new households added on the field
{	
	if loadcase(INFO_SAMPLE_DICT,A08) and loadcase(SAMPLE_DICT,A08) then
		a06n=DIST_NAME(1); //this is the same for old and new households
		a07n=WARD_NAME(1);		

		if XA10 has A10 then // old households	
			a06n=DIST_NAME(A10);
			a07n=WARD_NAME(A10);
			a09n= SETTLEMENT(A10);
			COMPOUND_NUMBER = COMP_SRN(A10);
			COMPOUND_NAME = NAME_COMP(A10);
			SCLUSTER_CO = CLUSTER_CO(A10);
			SCLUSTER_SG = CLUSTER_SG(A10);
			SEA_SGMT = EA_SGMT(A10);		
		
		elseif XNUMBER has A10 and !special(XCLUSTER_CO(A10)) then // news households added on the field
			a09n= XSETTLEMENT(A10);
			COMPOUND_NUMBER = XSTRUCTNUM(A10);
			COMPOUND_NAME = XSTRUCNAME(A10);
			SCLUSTER_CO = XCLUSTER_CO(A10);
			SCLUSTER_SG = XCLUSTER_SG(A10);
			SEA_SGMT = XEA_SGMT(A10);	
		elseif  A10 > 0 then // this is for the duplicated cases of the first assignment. Because we don't have more information we take the same from the first line
			a09n= SETTLEMENT(1);
			COMPOUND_NUMBER = COMP_SRN(1);
			COMPOUND_NAME = NAME_COMP(1);
			SCLUSTER_CO = CLUSTER_CO(1);
			SCLUSTER_SG = CLUSTER_SG(1);
			SEA_SGMT = EA_SGMT(1);	
		endif;
		
	else //household not found may be because the sample file has not yet been transferred on the survey 
		a06n="";
		a07n="";
		a09n="";
		COMPOUND_NUMBER = notappl;
		COMPOUND_NAME = "";
		SCLUSTER_CO = notappl;
		SCLUSTER_SG = notappl;
		SEA_SGMT = "";
			
	ENDIF;

}	
	// if a08 = 30101 then a06n = "Kombo North"; a07n = "Banjulunding-A" ; a09n = "NEMA KUNKU" ; endif;
	// if a08 = 30102 then a06n = "Kombo North"; a07n = "Banjulunding-A" ; a09n = "NEMA KUNKU" ; endif;
	// if a08 = 30103 then a06n = "Kombo North"; a07n = "Banjulunding-A" ; a09n = "NEMA KUNKU" ; endif;


end;


PROC SOCIAL_FF
preproc
	currentSUP = 0; //this variable will register the current Supervisor during the runing of export
	
	filedelete("../REF/*.csidx");
	// setfile(INFO_SAMPLE_DICT,"../REF/sampleinfo.csdb");
	
PROC SOCIAL_LEVEL



	
	if currentSUP <> A03 then //We load the appropriate sample file corresponding to the supervisor
		currentSUP = A03;
		close(SAMPLE_DICT);
		setfile(SAMPLE_DICT,maketext("../REF/sample_%03d.dat",A03));
	endif;
  
  set behavior() export ( stata ,ItemSubitem ); {export done at LEVEL procedure}
	
	complete();
	export to household

	case_id()   //A06N A07N A09N COMPOUND_NUMBER COMPOUND_NAME SCLUSTER_CO SCLUSTER_SG SEA_SGMT
	SECTA //exclude (a06 a07 a09)
	SECTF
	SECTG
	SECTH
	SECTI
	SECTJ1
	SECTJ2
	SECTJ3
	SECTJ4
	SECTJ5
	SECTK1
	SECTK2
	SECTL
	OTHER_VARIABLES
	INTERVIEW_DURATION_IN_MINUTES XDAY XMONTH XYEAR XHOUR XMINUTE XSECOND YDAY YMONTH YYEAR YHOUR YMINUTE YSECOND
	;
	
	
	
	do i = 1 while i<= HHsize
		export to individual
		case_id()  //A06N A07N A09N COMPOUND_NUMBER COMPOUND_NAME SCLUSTER_CO SCLUSTER_SG SEA_SGMT
		SECTA //exclude (a06 a07 a09)
		SECTB(i) 
		SECTC(i) exclude( C01) //
		SECTD(i) exclude( D01)
		SECTE(i) exclude( E01) 
		SECTF
		SECTG
		SECTH
		SECTI
		SECTJ1
		SECTJ2
		SECTJ3
		SECTJ4
		SECTJ5
		SECTK1
		SECTK2
		SECTL
		OTHER_VARIABLES
		INTERVIEW_DURATION_IN_MINUTES XDAY XMONTH XYEAR XHOUR XMINUTE XSECOND YDAY YMONTH YYEAR YHOUR YMINUTE YSECOND
		;
		enddo;
