infix using "C:\GBOS\SOCIAL-REGISTRY\MASK\EXPORT\HOUSEHOLD1.DCT"

label variable a08      "EA number"
label variable a10      "Household number"
label variable a01      "Date of the interview"
label variable a02      "Interviewer"
label variable a03      "Supervisor"
label variable a04      "Area"
label variable a05      "LGA"
label variable a06      "District"
label variable a07      "Ward"
label variable a09      "Town/ Settlement"
label variable a10gps   "Take GPS"
label variable a10a     "Longitude"
label variable a10b     "Latitude"
label variable a11      "Full name of respondent"
label variable a12      "Telephone number 1"
label variable a12b     "Telephone number 2"
label variable a12c     "Telephone number 3"
label variable a13      "Address"
label variable a14      "Full name of head of household"
label variable a15      "Result of the household interview"
label variable a15x     "Other result"
label variable a16      "Consent"
label variable hhsize   "House size"
label variable f01      "What is the occupancy status of the dwelling?"
label variable f02      "What is the main construction material of the exterior wall of the main dwelling"
label variable f02x     "Other"
label variable f03      "What is the main material used for roofing of the main dwelling?"
label variable f03x     "Other"
label variable f04      "What is the main material used for the floor of the main dwelling?"
label variable f04x     "Other"
label variable f05      "What is the household?s main source of lightning?"
label variable f05x     "Other"
label variable f06      "What is the household?s main cooking fuel?"
label variable f06x     "Other"
label variable f07      "What type of toilet facility does your household use?"
label variable f07x     "Other"
label variable f08      "Is the toilet facility shared with other households?"
label variable f09      "How many household in total use this toilet facility including your own househol"
label variable f10      "What is the main source of drinking water used by the household?"
label variable f10x     "Other"
label variable f11      "How does the household usually dispose of rubbish/refuse?"
label variable f11x     "Other"

label variable g00_01   "Service facility (Drinking water supply)"
label variable g00_02   "Service facility (Market/Lumo)"
label variable g00_03   "Service facility (Pre-school)"
label variable g00_04   "Service facility (Primary school.)"
label variable g00_05   "Service facility (Secondary school)"
label variable g00_06   "Service facility (Health center / Dispensary)"
label variable g00_07   "Service facility (Post office)"
label variable g00_08   "Service facility (Police post)"
label variable g00_09   "Service facility (All seasons road)"
label variable g00_10   "Service facility (Fire and Rescue services)"
label variable g00_11   "Service facility (Hospital)"
label variable g00_12   "Service facility (Financial services)"
label variable g00_13   "Service facility (Tertiary (including TVET))"
label variable g01_01   "How far (in km) are these service facilities from your household? (Drinking water supply)"
label variable g01_02   "How far (in km) are these service facilities from your household? (Market/Lumo)"
label variable g01_03   "How far (in km) are these service facilities from your household? (Pre-school)"
label variable g01_04   "How far (in km) are these service facilities from your household? (Primary school.)"
label variable g01_05   "How far (in km) are these service facilities from your household? (Secondary school)"
label variable g01_06   "How far (in km) are these service facilities from your household? (Health center / Dispensary)"
label variable g01_07   "How far (in km) are these service facilities from your household? (Post office)"
label variable g01_08   "How far (in km) are these service facilities from your household? (Police post)"
label variable g01_09   "How far (in km) are these service facilities from your household? (All seasons road)"
label variable g01_10   "How far (in km) are these service facilities from your household? (Fire and Rescue services)"
label variable g01_11   "How far (in km) are these service facilities from your household? (Hospital)"
label variable g01_12   "How far (in km) are these service facilities from your household? (Financial services)"
label variable g01_13   "How far (in km) are these service facilities from your household? (Tertiary (including TVET))"
label variable g02_01   "Minutes to reach these service facilities (Drinking water supply)"
label variable g02_02   "Minutes to reach these service facilities (Market/Lumo)"
label variable g02_03   "Minutes to reach these service facilities (Pre-school)"
label variable g02_04   "Minutes to reach these service facilities (Primary school.)"
label variable g02_05   "Minutes to reach these service facilities (Secondary school)"
label variable g02_06   "Minutes to reach these service facilities (Health center / Dispensary)"
label variable g02_07   "Minutes to reach these service facilities (Post office)"
label variable g02_08   "Minutes to reach these service facilities (Police post)"
label variable g02_09   "Minutes to reach these service facilities (All seasons road)"
label variable g02_10   "Minutes to reach these service facilities (Fire and Rescue services)"
label variable g02_11   "Minutes to reach these service facilities (Hospital)"
label variable g02_12   "Minutes to reach these service facilities (Financial services)"
label variable g02_13   "Minutes to reach these service facilities (Tertiary (including TVET))"
label variable g03_01   "means mainly use to reach these service facilities, ? (Drinking water supply)"
label variable g03_02   "means mainly use to reach these service facilities, ? (Market/Lumo)"
label variable g03_03   "means mainly use to reach these service facilities, ? (Pre-school)"
label variable g03_04   "means mainly use to reach these service facilities, ? (Primary school.)"
label variable g03_05   "means mainly use to reach these service facilities, ? (Secondary school)"
label variable g03_06   "means mainly use to reach these service facilities, ? (Health center / Dispensary)"
label variable g03_07   "means mainly use to reach these service facilities, ? (Post office)"
label variable g03_08   "means mainly use to reach these service facilities, ? (Police post)"
label variable g03_09   "means mainly use to reach these service facilities, ? (All seasons road)"
label variable g03_10   "means mainly use to reach these service facilities, ? (Fire and Rescue services)"
label variable g03_11   "means mainly use to reach these service facilities, ? (Hospital)"
label variable g03_12   "means mainly use to reach these service facilities, ? (Financial services)"
label variable g03_13   "means mainly use to reach these service facilities, ? (Tertiary (including TVET))"
label variable g03x_01  "Other mean (Drinking water supply)"
label variable g03x_02  "Other mean (Market/Lumo)"
label variable g03x_03  "Other mean (Pre-school)"
label variable g03x_04  "Other mean (Primary school.)"
label variable g03x_05  "Other mean (Secondary school)"
label variable g03x_06  "Other mean (Health center / Dispensary)"
label variable g03x_07  "Other mean (Post office)"
label variable g03x_08  "Other mean (Police post)"
label variable g03x_09  "Other mean (All seasons road)"
label variable g03x_10  "Other mean (Fire and Rescue services)"
label variable g03x_11  "Other mean (Hospital)"
label variable g03x_12  "Other mean (Financial services)"
label variable g03x_13  "Other mean (Tertiary (including TVET))"

label variable h00_01   "Item (Radio)"
label variable h00_02   "Item (TV)"
label variable h00_03   "Item (Mobile phone)"
label variable h00_04   "Item (Land telephone)"
label variable h00_05   "Item (Computer/ Laptop)"
label variable h00_06   "Item (Bicycle)"
label variable h00_07   "Item (Motorcycle)"
label variable h00_08   "Item (Car/Van)"
label variable h00_09   "Item (Truck/Lorry)"
label variable h00_10   "Item (Bus)"
label variable h00_11   "Item (Boat / Canoe)"
label variable h00_12   "Item (Animal drawn cart)"
label variable h01_01   "Does household/anyone in the household own  (Radio)?"
label variable h01_02   "Does household/anyone in the household own  (TV)?"
label variable h01_03   "Does household/anyone in the household own  (Mobile phone)?"
label variable h01_04   "Does household/anyone in the household own  (Land telephone)?"
label variable h01_05   "Does household/anyone in the household own  (Computer/ Laptop)?"
label variable h01_06   "Does household/anyone in the household own  (Bicycle)?"
label variable h01_07   "Does household/anyone in the household own  (Motorcycle)?"
label variable h01_08   "Does household/anyone in the household own  (Car/Van)?"
label variable h01_09   "Does household/anyone in the household own  (Truck/Lorry)?"
label variable h01_10   "Does household/anyone in the household own  (Bus)?"
label variable h01_11   "Does household/anyone in the household own  (Boat / Canoe)?"
label variable h01_12   "Does household/anyone in the household own  (Animal drawn cart)?"
label variable h02_01   "What is the total number of (Radio) owned?"
label variable h02_02   "What is the total number of (TV) owned?"
label variable h02_03   "What is the total number of (Mobile phone) owned?"
label variable h02_04   "What is the total number of (Land telephone) owned?"
label variable h02_05   "What is the total number of (Computer/ Laptop) owned?"
label variable h02_06   "What is the total number of (Bicycle) owned?"
label variable h02_07   "What is the total number of (Motorcycle) owned?"
label variable h02_08   "What is the total number of (Car/Van) owned?"
label variable h02_09   "What is the total number of (Truck/Lorry) owned?"
label variable h02_10   "What is the total number of (Bus) owned?"
label variable h02_11   "What is the total number of (Boat / Canoe) owned?"
label variable h02_12   "What is the total number of (Animal drawn cart) owned?"
label variable h03a_01  "How long ago the (Radio) 1 was obtained?"
label variable h03a_02  "How long ago the (TV) 1 was obtained?"
label variable h03a_03  "How long ago the (Mobile phone) 1 was obtained?"
label variable h03a_04  "How long ago the (Land telephone) 1 was obtained?"
label variable h03a_05  "How long ago the (Computer/ Laptop) 1 was obtained?"
label variable h03a_06  "How long ago the (Bicycle) 1 was obtained?"
label variable h03a_07  "How long ago the (Motorcycle) 1 was obtained?"
label variable h03a_08  "How long ago the (Car/Van) 1 was obtained?"
label variable h03a_09  "How long ago the (Truck/Lorry) 1 was obtained?"
label variable h03a_10  "How long ago the (Bus) 1 was obtained?"
label variable h03a_11  "How long ago the (Boat / Canoe) 1 was obtained?"
label variable h03a_12  "How long ago the (Animal drawn cart) 1 was obtained?"
label variable h03b_01  "How long ago the (Radio)2 was obtained?"
label variable h03b_02  "How long ago the (TV)2 was obtained?"
label variable h03b_03  "How long ago the (Mobile phone)2 was obtained?"
label variable h03b_04  "How long ago the (Land telephone)2 was obtained?"
label variable h03b_05  "How long ago the (Computer/ Laptop)2 was obtained?"
label variable h03b_06  "How long ago the (Bicycle)2 was obtained?"
label variable h03b_07  "How long ago the (Motorcycle)2 was obtained?"
label variable h03b_08  "How long ago the (Car/Van)2 was obtained?"
label variable h03b_09  "How long ago the (Truck/Lorry)2 was obtained?"
label variable h03b_10  "How long ago the (Bus)2 was obtained?"
label variable h03b_11  "How long ago the (Boat / Canoe)2 was obtained?"
label variable h03b_12  "How long ago the (Animal drawn cart)2 was obtained?"

label variable i01      "What is the household?s main source of income?"
label variable i01x     "Other"
label variable i02      "What is the household?s second main income source?"
label variable i02x     "Ohter"
label variable i03      "In the past 12 months, did the household receive help in the form of money (and "
label variable i04      "If yes, how many times?"
label variable i05      "In total, what was the amount received in the last 12 months?"
label variable i06      "In the past 12 months, has this household received or collected any aid (money a"
label variable i07      "If yes, what type of aid has the household received?"
label variable i07x     "Other"
label variable i08      "How frequently?"
label variable i08x     "Other"
label variable i09      "From which type of organization?"
label variable j01      "Does anyone in your household cultivate land?"
label variable j07      "Was any household member involved in catching or farming fish for sale or family"
label variable j07x     "List of member involved"
label variable j08a     "How many male household members"
label variable j08b     "How many female household members"
label variable j09      "Does anyone of your household own livestock?"

label variable j02a_1   "Type of land (Owned)"
label variable j02a_2   "Type of land (Rented)"
label variable j02a_3   "Type of land (Used for free)"
label variable j02_1    "How much land does the household cultivate? (Owned)"
label variable j02_2    "How much land does the household cultivate? (Rented)"
label variable j02_3    "How much land does the household cultivate? (Used for free)"
label variable j03_1    "If owned, by whom? (Owned)"
label variable j03_2    "If owned, by whom? (Rented)"
label variable j03_3    "If owned, by whom? (Used for free)"

label variable j04a_1   "Type of ecology (Rain fed, Low)"
label variable j04a_2   "Type of ecology (Rain fed, High)"
label variable j04a_3   "Type of ecology (Irrigated)"
label variable j04a_4   "Type of ecology (Pasture)"
label variable j04_1    "how many hectares (Rain fed, Low)"
label variable j04_2    "how many hectares (Rain fed, High)"
label variable j04_3    "how many hectares (Irrigated)"
label variable j04_4    "how many hectares (Pasture)"

label variable j05a_1   "Crops (Cereals)"
label variable j05a_2   "Crops (Groundnut)"
label variable j05a_3   "Crops (Vegetables)"
label variable j05a_4   "Crops (Fruit trees)"
label variable j05a_5   "Crops (Legumes)"
label variable j05a_6   "Crops (Tubers)"
label variable j05_1    "how many hectares of cultivated crops (Cereals)"
label variable j05_2    "how many hectares of cultivated crops (Groundnut)"
label variable j05_3    "how many hectares of cultivated crops (Vegetables)"
label variable j05_4    "how many hectares of cultivated crops (Fruit trees)"
label variable j05_5    "how many hectares of cultivated crops (Legumes)"
label variable j05_6    "how many hectares of cultivated crops (Tubers)"
label variable j06_1    "List of members responsibles for cultivation of (Cereals)"
label variable j06_2    "List of members responsibles for cultivation of (Groundnut)"
label variable j06_3    "List of members responsibles for cultivation of (Vegetables)"
label variable j06_4    "List of members responsibles for cultivation of (Fruit trees)"
label variable j06_5    "List of members responsibles for cultivation of (Legumes)"
label variable j06_6    "List of members responsibles for cultivation of (Tubers)"
label variable j06a_1   "Number of males responsibles for cultivation (Cereals)"
label variable j06a_2   "Number of males responsibles for cultivation (Groundnut)"
label variable j06a_3   "Number of males responsibles for cultivation (Vegetables)"
label variable j06a_4   "Number of males responsibles for cultivation (Fruit trees)"
label variable j06a_5   "Number of males responsibles for cultivation (Legumes)"
label variable j06a_6   "Number of males responsibles for cultivation (Tubers)"
label variable j06b_1   "Number of females responsibles for cultivation (Cereals)"
label variable j06b_2   "Number of females responsibles for cultivation (Groundnut)"
label variable j06b_3   "Number of females responsibles for cultivation (Vegetables)"
label variable j06b_4   "Number of females responsibles for cultivation (Fruit trees)"
label variable j06b_5   "Number of females responsibles for cultivation (Legumes)"
label variable j06b_6   "Number of females responsibles for cultivation (Tubers)"


label variable j10a_1   "Animals (Cattle)"
label variable j10a_2   "Animals (Sheep)"
label variable j10a_3   "Animals (Goats)"
label variable j10a_4   "Animals (Pigs)"
label variable j10a_5   "Animals (Chicken)"
label variable j10a_6   "Animals (Ducks)"
label variable j10a_7   "Animals (Horses)"
label variable j10a_8   "Animals (Donkeys)"
label variable j10a_9   "Animals (Other animals)"
label variable j10_1    "How many (Cattle) does the household own?"
label variable j10_2    "How many (Sheep) does the household own?"
label variable j10_3    "How many (Goats) does the household own?"
label variable j10_4    "How many (Pigs) does the household own?"
label variable j10_5    "How many (Chicken) does the household own?"
label variable j10_6    "How many (Ducks) does the household own?"
label variable j10_7    "How many (Horses) does the household own?"
label variable j10_8    "How many (Donkeys) does the household own?"
label variable j10_9    "How many (Other animals) does the household own?"
label variable j10x_1   "Other"
label variable j10x_2   "Other"
label variable j10x_3   "Other"
label variable j10x_4   "Other"
label variable j10x_5   "Other"
label variable j10x_6   "Other"
label variable j10x_7   "Other"
label variable j10x_8   "Other"
label variable j10x_9   "Other animal"
label variable j11_1    "Who is responsible for breeding the (Cattle)?"
label variable j11_2    "Who is responsible for breeding the (Sheep)?"
label variable j11_3    "Who is responsible for breeding the (Goats)?"
label variable j11_4    "Who is responsible for breeding the (Pigs)?"
label variable j11_5    "Who is responsible for breeding the (Chicken)?"
label variable j11_6    "Who is responsible for breeding the (Ducks)?"
label variable j11_7    "Who is responsible for breeding the (Horses)?"
label variable j11_8    "Who is responsible for breeding the (Donkeys)?"
label variable j11_9    "Who is responsible for breeding the (Other animals)?"



label variable k01      "In the last 12 months, have the household?s livelihood activities been affected "

label variable k02a_1   "livelihood (Crops)"
label variable k02a_2   "livelihood (Livestock)"
label variable k02a_3   "livelihood (Labour/employment)"
label variable k02a_4   "livelihood (Other livelihood)"
label variable k02b_1   "livelihood was affected? (Crops)"
label variable k02b_2   "livelihood was affected? (Livestock)"
label variable k02b_3   "livelihood was affected? (Labour/employment)"
label variable k02b_4   "livelihood was affected? (Other livelihood)"
label variable k02ax_1  "Other livelihood (Crops)"
label variable k02ax_2  "Other livelihood (Livestock)"
label variable k02ax_3  "Other livelihood (Labour/employment)"
label variable k02ax_4  "Other livelihood (Other livelihood)"
label variable k03_1    "What type of shock affected  (Crops)?"
label variable k03_2    "What type of shock affected  (Livestock)?"
label variable k03_3    "What type of shock affected  (Labour/employment)?"
label variable k03_4    "What type of shock affected  (Other livelihood)?"
label variable k03x_1   "Other shock (Crops)"
label variable k03x_2   "Other shock (Livestock)"
label variable k03x_3   "Other shock (Labour/employment)"
label variable k03x_4   "Other shock (Other livelihood)"
label variable k04_1    "severity of the losses caused by the shocks in (Crops)"
label variable k04_2    "severity of the losses caused by the shocks in (Livestock)"
label variable k04_3    "severity of the losses caused by the shocks in (Labour/employment)"
label variable k04_4    "severity of the losses caused by the shocks in (Other livelihood)"


label variable l01_1    "last 12 months,in need of resorting to (Engage in casual labor)"
label variable l01_2    "last 12 months,in need of resorting to (Sell property/assets (including livestock))"
label variable l01_3    "last 12 months,in need of resorting to (Borrow money)"
label variable l01_4    "last 12 months,in need of resorting to (Seek assistance from friends, community and relatives)"
label variable l01_5    "last 12 months,in need of resorting to (Seek assistance from relief agencies)"
label variable l01_6    "last 12 months,in need of resorting to (Rely on remittances)"
label variable l01_7    "last 12 months,in need of resorting to (Sand and gravel mining)"
label variable l01_8    "last 12 months,in need of resorting to (Relocate family)"
label variable l01_9    "last 12 months,in need of resorting to (Begging)"


label variable l02_1    "last 12 months, resort to (Consume less preferred or less expensive food)"
label variable l02_2    "last 12 months, resort to (Borrow food or money from a friend/relative/people from community)"
label variable l02_3    "last 12 months, resort to (Reduce the size of food portions)"
label variable l02_4    "last 12 months, resort to (Reduce the number of daily meals)"
label variable l02_5    "last 12 months, resort to (Restrict consumption of adults for children to eat)"
label variable l02_6    "last 12 months, resort to (Spend entire day(s) without eating)"
label variable l02_7    "last 12 months, resort to (Harvest wild food /hunting)"
label variable l02_8    "last 12 months, resort to (Merging households to eat together)"
label variable l02_9    "last 12 months, resort to (Begging)"


label variable badclosed "Other items"
label variable start_time "Start time"
label variable end_time "End time"

label variable census_longitude "Census Longitude"
label variable census_latitude "Census Latitude"
label variable distance_cens_survey "Distance between census and survey"
label variable interview_duration_in_minutes "Interview duration in minutes"
label variable xday     "start day"
label variable xmonth   "start month"
label variable xyear    "start year"
label variable xhour    "start hour"
label variable xminute  "start minute"
label variable xsecond  "start second"
label variable yday     "end day"
label variable ymonth   "end month"
label variable yyear    "end year"
label variable yhour    "end hour"
label variable yminute  "end minute"
label variable ysecond  "end second"

#delimit ;
label define A04     
     1 "Urban"
     2 "Rural"
;
label define A05     
     1 "Banjul"
     2 "Kanifing"
     3 "Brikama"
     4 "Mansakonko"
     5 "Kerewan"
     6 "Kuntaur"
     7 "Janjanbureh"
     8 "Basse"
;
label define A10GPS  
     1 "TAKE GPS NOW"
     2 "RETAKE GPS"
     3 "TAKE LATER"
;
label define A15     
     1 "COMPLETED"
     2 "NO HOUSEHOLD MEMBER AT HOME OR NO COMPETENT RESPONDENT AT HOME AT TIME OF VISIT"
     3 "ENTIRE HOUSEHOLD ABSENT FOR EXTENDED PERIOD OF TIME"
     4 "REFUSED"
     5 "POSTPONED"
     6 "DWELLING VACANT OR ADDRESS NOT A DWELLING"
     7 "DWELLING DESTROYED"
     8 "DWELLING NOT FOUND"
     9 "PARTIALLY COMPLETED"
    96 "OTHER"
;
label define A16     
     1 "YES"
     2 "NO / NOT ASKED"
;
label define F01     
     1 "Owner"
     2 "Renting"
     3 "Dwelling provided rent-free."
;
label define F02     
     0 "Natural walls"
     1 "No walls"
     2 "Cane/palm/trunks"
     0 "Rudimentary walls"
     3 "Bamboo with mud"
     4 "Stone with mood"
     5 "Uncovered adobe"
     6 "Plywood"
     7 "Cardboard"
     8 "Reused wood"
     9 "Mud/Mud bricks"
     0 "Finished walls"
    10 "Cement / Cement blocks"
    11 "Stone with lime / cement"
    96 "Other"
;
label define F03     
     0 "Natural roofing"
     1 "No roof"
     2 "Thatch/palm leaves"
     0 "Rudimentary roofing"
     3 "Palm/bamboo"
     4 "Wood planks"
     5 "Cardboard"
     0 "Finished roofing"
     6 "Metal/Tin"
     7 "Wood"
     8 "Calamine / Cement fiber"
     9 "Ceramic tiles"
    10 "Cement"
    11 "Decra"
    96 "Other"
;
label define F04     
     0 "Natural floor"
     1 "Earth/Sand"
     2 "Dung"
     0 "Rudimentary floor"
     3 "Wood planks"
     0 "Finished floor"
     4 "Parquet or polished floor"
     5 "Linoleum (tapeh)/ vinyl"
     6 "Ceramic tiles"
     7 "Cement/ Concrete"
     8 "Carpet"
    96 "Other"
;
label define F05     
     1 "Electricity (NAWEC)"
     2 "Electricity (Generator)"
     3 "Solar power"
     4 "Kerosene lamp with shade"
     5 "Other kerosene lamp"
     6 "Candles"
     7 "Battery powered light"
    96 "Other"
;
label define F06     
     1 "Firewood collected"
     2 "Firewood purchased"
     3 "Charcoal"
     4 "Gas"
     5 "Electricity"
     6 "Solar power"
     7 "Animal/ plant waste"
     8 "Does not cook"
    96 "Other"
;
label define F07     
     1 "Flush to piped sewer system"
     2 "Flush to septic tank"
     3 "Flush to pit latrine"
     4 "Ventilated improved pit latrine"
     5 "Pit latrine with slab"
     6 "Pit latrine without slab / Open pit"
     7 "No facility/Bush/ Open space"
    96 "Other"
;
label define F08     
     1 "Yes"
     2 "No"
;
label define F09     
    10 "Ten or more households"
    98 "DK"
;
label define F10     
     1 "Piped into dwelling"
     2 "Piped into compound"
     3 "Public stand pipe"
     4 "Protected well in compound"
     5 "Unprotected well in compound"
     6 "Well with pump (public)"
     7 "Well without pump (public)"
     8 "River, stream"
     9 "Bottled / sachet water"
    10 "Borehole"
	11 "Piped to neighbour"
    96 "Other"
;
label define F11     
     1 "Landfill / Bury"
     2 "Burn"
     3 "Use as compost"
     4 "Recycle"
     5 "Collected by municipality (household provides containers)"
     6 "Collected by municipality (municipality provides containers)"
     7 "Collected by private body"
     8 "Set Setal (community cleaning days)"
     9 "Public dump (authorized)"
    10 "In the bush or open space"
    11 "Collected by carts"
    96 "Other"
;
label define G00     
     1 "Drinking water supply"
     2 "Market/Lumo"
     3 "Pre-school"
     4 "Primary school."
     5 "Secondary school"
     6 "Health center / Dispensary"
     7 "Post office"
     8 "Police post"
     9 "All seasons road"
    10 "Fire and Rescue services"
    11 "Hospital"
    12 "Financial services"
    13 "Tertiary (including TVET)"
;
label define G03     
     1 "Vehicle"
     2 "Motorcycle"
     3 "Bicycle"
     4 "Foot"
     5 "Animal cart"
     6 "Boat"
    96 "Other"
;
label define H00     
     1 "Radio"
     2 "TV"
     3 "Mobile phone"
     4 "Land telephone"
     5 "Computer/ Laptop"
     6 "Bicycle"
     7 "Motorcycle"
     8 "Car/Van"
     9 "Truck/Lorry"
    10 "Bus"
    11 "Boat / Canoe"
    12 "Animal drawn cart"
;
label define H01     
     1 "Yes"
     2 "No"
;
label define I01     
     1 "Sale of food crops production (including garden produce)"
     2 "Sale of cash crops (e.g. groundnuts)"
     3 "Sale of animals/ livestock, animal produce"
     4 "Fishing"
     5 "Forest"
     6 "Sand and gravel mining"
     7 "Agricultural wage labor (paid in kind)"
     8 "Agricultural hired labor"
     9 "Non agriculture wage labor (e.g. construction workers)"
    10 "Self-employed services (e.g. taxi, carpenter, crafts)"
    11 "Self-employed shopkeepers, traders"
    12 "Self-employed street vendors"
    13 "Salaried employee ? NGO / private"
    14 "Salaried employee ? Public"
    15 "Business / entrepreneur"
    16 "Pensions / allowances"
    17 "Remittances"
    18 "Project/NGO support"
    19 "Begging"
    96 "Other"
;
label define I02     
     1 "Sale of food crops production (including garden produce)"
     2 "Sale of cash crops (e.g. groundnuts)"
     3 "Sale of animals/ livestock, animal produce"
     4 "Fishing"
     5 "Forest"
     6 "Sand and gravel mining"
     7 "Agricultural wage labor (paid in kind)"
     8 "Agricultural hired labor"
     9 "Non agriculture wage labor (e.g. construction workers)"
    10 "Self-employed services (e.g. taxi, carpenter, crafts)"
    11 "Self-employed shopkeepers, traders"
    12 "Self-employed street vendors"
    13 "Salaried employee ? NGO / private"
    14 "Salaried employee ? Public"
    15 "Business / entrepreneur"
    16 "Pensions / allowances"
    17 "Remittances"
    18 "Credit / Loan"
    19 "Project/NGO support"
    20 "Begging"
    21 "None"
    96 "Other"
;
label define I03     
     1 "Yes"
     2 "No"
;
label define I06     
     1 "Yes"
     2 "No"
;
label define I08     
     1 "Weekly"
     2 "Monthly"
     3 "Quarterly"
     4 "Bi-Annually"
     5 "Annually"
     6 "one-off"
     7 "Ad-hoc"
    96 "Other"
;
label define J01     
     1 "Yes"
     2 "No"
;
label define J07     
     1 "Yes"
     2 "No"
;
label define J09     
     1 "Yes"
     2 "No"
;
label define J02A    
     1 "Owned"
     2 "Rented"
     3 "Used for free"
;
label define J03     
     1 "Female members"
     2 "Male members"
     3 "Both"
;
label define J04A    
     1 "Rain fed, Low"
     2 "Rain fed, High"
     3 "Irrigated"
     4 "Pasture"
;
label define J05A    
     1 "Cereals"
     2 "Groundnut"
     3 "Vegetables"
     4 "Fruit trees"
     5 "Legumes"
     6 "Tubers"
;
label define J10A    
     1 "Cattle"
     2 "Sheep"
     3 "Goats"
     4 "Pigs"
     5 "Chicken"
     6 "Ducks"
     7 "Horses"
     8 "Donkeys"
     9 "Other animals"
;
label define K01     
     1 "Yes"
     2 "No"
;
label define K02A    
     1 "Crops"
     2 "Livestock"
     3 "Labour/employment"
     4 "Other livelihood"
;
label define K02B    
     1 "Yes"
     2 "No"
;
label define K04     
     1 "Very severe"
     2 "Severe"
     3 "Mild / moderate"
;
label define L01     
     1 "Yes, frequently"
     2 "Only during the dry period"
     3 "Occasionally"
     4 "Never"
;
label define L02     
     1 "Yes, Frequently"
     2 "Only during the lean period"
     3 "Occasionally"
     4 "Never"
;
label define BADCLOSED
     0 "Bad closed"
     1 "Well closed"
;

#delimit cr
label values a04      A04     
label values a05      A05     
label values a10gps   A10GPS  
label values a15      A15     
label values a16      A16     
label values f01      F01     
label values f02      F02     
label values f03      F03     
label values f04      F04     
label values f05      F05     
label values f06      F06     
label values f07      F07     
label values f08      F08     
label values f09      F09     
label values f10      F10     
label values f11      F11     
label values g00_01   G00     
label values g00_02   G00     
label values g00_03   G00     
label values g00_04   G00     
label values g00_05   G00     
label values g00_06   G00     
label values g00_07   G00     
label values g00_08   G00     
label values g00_09   G00     
label values g00_10   G00     
label values g00_11   G00     
label values g00_12   G00     
label values g00_13   G00     
label values g03_01   G03     
label values g03_02   G03     
label values g03_03   G03     
label values g03_04   G03     
label values g03_05   G03     
label values g03_06   G03     
label values g03_07   G03     
label values g03_08   G03     
label values g03_09   G03     
label values g03_10   G03     
label values g03_11   G03     
label values g03_12   G03     
label values g03_13   G03     
label values h00_01   H00     
label values h00_02   H00     
label values h00_03   H00     
label values h00_04   H00     
label values h00_05   H00     
label values h00_06   H00     
label values h00_07   H00     
label values h00_08   H00     
label values h00_09   H00     
label values h00_10   H00     
label values h00_11   H00     
label values h00_12   H00     
label values h01_01   H01     
label values h01_02   H01     
label values h01_03   H01     
label values h01_04   H01     
label values h01_05   H01     
label values h01_06   H01     
label values h01_07   H01     
label values h01_08   H01     
label values h01_09   H01     
label values h01_10   H01     
label values h01_11   H01     
label values h01_12   H01     
label values i01      I01     
label values i02      I02     
label values i03      I03     
label values i05      I05     
label values i06      I06     
label values i08      I08     
label values j01      J01     
label values j07      J07     
label values j09      J09     
label values j02a_1   J02A    
label values j02a_2   J02A    
label values j02a_3   J02A    
label values j03_1    J03     
label values j03_2    J03     
label values j03_3    J03     
label values j04a_1   J04A    
label values j04a_2   J04A    
label values j04a_3   J04A    
label values j04a_4   J04A    
label values j05a_1   J05A    
label values j05a_2   J05A    
label values j05a_3   J05A    
label values j05a_4   J05A    
label values j05a_5   J05A    
label values j05a_6   J05A    
label values j10a_1   J10A    
label values j10a_2   J10A    
label values j10a_3   J10A    
label values j10a_4   J10A    
label values j10a_5   J10A    
label values j10a_6   J10A    
label values j10a_7   J10A    
label values j10a_8   J10A    
label values j10a_9   J10A    
label values k01      K01     
label values k02a_1   K02A    
label values k02a_2   K02A    
label values k02a_3   K02A    
label values k02a_4   K02A    
label values k02b_1   K02B    
label values k02b_2   K02B    
label values k02b_3   K02B    
label values k02b_4   K02B    
label values k04_1    K04     
label values k04_2    K04     
label values k04_3    K04     
label values k04_4    K04     
label values l01_1    L01     
label values l01_2    L01     
label values l01_3    L01     
label values l01_4    L01     
label values l01_5    L01     
label values l01_6    L01     
label values l01_7    L01     
label values l01_8    L01     
label values l01_9    L01     
label values l02_1    L02     
label values l02_2    L02     
label values l02_3    L02     
label values l02_4    L02     
label values l02_5    L02     
label values l02_6    L02     
label values l02_7    L02     
label values l02_8    L02     
label values l02_9    L02     
label values badclosed BADCLOSED

drop j10x_1 j10x_2 j10x_3 j10x_4 j10x_5 j10x_6 j10x_7 j10x_8
