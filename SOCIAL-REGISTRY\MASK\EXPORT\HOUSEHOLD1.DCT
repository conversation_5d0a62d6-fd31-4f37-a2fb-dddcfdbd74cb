infix dictionary using "C:\GBOS\SOCIAL-REGISTRY\MASK\EXPORT\HOUSEHOLD1.CSDB" {
1 lines
    long     a08         1:   1-5   
    int      a10         1:   6-9   
    long     a01         1:  10-17  
    int      a02         1:  18-20  
    int      a03         1:  21-23  
    byte     a04         1:  24-24  
    byte     a05         1:  25-25  
    int      a06         1:  26-28  
    int      a07         1:  29-31  
    str      a09         1:  32-131 
    byte     a10gps      1: 132-132 
    double   a10a        1: 133-145 
    double   a10b        1: 146-158 
    str      a11         1: 159-208 
    long     a12         1: 209-217 
    long     a12b        1: 218-226 
    long     a12c        1: 227-235 
    str      a13         1: 236-385 
    str      a14         1: 386-435 
    byte     a15         1: 436-437 
    str      a15x        1: 438-587 
    byte     a16         1: 588-588 
    byte     hhsize      1: 589-590 
    byte     f01         1: 591-591 
    byte     f02         1: 592-593 
    str      f02x        1: 594-693 
    byte     f03         1: 694-695 
    str      f03x        1: 696-795 
    byte     f04         1: 796-797 
    str      f04x        1: 798-897 
    byte     f05         1: 898-899 
    str      f05x        1: 900-999 
    byte     f06         1:1000-1001
    str      f06x        1:1002-1101
    byte     f07         1:1102-1103
    str      f07x        1:1104-1203
    byte     f08         1:1204-1204
    byte     f09         1:1205-1206
    byte     f10         1:1207-1208
    str      f10x        1:1209-1308
    byte     f11         1:1309-1310
    str      f11x        1:1311-1410
    byte     g00_01      1:1411-1412
    byte     g00_02      1:1413-1414
    byte     g00_03      1:1415-1416
    byte     g00_04      1:1417-1418
    byte     g00_05      1:1419-1420
    byte     g00_06      1:1421-1422
    byte     g00_07      1:1423-1424
    byte     g00_08      1:1425-1426
    byte     g00_09      1:1427-1428
    byte     g00_10      1:1429-1430
    byte     g00_11      1:1431-1432
    byte     g00_12      1:1433-1434
    byte     g00_13      1:1435-1436
    float    g01_01      1:1437-1442
    float    g01_02      1:1443-1448
    float    g01_03      1:1449-1454
    float    g01_04      1:1455-1460
    float    g01_05      1:1461-1466
    float    g01_06      1:1467-1472
    float    g01_07      1:1473-1478
    float    g01_08      1:1479-1484
    float    g01_09      1:1485-1490
    float    g01_10      1:1491-1496
    float    g01_11      1:1497-1502
    float    g01_12      1:1503-1508
    float    g01_13      1:1509-1514
    int      g02_01      1:1515-1517
    int      g02_02      1:1518-1520
    int      g02_03      1:1521-1523
    int      g02_04      1:1524-1526
    int      g02_05      1:1527-1529
    int      g02_06      1:1530-1532
    int      g02_07      1:1533-1535
    int      g02_08      1:1536-1538
    int      g02_09      1:1539-1541
    int      g02_10      1:1542-1544
    int      g02_11      1:1545-1547
    int      g02_12      1:1548-1550
    int      g02_13      1:1551-1553
    byte     g03_01      1:1554-1555
    byte     g03_02      1:1556-1557
    byte     g03_03      1:1558-1559
    byte     g03_04      1:1560-1561
    byte     g03_05      1:1562-1563
    byte     g03_06      1:1564-1565
    byte     g03_07      1:1566-1567
    byte     g03_08      1:1568-1569
    byte     g03_09      1:1570-1571
    byte     g03_10      1:1572-1573
    byte     g03_11      1:1574-1575
    byte     g03_12      1:1576-1577
    byte     g03_13      1:1578-1579
    str      g03x_01     1:1580-1679
    str      g03x_02     1:1680-1779
    str      g03x_03     1:1780-1879
    str      g03x_04     1:1880-1979
    str      g03x_05     1:1980-2079
    str      g03x_06     1:2080-2179
    str      g03x_07     1:2180-2279
    str      g03x_08     1:2280-2379
    str      g03x_09     1:2380-2479
    str      g03x_10     1:2480-2579
    str      g03x_11     1:2580-2679
    str      g03x_12     1:2680-2779
    str      g03x_13     1:2780-2879
    byte     h00_01      1:2880-2881
    byte     h00_02      1:2882-2883
    byte     h00_03      1:2884-2885
    byte     h00_04      1:2886-2887
    byte     h00_05      1:2888-2889
    byte     h00_06      1:2890-2891
    byte     h00_07      1:2892-2893
    byte     h00_08      1:2894-2895
    byte     h00_09      1:2896-2897
    byte     h00_10      1:2898-2899
    byte     h00_11      1:2900-2901
    byte     h00_12      1:2902-2903
    byte     h01_01      1:2904-2904
    byte     h01_02      1:2905-2905
    byte     h01_03      1:2906-2906
    byte     h01_04      1:2907-2907
    byte     h01_05      1:2908-2908
    byte     h01_06      1:2909-2909
    byte     h01_07      1:2910-2910
    byte     h01_08      1:2911-2911
    byte     h01_09      1:2912-2912
    byte     h01_10      1:2913-2913
    byte     h01_11      1:2914-2914
    byte     h01_12      1:2915-2915
    byte     h02_01      1:2916-2917
    byte     h02_02      1:2918-2919
    byte     h02_03      1:2920-2921
    byte     h02_04      1:2922-2923
    byte     h02_05      1:2924-2925
    byte     h02_06      1:2926-2927
    byte     h02_07      1:2928-2929
    byte     h02_08      1:2930-2931
    byte     h02_09      1:2932-2933
    byte     h02_10      1:2934-2935
    byte     h02_11      1:2936-2937
    byte     h02_12      1:2938-2939
    int      h03a_01     1:2940-2942
    int      h03a_02     1:2943-2945
    int      h03a_03     1:2946-2948
    int      h03a_04     1:2949-2951
    int      h03a_05     1:2952-2954
    int      h03a_06     1:2955-2957
    int      h03a_07     1:2958-2960
    int      h03a_08     1:2961-2963
    int      h03a_09     1:2964-2966
    int      h03a_10     1:2967-2969
    int      h03a_11     1:2970-2972
    int      h03a_12     1:2973-2975
    int      h03b_01     1:2976-2978
    int      h03b_02     1:2979-2981
    int      h03b_03     1:2982-2984
    int      h03b_04     1:2985-2987
    int      h03b_05     1:2988-2990
    int      h03b_06     1:2991-2993
    int      h03b_07     1:2994-2996
    int      h03b_08     1:2997-2999
    int      h03b_09     1:3000-3002
    int      h03b_10     1:3003-3005
    int      h03b_11     1:3006-3008
    int      h03b_12     1:3009-3011
    byte     i01         1:3012-3013
    str      i01x        1:3014-3063
    byte     i02         1:3064-3065
    str      i02x        1:3066-3115
    byte     i03         1:3116-3116
    byte     i04         1:3117-3118
    double   i05         1:3119-3128
    byte     i06         1:3129-3129
    str      i07         1:3130-3141
    str      i07x        1:3142-3191
    byte     i08         1:3192-3193
    str      i08x        1:3194-3243
    str      i09         1:3244-3249
    byte     j01         1:3250-3250
    byte     j07         1:3251-3251
    str      j07x        1:3252-3351
    byte     j08a        1:3352-3353
    byte     j08b        1:3354-3355
    byte     j09         1:3356-3357
    byte     j02a_1      1:3358-3358
    byte     j02a_2      1:3359-3359
    byte     j02a_3      1:3360-3360
    float    j02_1       1:3361-3367
    float    j02_2       1:3368-3374
    float    j02_3       1:3375-3381
    byte     j03_1       1:3382-3382
    byte     j03_2       1:3383-3383
    byte     j03_3       1:3384-3384
    byte     j04a_1      1:3385-3385
    byte     j04a_2      1:3386-3386
    byte     j04a_3      1:3387-3387
    byte     j04a_4      1:3388-3388
    float    j04_1       1:3389-3395
    float    j04_2       1:3396-3402
    float    j04_3       1:3403-3409
    float    j04_4       1:3410-3416
    byte     j05a_1      1:3417-3417
    byte     j05a_2      1:3418-3418
    byte     j05a_3      1:3419-3419
    byte     j05a_4      1:3420-3420
    byte     j05a_5      1:3421-3421
    byte     j05a_6      1:3422-3422
    float    j05_1       1:3423-3429
    float    j05_2       1:3430-3436
    float    j05_3       1:3437-3443
    float    j05_4       1:3444-3450
    float    j05_5       1:3451-3457
    float    j05_6       1:3458-3464
    str      j06_1       1:3465-3564
    str      j06_2       1:3565-3664
    str      j06_3       1:3665-3764
    str      j06_4       1:3765-3864
    str      j06_5       1:3865-3964
    str      j06_6       1:3965-4064
    byte     j06a_1      1:4065-4066
    byte     j06a_2      1:4067-4068
    byte     j06a_3      1:4069-4070
    byte     j06a_4      1:4071-4072
    byte     j06a_5      1:4073-4074
    byte     j06a_6      1:4075-4076
    byte     j06b_1      1:4077-4078
    byte     j06b_2      1:4079-4080
    byte     j06b_3      1:4081-4082
    byte     j06b_4      1:4083-4084
    byte     j06b_5      1:4085-4086
    byte     j06b_6      1:4087-4088
    byte     j10a_1      1:4089-4090
    byte     j10a_2      1:4091-4092
    byte     j10a_3      1:4093-4094
    byte     j10a_4      1:4095-4096
    byte     j10a_5      1:4097-4098
    byte     j10a_6      1:4099-4100
    byte     j10a_7      1:4101-4102
    byte     j10a_8      1:4103-4104
    byte     j10a_9      1:4105-4106
    long     j10_1       1:4107-4111
    long     j10_2       1:4112-4116
    long     j10_3       1:4117-4121
    long     j10_4       1:4122-4126
    long     j10_5       1:4127-4131
    long     j10_6       1:4132-4136
    long     j10_7       1:4137-4141
    long     j10_8       1:4142-4146
    long     j10_9       1:4147-4151
    str      j10x_1      1:4152-4201
    str      j10x_2      1:4202-4251
    str      j10x_3      1:4252-4301
    str      j10x_4      1:4302-4351
    str      j10x_5      1:4352-4401
    str      j10x_6      1:4402-4451
    str      j10x_7      1:4452-4501
    str      j10x_8      1:4502-4551
    str      j10x_9      1:4552-4601
    str      j11_1       1:4602-4604
    str      j11_2       1:4605-4607
    str      j11_3       1:4608-4610
    str      j11_4       1:4611-4613
    str      j11_5       1:4614-4616
    str      j11_6       1:4617-4619
    str      j11_7       1:4620-4622
    str      j11_8       1:4623-4625
    str      j11_9       1:4626-4628
    byte     k01         1:4629-4629
    byte     k02a_1      1:4630-4630
    byte     k02a_2      1:4631-4631
    byte     k02a_3      1:4632-4632
    byte     k02a_4      1:4633-4633
    byte     k02b_1      1:4634-4634
    byte     k02b_2      1:4635-4635
    byte     k02b_3      1:4636-4636
    byte     k02b_4      1:4637-4637
    str      k02ax_1     1:4638-4687
    str      k02ax_2     1:4688-4737
    str      k02ax_3     1:4738-4787
    str      k02ax_4     1:4788-4837
    str      k03_1       1:4838-4847
    str      k03_2       1:4848-4857
    str      k03_3       1:4858-4867
    str      k03_4       1:4868-4877
    str      k03x_1      1:4878-4927
    str      k03x_2      1:4928-4977
    str      k03x_3      1:4978-5027
    str      k03x_4      1:5028-5077
    byte     k04_1       1:5078-5078
    byte     k04_2       1:5079-5079
    byte     k04_3       1:5080-5080
    byte     k04_4       1:5081-5081
    byte     l01_1       1:5082-5082
    byte     l01_2       1:5083-5083
    byte     l01_3       1:5084-5084
    byte     l01_4       1:5085-5085
    byte     l01_5       1:5086-5086
    byte     l01_6       1:5087-5087
    byte     l01_7       1:5088-5088
    byte     l01_8       1:5089-5089
    byte     l01_9       1:5090-5090
    byte     l02_1       1:5091-5091
    byte     l02_2       1:5092-5092
    byte     l02_3       1:5093-5093
    byte     l02_4       1:5094-5094
    byte     l02_5       1:5095-5095
    byte     l02_6       1:5096-5096
    byte     l02_7       1:5097-5097
    byte     l02_8       1:5098-5098
    byte     l02_9       1:5099-5099
    byte     badclosed    1:5100-5100
    double   start_time    1:5101-5110
    double   end_time    1:5111-5120
    double   census_longitude    1:5121-5133
    double   census_latitude    1:5134-5146
    double   distance_cens_survey    1:5147-5156
    long     interview_duration_in_minutes    1:5157-5161
    byte     xday        1:5162-5163
    byte     xmonth      1:5164-5165
    int      xyear       1:5166-5169
    byte     xhour       1:5170-5171
    byte     xminute     1:5172-5173
    byte     xsecond     1:5174-5175
    byte     yday        1:5176-5177
    byte     ymonth      1:5178-5179
    int      yyear       1:5180-5183
    byte     yhour       1:5184-5185
    byte     yminute     1:5186-5187
    byte     ysecond     1:5188-5189
}
