infix dictionary using "C:\USERS\<USER>\DESKTOP\SR LATEST\GBOS\SOCIAL-REGISTRY\MASK\EXPORT\INDIVIDUAL1.CSDB" {
1 lines
    long     a08         1:   1-5   
    int      a10         1:   6-9   
    long     a01         1:  10-17  
    int      a02         1:  18-20  
    int      a03         1:  21-23  
    byte     a04         1:  24-24  
    byte     a05         1:  25-25  
    int      a06         1:  26-28  
    int      a07         1:  29-31  
    str      a09         1:  32-131 
    byte     a10gps      1: 132-132 
    double   a10a        1: 133-145 
    double   a10b        1: 146-158 
    str      a11         1: 159-208 
    long     a12         1: 209-217 
    long     a12b        1: 218-226 
    long     a12c        1: 227-235 
    str      a13         1: 236-385 
    str      a14         1: 386-435 
    byte     a15         1: 436-437 
    str      a15x        1: 438-587 
    byte     a16         1: 588-588 
    byte     hhsize      1: 589-590 
    byte     b01         1: 591-592 
    str      b02a        1: 593-672 
    str      b02b        1: 673-752 
    byte     b03         1: 753-753 
    byte     b04         1: 754-755 
    byte     b05         1: 756-757 
    int      b06         1: 758-761 
    byte     b07a        1: 762-763 
    byte     b07b        1: 764-765 
    int      b07         1: 766-769 
    byte     b08         1: 770-771 
    int      b09         1: 772-774 
    str      b10         1: 775-776 
    str      b11         1: 777-786 
    str      b11x        1: 787-836 
    byte     b11a        1: 837-837 
    str      b11b        1: 838-838 
    byte     b12         1: 839-839 
    byte     b12a        1: 840-840 
    byte     b13         1: 841-842 
    str      b13x        1: 843-892 
    byte     b14         1: 893-893 
    byte     b15a        1: 894-895 
    byte     b15b        1: 896-896 
    byte     b15c        1: 897-898 
    byte     b16a        1: 899-900 
    byte     b16b        1: 901-901 
    byte     b16c        1: 902-903 
    byte     more        1: 904-904 
    byte     b17         1: 905-905 
    str      b17a        1: 906-925 
    byte     b17b        1: 926-926 
    byte     b18         1: 927-927 
    str      b18a        1: 928-947 
    byte     b18b        1: 948-948 
    str      b12b        1: 949-968 
    byte     c04         1: 969-969 
    byte     c05a        1: 970-970 
    byte     c05b        1: 971-972 
    byte     c06         1: 973-973 
    byte     c07a        1: 974-974 
    byte     c07b        1: 975-976 
    byte     c08         1: 977-977 
    byte     c09         1: 978-979 
    str      c09x        1: 980-1079
    byte     d03         1:1080-1080
    str      d04         1:1081-1089
    str      d04x        1:1090-1189
    byte     d05a        1:1190-1190
    byte     d05b        1:1191-1191
    byte     d05c        1:1192-1192
    byte     d05d        1:1193-1193
    byte     d05e        1:1194-1194
    byte     d05f        1:1195-1195
    byte     e02         1:1196-1196
    byte     e03         1:1197-1197
    byte     e04         1:1198-1199
    byte     e05         1:1200-1201
    str      e05x        1:1202-1301
    byte     e06         1:1302-1303
    str      e06x        1:1304-1403
    byte     f01         1:1404-1404
    byte     f02         1:1405-1406
    str      f02x        1:1407-1506
    byte     f03         1:1507-1508
    str      f03x        1:1509-1608
    byte     f04         1:1609-1610
    str      f04x        1:1611-1710
    byte     f05         1:1711-1712
    str      f05x        1:1713-1812
    byte     f06         1:1813-1814
    str      f06x        1:1815-1914
    byte     f07         1:1915-1916
    str      f07x        1:1917-2016
    byte     f08         1:2017-2017
    byte     f09         1:2018-2019
    byte     f10         1:2020-2021
    str      f10x        1:2022-2121
    byte     f11         1:2122-2123
    str      f11x        1:2124-2223
    byte     g00_01      1:2224-2225
    byte     g00_02      1:2226-2227
    byte     g00_03      1:2228-2229
    byte     g00_04      1:2230-2231
    byte     g00_05      1:2232-2233
    byte     g00_06      1:2234-2235
    byte     g00_07      1:2236-2237
    byte     g00_08      1:2238-2239
    byte     g00_09      1:2240-2241
    byte     g00_10      1:2242-2243
    byte     g00_11      1:2244-2245
    byte     g00_12      1:2246-2247
    byte     g00_13      1:2248-2249
    float    g01_01      1:2250-2255
    float    g01_02      1:2256-2261
    float    g01_03      1:2262-2267
    float    g01_04      1:2268-2273
    float    g01_05      1:2274-2279
    float    g01_06      1:2280-2285
    float    g01_07      1:2286-2291
    float    g01_08      1:2292-2297
    float    g01_09      1:2298-2303
    float    g01_10      1:2304-2309
    float    g01_11      1:2310-2315
    float    g01_12      1:2316-2321
    float    g01_13      1:2322-2327
    int      g02_01      1:2328-2330
    int      g02_02      1:2331-2333
    int      g02_03      1:2334-2336
    int      g02_04      1:2337-2339
    int      g02_05      1:2340-2342
    int      g02_06      1:2343-2345
    int      g02_07      1:2346-2348
    int      g02_08      1:2349-2351
    int      g02_09      1:2352-2354
    int      g02_10      1:2355-2357
    int      g02_11      1:2358-2360
    int      g02_12      1:2361-2363
    int      g02_13      1:2364-2366
    byte     g03_01      1:2367-2368
    byte     g03_02      1:2369-2370
    byte     g03_03      1:2371-2372
    byte     g03_04      1:2373-2374
    byte     g03_05      1:2375-2376
    byte     g03_06      1:2377-2378
    byte     g03_07      1:2379-2380
    byte     g03_08      1:2381-2382
    byte     g03_09      1:2383-2384
    byte     g03_10      1:2385-2386
    byte     g03_11      1:2387-2388
    byte     g03_12      1:2389-2390
    byte     g03_13      1:2391-2392
    str      g03x_01     1:2393-2492
    str      g03x_02     1:2493-2592
    str      g03x_03     1:2593-2692
    str      g03x_04     1:2693-2792
    str      g03x_05     1:2793-2892
    str      g03x_06     1:2893-2992
    str      g03x_07     1:2993-3092
    str      g03x_08     1:3093-3192
    str      g03x_09     1:3193-3292
    str      g03x_10     1:3293-3392
    str      g03x_11     1:3393-3492
    str      g03x_12     1:3493-3592
    str      g03x_13     1:3593-3692
    byte     h00_01      1:3693-3694
    byte     h00_02      1:3695-3696
    byte     h00_03      1:3697-3698
    byte     h00_04      1:3699-3700
    byte     h00_05      1:3701-3702
    byte     h00_06      1:3703-3704
    byte     h00_07      1:3705-3706
    byte     h00_08      1:3707-3708
    byte     h00_09      1:3709-3710
    byte     h00_10      1:3711-3712
    byte     h00_11      1:3713-3714
    byte     h00_12      1:3715-3716
    byte     h01_01      1:3717-3717
    byte     h01_02      1:3718-3718
    byte     h01_03      1:3719-3719
    byte     h01_04      1:3720-3720
    byte     h01_05      1:3721-3721
    byte     h01_06      1:3722-3722
    byte     h01_07      1:3723-3723
    byte     h01_08      1:3724-3724
    byte     h01_09      1:3725-3725
    byte     h01_10      1:3726-3726
    byte     h01_11      1:3727-3727
    byte     h01_12      1:3728-3728
    byte     h02_01      1:3729-3730
    byte     h02_02      1:3731-3732
    byte     h02_03      1:3733-3734
    byte     h02_04      1:3735-3736
    byte     h02_05      1:3737-3738
    byte     h02_06      1:3739-3740
    byte     h02_07      1:3741-3742
    byte     h02_08      1:3743-3744
    byte     h02_09      1:3745-3746
    byte     h02_10      1:3747-3748
    byte     h02_11      1:3749-3750
    byte     h02_12      1:3751-3752
    int      h03a_01     1:3753-3755
    int      h03a_02     1:3756-3758
    int      h03a_03     1:3759-3761
    int      h03a_04     1:3762-3764
    int      h03a_05     1:3765-3767
    int      h03a_06     1:3768-3770
    int      h03a_07     1:3771-3773
    int      h03a_08     1:3774-3776
    int      h03a_09     1:3777-3779
    int      h03a_10     1:3780-3782
    int      h03a_11     1:3783-3785
    int      h03a_12     1:3786-3788
    int      h03b_01     1:3789-3791
    int      h03b_02     1:3792-3794
    int      h03b_03     1:3795-3797
    int      h03b_04     1:3798-3800
    int      h03b_05     1:3801-3803
    int      h03b_06     1:3804-3806
    int      h03b_07     1:3807-3809
    int      h03b_08     1:3810-3812
    int      h03b_09     1:3813-3815
    int      h03b_10     1:3816-3818
    int      h03b_11     1:3819-3821
    int      h03b_12     1:3822-3824
    byte     i01         1:3825-3826
    str      i01x        1:3827-3876
    byte     i02         1:3877-3878
    str      i02x        1:3879-3928
    byte     i03         1:3929-3929
    byte     i04         1:3930-3931
    double   i05         1:3932-3941
    byte     i06         1:3942-3942
    str      i07         1:3943-3954
    str      i07x        1:3955-4004
    byte     i08         1:4005-4006
    str      i08x        1:4007-4056
    str      i09         1:4057-4062
    byte     j01         1:4063-4063
    byte     j07         1:4064-4064
    str      j07x        1:4065-4164
    byte     j08a        1:4165-4166
    byte     j08b        1:4167-4168
    byte     j09         1:4169-4170
    byte     j02a_1      1:4171-4171
    byte     j02a_2      1:4172-4172
    byte     j02a_3      1:4173-4173
    float    j02_1       1:4174-4180
    float    j02_2       1:4181-4187
    float    j02_3       1:4188-4194
    byte     j03_1       1:4195-4195
    byte     j03_2       1:4196-4196
    byte     j03_3       1:4197-4197
    byte     j04a_1      1:4198-4198
    byte     j04a_2      1:4199-4199
    byte     j04a_3      1:4200-4200
    byte     j04a_4      1:4201-4201
    float    j04_1       1:4202-4208
    float    j04_2       1:4209-4215
    float    j04_3       1:4216-4222
    float    j04_4       1:4223-4229
    byte     j05a_1      1:4230-4230
    byte     j05a_2      1:4231-4231
    byte     j05a_3      1:4232-4232
    byte     j05a_4      1:4233-4233
    byte     j05a_5      1:4234-4234
    byte     j05a_6      1:4235-4235
    float    j05_1       1:4236-4242
    float    j05_2       1:4243-4249
    float    j05_3       1:4250-4256
    float    j05_4       1:4257-4263
    float    j05_5       1:4264-4270
    float    j05_6       1:4271-4277
    str      j06_1       1:4278-4377
    str      j06_2       1:4378-4477
    str      j06_3       1:4478-4577
    str      j06_4       1:4578-4677
    str      j06_5       1:4678-4777
    str      j06_6       1:4778-4877
    byte     j06a_1      1:4878-4879
    byte     j06a_2      1:4880-4881
    byte     j06a_3      1:4882-4883
    byte     j06a_4      1:4884-4885
    byte     j06a_5      1:4886-4887
    byte     j06a_6      1:4888-4889
    byte     j06b_1      1:4890-4891
    byte     j06b_2      1:4892-4893
    byte     j06b_3      1:4894-4895
    byte     j06b_4      1:4896-4897
    byte     j06b_5      1:4898-4899
    byte     j06b_6      1:4900-4901
    byte     j10a_1      1:4902-4903
    byte     j10a_2      1:4904-4905
    byte     j10a_3      1:4906-4907
    byte     j10a_4      1:4908-4909
    byte     j10a_5      1:4910-4911
    byte     j10a_6      1:4912-4913
    byte     j10a_7      1:4914-4915
    byte     j10a_8      1:4916-4917
    byte     j10a_9      1:4918-4919
    long     j10_1       1:4920-4924
    long     j10_2       1:4925-4929
    long     j10_3       1:4930-4934
    long     j10_4       1:4935-4939
    long     j10_5       1:4940-4944
    long     j10_6       1:4945-4949
    long     j10_7       1:4950-4954
    long     j10_8       1:4955-4959
    long     j10_9       1:4960-4964
    str      j10x_1      1:4965-5014
    str      j10x_2      1:5015-5064
    str      j10x_3      1:5065-5114
    str      j10x_4      1:5115-5164
    str      j10x_5      1:5165-5214
    str      j10x_6      1:5215-5264
    str      j10x_7      1:5265-5314
    str      j10x_8      1:5315-5364
    str      j10x_9      1:5365-5414
    str      j11_1       1:5415-5417
    str      j11_2       1:5418-5420
    str      j11_3       1:5421-5423
    str      j11_4       1:5424-5426
    str      j11_5       1:5427-5429
    str      j11_6       1:5430-5432
    str      j11_7       1:5433-5435
    str      j11_8       1:5436-5438
    str      j11_9       1:5439-5441
    byte     k01         1:5442-5442
    byte     k02a_1      1:5443-5443
    byte     k02a_2      1:5444-5444
    byte     k02a_3      1:5445-5445
    byte     k02a_4      1:5446-5446
    byte     k02b_1      1:5447-5447
    byte     k02b_2      1:5448-5448
    byte     k02b_3      1:5449-5449
    byte     k02b_4      1:5450-5450
    str      k02ax_1     1:5451-5500
    str      k02ax_2     1:5501-5550
    str      k02ax_3     1:5551-5600
    str      k02ax_4     1:5601-5650
    str      k03_1       1:5651-5660
    str      k03_2       1:5661-5670
    str      k03_3       1:5671-5680
    str      k03_4       1:5681-5690
    str      k03x_1      1:5691-5740
    str      k03x_2      1:5741-5790
    str      k03x_3      1:5791-5840
    str      k03x_4      1:5841-5890
    byte     k04_1       1:5891-5891
    byte     k04_2       1:5892-5892
    byte     k04_3       1:5893-5893
    byte     k04_4       1:5894-5894
    byte     l01_1       1:5895-5895
    byte     l01_2       1:5896-5896
    byte     l01_3       1:5897-5897
    byte     l01_4       1:5898-5898
    byte     l01_5       1:5899-5899
    byte     l01_6       1:5900-5900
    byte     l01_7       1:5901-5901
    byte     l01_8       1:5902-5902
    byte     l01_9       1:5903-5903
    byte     l02_1       1:5904-5904
    byte     l02_2       1:5905-5905
    byte     l02_3       1:5906-5906
    byte     l02_4       1:5907-5907
    byte     l02_5       1:5908-5908
    byte     l02_6       1:5909-5909
    byte     l02_7       1:5910-5910
    byte     l02_8       1:5911-5911
    byte     l02_9       1:5912-5912
    byte     badclosed    1:5913-5913
    double   start_time    1:5914-5923
    double   end_time    1:5924-5933
    double   census_longitude    1:5934-5946
    double   census_latitude    1:5947-5959
    double   distance_cens_survey    1:5960-5969
    long     interview_duration_in_minutes    1:5970-5974
    byte     xday        1:5975-5976
    byte     xmonth      1:5977-5978
    int      xyear       1:5979-5982
    byte     xhour       1:5983-5984
    byte     xminute     1:5985-5986
    byte     xsecond     1:5987-5988
    byte     yday        1:5989-5990
    byte     ymonth      1:5991-5992
    int      yyear       1:5993-5996
    byte     yhour       1:5997-5998
    byte     yminute     1:5999-6000
    byte     ysecond     1:6001-6002
}
