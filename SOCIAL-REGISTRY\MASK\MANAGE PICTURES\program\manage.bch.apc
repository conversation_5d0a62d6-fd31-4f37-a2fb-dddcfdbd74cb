﻿{Application 'MANAGE' logic file generated by CSPro}
PROC GLOBAL

PROC MANAGE_FF

preproc
	
	file tmpfile; list string ListEAcompleted;
	setfile(tmpfile,"../COMPLETED EA.dat");
	
	fileread(tmpfile,ListEAcompleted);
	close(tmpfile);
	
	string operator = prompt("Enter the 3 digits usercode that you want to manage",numeric);
	
	list string ListPictures;
	dirlist(ListPictures,"../INPUT/"+ operator + "/");
	
	numeric i;
	dircreate("../OUTPUT/"+ operator + "/");
	filedelete("../OUTPUT/"+ operator + "/*");

	do i = 1 while i<= ListPictures.length()
		string EA = path.getFileName(ListPictures(i))[2:5];
		if ListEAcompleted.seek(EA) = 0 then
			filecopy(ListPictures(i),"../OUTPUT/"+ operator + "/");
			errmsg(001,path.getFileName(ListPictures(i)));
		endif;
	enddo;
	
