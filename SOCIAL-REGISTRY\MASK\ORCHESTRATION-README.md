# Social Registry System Orchestration

## Overview

The Social Registry System Orchestration is a Python-based orchestration layer that enhances and automates the workflow of the Social Registry System. It provides command-line tools and utilities for managing the CSPro-based survey system, including data collection, validation, batch processing, and reporting.

## Features

- **Application Launch**: Easily launch CSPro applications (Menu, Data Entry, Sample Management)
- **Backup Management**: Create and manage backups of survey data
- **Reporting**: Generate reports on data collection progress and status
- **Batch Processing**: Automate batch operations like excluding completed enumeration areas
- **Data Export**: Simplify the export of data to various formats
- **Command-Line Interface**: Control the system through a simple command-line interface
- **Configuration Management**: Centralized configuration with environment-specific overrides

## Installation

### Prerequisites

- Python 3.6 or higher
- Windows operating system (required for CSPro)
- CSPro 7.6 or higher installed

### Setup

1. Run the setup script to install required dependencies:

```
python setup.py
```

This will:
- Check your Python version
- Install required packages
- Create environment-specific configuration
- Set up logging directory
- Create batch files for easy access

## Usage

### Command-Line Interface

The orchestration system provides a command-line interface for interacting with the Social Registry System:

```
python orchestration_cli.py [command] [options]
```

Available commands:

- `launch-menu` - Launch the menu system
- `launch-data-entry` - Launch the data entry application
- `launch-sample` - Launch the sample management application
- `backup` - Create a backup of the current data
- `report` - Generate a report of the current data
- `exclude-completed-eas` - Run batch to exclude completed enumeration areas
- `remove-eas` - Run batch to remove enumeration areas from sample
- `export-data` - Export data to specified format
- `help` - Show help message

Example:
```
python orchestration_cli.py report --report-name "Weekly Status"
```

### Batch Files

For convenience, the setup script creates several batch files for common operations:

- `launch_menu.bat` - Launch the menu system
- `launch_data_entry.bat` - Launch the data entry application
- `launch_sample.bat` - Launch the sample management application
- `create_backup.bat` - Create a backup of the data
- `generate_report.bat` - Generate a report of the data

### Programmatic Usage

You can also use the orchestration library programmatically in your own Python scripts:

```python
from orchestration import SocialRegistryOrchestrator

# Initialize the orchestrator
orchestrator = SocialRegistryOrchestrator()

# Create a backup
backup_path = orchestrator.create_backup()
print(f"Created backup at {backup_path}")

# Generate a report
report_path = orchestrator.generate_report("Data Summary")
print(f"Generated report at {report_path}")

# Launch the menu system
orchestrator.launch_menu()
```

## Configuration

The orchestration system uses a configuration framework that allows for environment-specific overrides:

1. **Base Configuration**: Defined in `orchestration_config.py`
2. **Environment Configuration**: Defined in `orchestration_config_env.py` (created during setup)
3. **Environment Variables**: Can override specific settings (e.g., `SRS_CONFIG_BACKUP_AUTOMATIC=true`)

Example environment configuration override:

```python
# orchestration_config_env.py
ENV_CONFIG = {
    "cspro_paths": [
        r"C:\Custom\Path\To\CSPro.exe",
    ],
    "backup": {
        "automatic": True,
        "interval_hours": 12,  # More frequent backups
    },
}
```

## Architecture

The orchestration system consists of the following components:

1. **Core Orchestrator** (`orchestration.py`): Main orchestration class
2. **Configuration** (`orchestration_config.py`): Configuration framework
3. **Utilities** (`orchestration_utils.py`): Helper functions and utilities
4. **Command-Line Interface** (`orchestration_cli.py`): CLI implementation
5. **Setup** (`setup.py`): Installation and setup script

## Extending the Orchestration

The orchestration system can be extended to add new functionality:

1. **Add New Commands**: Extend the CLI with new commands
2. **Add Utilities**: Add helper functions to `orchestration_utils.py`
3. **Enhance Reporting**: Modify report generation for custom reports
4. **Add Integration**: Integrate with other systems or tools

## Troubleshooting

### Common Issues

1. **CSPro not found**:
   - Verify CSPro is installed
   - Update the CSPro path in `orchestration_config_env.py`

2. **Missing Directories**:
   - Ensure all required directories exist in the Social Registry System
   - Verify the base directory is correct when initializing the orchestrator

3. **Permission Issues**:
   - Ensure you have appropriate permissions for file operations
   - Run the CLI as administrator if needed

### Logs

The orchestration system maintains logs that can help diagnose issues:

- `orchestration.log`: General orchestration log
- `orchestration_cli.log`: Command-line interface log

## Contact

For technical support or questions about the Social Registry System Orchestration, please contact:

- **System Administrator**: [Name]
- **Email**: [Email Address]
- **Phone**: [Phone Number]

---

© 2025 Social Registry System. All rights reserved.