# Social Registry System

## Overview
This system is built using CSPro (Census and Survey Processing System) and is specifically tailored for the "Enquête Nationale sur la Consommation" (National Consumption Survey) in Algeria.

The system enables the collection, processing, and analysis of detailed household data including:

- Demographic information and household composition
- Education status and history
- Health conditions and healthcare access
- Housing conditions and assets
- Employment and economic activities
- Income and expenditure patterns
- Agricultural activities and production
- Social assistance and transfers


## Directory Structure

The system is organized into the following main directories:

### `/BACKUP`
Contains backup files of the system and data, organized by date and time.

### `/bacth exclude EA`
Contains batch processing files (.bch) for excluding enumeration areas from the sample. These batch files help manage the survey sample by removing completed or ineligible areas.

### `/DATA`
Stores the collected data files in CSPro database format (.csdb). The naming convention for data files follows a pattern of region/district codes (e.g., M01010101.csdb).

### `/DICTS`
Contains the data dictionary files (.dcf) that define the structure of the data, including:
- SOCIAL.dcf - Main dictionary for the social registry data
- SAMPLE.dcf - Dictionary for sample management
- MENU.dcf - Dictionary for the menu system

### `/DOCUMENTS`
Stores documentation files related to the system.

### `/ENTRY`
Contains the data entry application files:
- SOCIAL.ent - Main data entry application
- SOCIAL.ent.apc - Application logic file
- SOCIAL.fmf - Form file defining the user interface
- MENU.ent - Menu system for navigation
- SAMPLE.ent - Sample management application

### `/EXPORT`
Directory for exported data files.

### `/MANAGE PICTURES`
Contains tools and files for managing pictures/images associated with the data.

### `/REF` and `/REF1`
Reference files and lookup tables used by the system.

### `/REPORTS`
Contains reporting templates and generated reports.

### `/WORK`
Working directory for temporary files.

### `/XLS2CS`
Tools for converting Excel files to CSPro format.

## Data Collection Process

The Social Registry System follows a structured data collection process:

1. **Sample Management**: The SAMPLE application manages the selection and assignment of households to interviewers.

2. **Data Entry**: The application is used to collect data through structured interviews. The application includes:
   - Identification section for household location and basic information
   - Household roster for collecting information on all household members
   - Specialized sections for education, health, housing, employment, etc.
   - GPS functionality for capturing household locations

3. **Data Validation**: The system implements comprehensive validation rules to ensure data quality, including:
   - Range checks for numeric values
   - Consistency checks across related questions
   - Skip patterns to control questionnaire flow based on responses

4. **Data Processing**: Batch files (.bch) are used for automated processing tasks such as:
   - Excluding completed enumeration areas
   - Data cleaning and validation
   - Generating derived variables

5. **Reporting**: The system can generate various reports for monitoring survey progress and analyzing results.

## File Formats and Naming Conventions

### Data Files (.csdb)
- CSPro database files containing the collected data
- Naming convention: M[Region][District][Ward][Settlement].csdb
  - Example: M01010101.csdb represents data from Region 01, District 01, Ward 01, Settlement 01

### Application Files
- .ent - CSPro entry application
- .ent.apc - Application logic file (contains the programming code)
- .fmf - Form file (defines the user interface)
- .dcf - Dictionary file (defines the data structure)
- .pff - Parameter file (contains runtime parameters)

### Batch Files (.bch)
- CSPro batch files for automated processing
- Associated files:
  - .bch.apc - Batch application logic
  - .bch.mgf - Message file
  - .lst - Listing file
  - .ord - Order file

## Setup and Installation Requirements

### System Requirements
- Windows operating system (Windows 7 or later)
- CSPro 7.7 or later
- Minimum 4GB RAM
- 10GB available disk space
- GPS-enabled device for location capture (optional)

### Installation Steps
1. Install CSPro from the [U.S. Census Bureau website](https://www.census.gov/data/software/cspro.html)
2. Copy the Social Registry System folder to your computer
3. Ensure all directories maintain their relative paths
4. Launch the system by opening ENTRY/MENU.ent in CSPro

## Usage Instructions

### Starting the System
1. Open CSPro
2. Navigate to the ENTRY directory
3. Open MENU.ent
4. Log in with your assigned credentials

### Data Collection
1. Select "New Interview" from the menu
2. Enter the household identification information
3. Complete each section of the questionnaire
4. Use the GPS functionality to capture the household location
5. Save the completed interview

### Data Management
1. Use the batch processes in the "bacth exclude EA" directory to manage completed areas
2. Back up data regularly using the backup functionality
3. Export data as needed for analysis

### Reporting
1. Access the REPORTS directory for available report templates
2. Generate reports based on the collected data
3. Export reports in HTML format for sharing

## Troubleshooting

### Common Issues
- **Missing GPS coordinates**: Ensure GPS is enabled on the device and try recapturing
- **Validation errors**: Review the error message and correct the data as indicated
- **Application crashes**: Ensure you have the correct version of CSPro installed

### Data Recovery
- Check the BACKUP directory for the latest backup
- Use the partial save functionality to recover incomplete interviews

© 2025 Social Registry System. All rights reserved.
