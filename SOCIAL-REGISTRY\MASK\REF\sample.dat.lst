﻿Application     C:\GBOS\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\GBOS\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\GBOS\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 12, 2025
Start Time      13:15:59
End Time        13:16:28


System messages:

  Number        Freq         Message Text                                                                               
  ------        ----         ------------                                                                               
   91176           <USER>         <GROUP> to 'advance to %s' - target-item is located backward in the flow {in %p}            


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\GBOS\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\GBOS\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\GBOS\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 12, 2025
Start Time      13:17:24
End Time        13:17:32



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\GBOS\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\GBOS\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\GBOS\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 12, 2025
Start Time      13:18:11
End Time        13:18:35



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      12:01:17
End Time        12:03:03


System messages:

  Number        Freq         Message Text                                                                               
  ------        ----         ------------                                                                               
   88889           <USER>         <GROUP>: Out of range! Please enter a valid value for %s%s                                 
   99990           1         Operator entered a value out of range                                                      


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      12:03:57
End Time        12:04:19



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRYM\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRYM\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRYM\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      12:14:40
End Time        12:15:01



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      16:35:35
End Time        16:35:45



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      16:38:22
End Time        16:38:51


System messages:

  Number        Freq         Message Text                                                                               
  ------        ----         ------------                                                                               
   88889           <USER>         <GROUP>: Out of range! Please enter a valid value for %s%s                                 
   99990           1         Operator entered a value out of range                                                      


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      16:39:20
End Time        16:39:40



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      16:41:14
End Time        16:41:25


***  has 1 message (0 E / 0 W / 0 U)
    A    1010 Application error: protected field XWARD is out of range - value is  NOTAPPL


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      16:42:07
End Time        16:42:25


System messages:

  Number        Freq         Message Text                                                                               
  ------        ----         ------------                                                                               
   91186           <USER>         <GROUP> to 'reenter %s' - target-item is located forward in the flow {in %p}                


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      20:16:44
End Time        20:17:25



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      22:39:33
End Time        22:39:40



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 13, 2025
Start Time      22:39:53
End Time        22:40:00



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:11:57
End Time        10:12:13



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:25:49
End Time        10:26:17


System messages:

  Number        Freq         Message Text                                                                               
  ------        ----         ------------                                                                               
   88889           <USER>         <GROUP>: Out of range! Please enter a valid value for %s%s                                 
   99990           1         Operator entered a value out of range                                                      


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:35:56
End Time        10:38:00



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:41:35
End Time        10:41:40



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:42:37
End Time        10:42:47



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:43:40
End Time        10:43:51



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:47:01
End Time        10:47:26



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:49:33
End Time        10:51:35



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:51:53
End Time        10:52:08



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:57:02
End Time        10:57:42



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      10:57:46
End Time        11:01:09



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      11:06:00
End Time        11:06:44



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      11:06:47
End Time        11:07:06



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      11:07:11
End Time        11:07:32


System messages:

  Number        Freq         Message Text                                                                               
  ------        ----         ------------                                                                               
   91174           <USER>         <GROUP> to 'advance to %s' - target-item occurrence exceeds owner' group defined            
                             maximum {in %p}                                                                            


CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
Application     C:\SOCIAL-REGISTRY\MASK\ENTRY\SAMPLE.ent
Type            ENTRY
Input Data      <<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\sample.dat
External        FOLLOW_DICT=<<Text File>> C:\SOCIAL-REGISTRY\MASK\REF\follow.dat
<File>          tempfile=

Date            May 14, 2025
Start Time      11:07:38
End Time        11:07:40



CSPro Executor Normal End
------------------------------------------------------------------------------------------------------------------------
