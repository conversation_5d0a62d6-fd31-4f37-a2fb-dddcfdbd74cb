﻿Excel=..\..\SR sample with sequential HH number.xlsx
InputDict=..\DICTS\SAMPLE_INFOS.dcf
OutputData=.\sampleinfo.csdb
StartingRow=2
CaseManagement=CreateNewFile
RunOnlyIfNewer=No

Mapping=INFO_SAMPLE_REC;@1;SR_sample
Mapping=INFO_SAMPLE_REC.XA08;@1
Mapping=XA10;@2
Mapping=COMP_SRN;@3
Mapping=SOURCE_NAM;@4
Mapping=SERIAL_NUM;@5
Mapping=CLUSTER_CO;@6
Mapping=LGA;@7
Mapping=DISTRICT;@8
Mapping=WARD;@9
Mapping=SCODE;@10
Mapping=AREA;@11
Mapping=LGA_NAME;@12
Mapping=DIST_NAME;@13
Mapping=WARD_NAME;@14
Mapping=SETTLEMENT;@15
Mapping=COMPOUND_A;@16
Mapping=NAME_COMP;@17
Mapping=TELNO_COMP;@18
Mapping=TELNO_CO_1;@19
Mapping=TELNO_CO_2;@20
Mapping=NAME_KABIL;@21
Mapping=COMPOUND_P;@22
Mapping=MAIN_USE_O;@23
Mapping=OTHER_COMP;@24
Mapping=NO_OF_STRU;@25
Mapping=TOT_HH_IN;@26
Mapping=GPS_TAKEN;@27
Mapping=INTERVIEW;@28
Mapping=INTERVIEW1;@29
Mapping=INTERVIE_1;@30
Mapping=INTERVIE_2;@31
Mapping=INTERVIE_3;@32
Mapping=INTERVIE_4;@33
Mapping=INTERVIE_5;@34
Mapping=INTERVIE_6;@35
Mapping=INTERVIE_7;@36
Mapping=INTERVIE_8;@37
Mapping=ROLE;@38
Mapping=TEAM;@39
Mapping=TEAM_NAME;@40
Mapping=INTERVIEWE;@41
Mapping=INTERVIE_9;@42
Mapping=SUPERVISOR;@43
Mapping=SUPERVIS_1;@44
Mapping=HHNO;@45
Mapping=GENDER;@46
Mapping=NAME_OF_HO;@47
Mapping=H_HHOLD_PR;@48
Mapping=NAME_TEMP;@49
Mapping=TOT_NUMB_P;@50
Mapping=CONTACT_NU;@51
Mapping=CLUSTER_SG;@52
Mapping=LAT;@53
Mapping=LONG;@54
Mapping=EA_SGMT;@55
Mapping=HH;@56
Mapping=TNEW;@57
