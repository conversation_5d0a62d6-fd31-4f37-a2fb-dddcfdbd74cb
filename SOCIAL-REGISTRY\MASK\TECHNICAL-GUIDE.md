# Social Registry System - Technical Guide

This technical guide provides detailed information about the architecture, implementation, and technical aspects of the Social Registry System. It is intended for developers, system administrators, and technical users who need to understand the system at a deeper level.

## System Architecture

The System is built using CSPro (Census and Survey Processing System), a software package developed by the U.S. Census Bureau for designing, collecting, and processing census and survey data. The system follows a modular architecture with the following components:

### Core Components

1. **Menu System (MENU.ent)**
   - Entry point for the application
   - Handles user authentication and navigation
   - Manages case selection and assignment

2. **Sample Management (SAMPLE.ent)**
   - Manages the survey sample
   - Handles household selection and assignment to interviewers
   - Tracks completion status of interviews

3. **Data Collection (SOCIAL.ent)**
   - Main data entry application
   - Implements the questionnaire forms and logic
   - Handles data validation and skip patterns

4. **Batch Processing (*.bch files)**
   - Automated data processing scripts
   - Handles data cleaning and validation
   - Manages sample exclusions and updates

### Data Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  MENU.ent   │────▶│ SAMPLE.ent  │────▶│ SOCIAL.ent  │
└─────────────┘     └─────────────┘     └─────────────┘
       │                   │                   │
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  MENU.dcf   │     │ SAMPLE.dcf  │     │ SOCIAL.dcf  │
└─────────────┘     └─────────────┘     └─────────────┘
                                               │
                                               │
                                               ▼
                                        ┌─────────────┐
                                        │  DATA/*.csdb│
                                        └─────────────┘
                                               │
                                               │
                                               ▼
                                        ┌─────────────┐
                                        │ Batch Files │
                                        └─────────────┘
                                               │
                                               │
                                               ▼
                                        ┌─────────────┐
                                        │   Reports   │
                                        └─────────────┘
```

## Technical Implementation Details

### Application Logic (SOCIAL.ent.apc)

The SOCIAL.ent.apc file contains the application logic for the data entry application. It is written in CSPro's procedural language and implements:

1. **Global Variables and Functions**
   - Defines variables used throughout the application
   - Implements utility functions for common operations
   - Sets up GPS functionality for location capture

2. **Form Procedures**
   - Implements the logic for each form section
   - Handles data validation and skip patterns
   - Manages dynamic content based on user responses

3. **Data Validation**
   - Range checks for numeric values
   - Consistency checks across related questions
   - Custom validation rules for specific fields

4. **GPS Integration**
   - Captures GPS coordinates for household location
   - Validates coordinates to ensure accuracy
   - Allows adjustment of coordinates if needed

### Data Dictionary (SOCIAL.dcf)

The SOCIAL.dcf file defines the data structure for the Social Registry System. It includes:

1. **Record Types**
   - SECTA: Identification panel
   - QHSEC01X: Household roster
   - QHSEC02: Food security scale
   - QHSEC03: Housing
   - QHSEC04: Household assets
   - QHSEC05: Education
   - QHSEC06: Health
   - QHSEC08A-E: Economic activities
   - QHSEC09A-H: Non-agricultural enterprises
   - QHSEC10A-G: Consumption and expenditure
   - QHSEC11A-G: Agricultural activities
   - QHSEC12A-E: Transfers and income
   - QHSEC13A: Savings and credit

2. **Item Definitions**
   - Field names, types, and lengths
   - Value sets for categorical variables
   - Validation rules and skip patterns

3. **Hierarchical Structure**
   - Defines the relationship between different record types
   - Implements one-to-many relationships (e.g., household to members)

### Form File (SOCIAL.fmf)

The SOCIAL.fmf file defines the user interface for the data entry application. It includes:

1. **Form Layout**
   - Screen positioning and sizing
   - Field arrangement and grouping
   - Visual elements and styling

2. **Field Properties**
   - Display properties for each field
   - Input masks and formatting
   - Help text and instructions

3. **Navigation**
   - Tab order and field focus
   - Section navigation and flow
   - Form-level controls

## Data Structure

### Household Identification

The system uses a hierarchical identification system for households:

- **Region (A05)**: Geographic region code
- **Wilaya (A06)**: Province/state code
- **Commune (A07)**: Municipality code
- **EA Number (A08)**: Enumeration Area number
- **Household Number (A10)**: Sequential number within the EA

These fields together form a unique identifier for each household in the system.

### Data Files

Data is stored in CSPro database files (.csdb) with the following naming convention:

```
M[Region][District][Ward][Settlement].csdb
```

For example, `M01010101.csdb` represents data from:
- Region: 01
- District: 01
- Ward: 01
- Settlement: 01

Each .csdb file contains the data for multiple households within a specific geographic area.

## Questionnaire Structure

The questionnaire is organized into the following main sections:

1. **Identification (SECTA_FORM)**
   - Household location and identification
   - Interview date and time
   - Interviewer information
   - GPS coordinates

2. **Household Roster (QHSEC01X_FORM)**
   - List of household members
   - Demographic information (age, sex, relationship)
   - Marital status

3. **Food Security (QHSEC02_FORM)**
   - Food security scale questions
   - Household food consumption patterns

4. **Housing (QHSEC03_FORM)**
   - Dwelling characteristics
   - Tenure status
   - Housing amenities and services

5. **Household Assets (QHSEC04_FORM)**
   - Ownership of durable goods
   - Vehicle ownership
   - Appliances and electronics

6. **Education (QHSEC05_FORM)**
   - Educational attainment
   - School attendance
   - Educational expenses

7. **Health (QHSEC06_FORM)**
   - Health status and conditions
   - Healthcare access and utilization
   - Health insurance coverage

8. **Economic Activities (QHSEC08A-E_FORM)**
   - Employment status
   - Occupation and industry
   - Income from employment

9. **Non-Agricultural Enterprises (QHSEC09A-H_FORM)**
   - Business ownership
   - Business characteristics
   - Revenue and expenses

10. **Consumption and Expenditure (QHSEC10A-G_FORM)**
    - Food consumption
    - Non-food expenditure
    - Durable goods purchases

11. **Agricultural Activities (QHSEC11A-G_FORM)**
    - Land ownership and use
    - Crop production
    - Livestock ownership

12. **Transfers and Income (QHSEC12A-E_FORM)**
    - Remittances
    - Social assistance
    - Other income sources

13. **Savings and Credit (QHSEC13A_FORM)**
    - Savings behavior
    - Credit access and use
    - Financial inclusion

## Batch Processing

The system includes batch processing capabilities for automated data management:

### RemoveCompletedEAsampleINFO.bch

This batch file removes completed enumeration areas from the sample. It:
1. Identifies EAs where all households have been completed
2. Updates the sample database to mark these EAs as completed
3. Generates a report of completed EAs

### RemoveEAfromSample.bch

This batch file allows manual removal of specific enumeration areas from the sample. It:
1. Takes a list of EA codes as input
2. Removes these EAs from the active sample
3. Updates the sample database accordingly

## GPS Functionality

The system includes GPS functionality for capturing household locations:

1. **Capture Process**
   - Activates the device's GPS
   - Captures initial coordinates
   - Displays a map for verification
   - Allows adjustment if needed

2. **Validation**
   - Ensures coordinates are within expected range
   - Validates distance from initial position
   - Prevents accidental incorrect locations

3. **Storage**
   - Stores latitude and longitude in decimal degrees
   - Associates coordinates with household ID
   - Includes timestamp for verification

## Data Validation Rules

The system implements comprehensive validation rules to ensure data quality:

1. **Field-Level Validation**
   - Range checks for numeric values
   - Pattern matching for formatted fields
   - Required field validation

2. **Cross-Field Validation**
   - Consistency checks between related fields
   - Logical relationship validation
   - Dependency validation

3. **Cross-Record Validation**
   - Household composition checks
   - Relationship consistency
   - Age and relationship validation

## Performance Considerations

When working with the Social Registry System, consider the following performance factors:

1. **Data Volume**
   - Large household surveys can generate significant data volume
   - Plan for adequate storage capacity
   - Consider data archiving strategies for completed surveys

2. **Processing Speed**
   - Complex validation rules can impact processing speed
   - Batch operations may require significant processing time
   - Schedule resource-intensive operations during off-peak hours

3. **Concurrent Users**
   - Multiple interviewers may access the system simultaneously
   - Ensure adequate server resources for concurrent access
   - Implement proper locking mechanisms to prevent data corruption

## Security Considerations

The Social Registry System handles sensitive household data and requires appropriate security measures:

1. **User Authentication**
   - User accounts with appropriate permissions
   - Password policies and management
   - Role-based access control

2. **Data Protection**
   - Encryption of sensitive data
   - Secure storage of personal information
   - Data anonymization for analysis

3. **Audit Trail**
   - Logging of user actions
   - Tracking of data modifications
   - System access monitoring

## Customization and Extension

The Social Registry System can be customized and extended to meet specific requirements:

1. **Questionnaire Modification**
   - Adding or removing questions
   - Modifying skip patterns
   - Updating validation rules

2. **New Modules**
   - Adding new questionnaire sections
   - Implementing additional functionality
   - Integrating with other systems

3. **Reporting Enhancements**
   - Creating custom reports
   - Implementing data visualization
   - Developing analytical dashboards

## Troubleshooting

### Common Technical Issues

1. **Application Crashes**
   - Check CSPro version compatibility
   - Verify memory and disk space availability
   - Review error logs in the application directory

2. **Data Corruption**
   - Use CSPro's data recovery tools
   - Restore from backup if available
   - Check for concurrent access issues

3. **GPS Failures**
   - Verify device GPS functionality
   - Check for environmental interference
   - Ensure proper GPS permissions

### Debugging Techniques

1. **CSPro Debug Mode**
   - Run the application in debug mode
   - Set breakpoints at problematic procedures
   - Inspect variable values during execution

2. **Log Analysis**
   - Review application log files
   - Check for error patterns
   - Identify recurring issues

3. **Test Cases**
   - Create test cases for specific scenarios
   - Validate against expected results
   - Isolate problematic conditions

## System Maintenance

Regular maintenance is essential for optimal system performance:

1. **Backup Procedures**
   - Regular data backups
   - Application configuration backups
   - Offsite storage of critical backups

2. **Database Optimization**
   - Regular compaction of .csdb files
   - Index optimization
   - Performance monitoring

3. **Version Management**
   - Track application versions
   - Document changes and updates
   - Maintain version compatibility

## References

1. [CSPro User's Guide](https://www.census.gov/data/software/cspro/documentation.html)
2. [CSPro Programming Reference](https://www.census.gov/data/software/cspro/documentation.html)
3. [Social Registry System Documentation](./ENTRY/SOCIAL.ent.apc.documentation.html)

---

© 2025 Social Registry System. All rights reserved.
