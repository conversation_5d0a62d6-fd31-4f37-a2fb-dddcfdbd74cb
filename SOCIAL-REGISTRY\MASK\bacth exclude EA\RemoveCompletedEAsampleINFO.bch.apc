﻿{Application 'REMOVECOMPLETEDEASAMPLEINFO' logic file generated by CSPro}
PROC GLOBAL

PROC SAMPLE_INFOS_FF

PROC INFO_SAMPLE_LEVEL
preproc

//this is the list of COMPLETED EA
if XA08 in 32140,32141,32142,32143,32144,32145,32146,32147,32148,32149,32150,32151,32152,32153,31217,31218,31219,31220,31221,31222,31223,31224,31225,31226,31227,31228,31230,31231,31232,31233,32206,32419,32420,32422,32423,32425,32426,32427,32428,32429,32430,32431,32432,32433,32434,32436,30106,30107,30108,30109,30110,30629,30610,30632,30633,30634,30230,30274,30401,32154,32156,32158,32159,32160,32162,32163,32164,30223,30224,30225,30233,30235,30236,30237,30241,30242,30243,32234,32235,32236,32227,32238,32219,32218,32217,30863,30867,30873,30874,33316,33317,33318,33319,33320,33321,33322,33323,33325,33121,33120,33118,33117,33116,33113,31212,31211,31210,30846,30845,30844,30843,32437,32438,32439,32440,32441,32442,32443,32447,32448,32449,32452,32453,32454,32455,33315,33314,33313,33312,33311,33310,33307,33306,33305,33103,33226,33227,33228,33229,33230,33231,33301,33302,33303,33304,33308,33309,33324,30238,30239,30240,32302,32303,32304,32305,32401,32402,32403,32404,32405,32406,32407,32408,32409,32410,32411,32418,32475,32476,32477,32478,32479,32480,32473,32474,32183,32347,30640,32182,32170,32171,32172,32173,32174,32175,32177,32179,32180,32168,32167,32169,32161,32157,30254,30253,30901,30904,30457,30456,30455,30448,30447,30446,30430,30429,30428,30427,30426,32377,32127,32129,32128,32126,32125,32124,32123,30458,32237,32239,32240,32242,32243,32245,32246,32248,32251,32252,32253,32254,32255,30301,30302,30303,30304,30391,30392,30393,30649,30650,30651,30652,30653,30659,30856,30857,30858,30862,30868,32155,32165,32166,32178,32181,30861,31443,31445,31447,31456,31468,31469,32101,32102,30120,30121,30122,30123,30124,30125,30217,30218,30219,30221,30421,30442,31112,31113,31117,31110,31125,33135,33136,33137,33218,33219,33220,33222,33224,33225,33222,33224,33225,32356,32355,32354,32214,32213,32212,32211,32210,32209,32208,32207,32204,32203,32202,33128,33130,33131,33132,33133,33134,30101,30102,30103,30104,30105,30142,30145,30636,30637,30638,30201,30202,30204,30207,30210,30214,30248,30255,30256,32215,32216,32458,32459,32460,32461,32462,32463,32464,32465,32468,32470,32310,32312,32313,32314,32315,32481,32316,32317,32318,32319,32320,32321,32322,32308,32309,32306,32301,32307,32311,32322,32344,32345,32346,32482,32483,32484,32485,30150,30149,30148,30131,30132,30134,30135,30136,30137,30138,30462,30460,30463,33111,33112,33114,33107,33108,33109,33110,33104,33105,33106,33201,33202,33204,33205,33206,33207,33208,33209,33210,33211,33214,33215,33216,30835,30111,30112,30113,30114,30453,30454,30116,30115,30932,30929,30925,30913,30912,30905,30481,30479,30478,31357,31358,31431,31434,31432,31433,31435,31438,31439,30279,30280,30281,30282,30283,30284,30285,30286,33127,33126,33125,33124,30853,30850,32233,32232,32231,32230,32229,32228,32226,32225,32224,32130,32131,32132,32133,32134,32135,32136,32137,32138,32139,32376 
// if XA08 in 32140
  THEN SKIP CASE;
endif;
  
