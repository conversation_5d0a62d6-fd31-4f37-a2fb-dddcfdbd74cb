﻿{Application 'REMOVEEAFROMSAMPLE' logic file generated by CS<PERSON>ro}
PROC GLOBAL
list EAtoRemove;

PROC SAMPLE_FF
preproc



PROC SAMPLE_QUES
preproc
if XCLUSTER in 30101,
30102,
30103,
30104,
30105,
30106,
30107,
30108,
30109,
30110,
30111,
30112,
30113,
30114,
30115,
30116,
30120,
30121,
30122,
30123,
30124,
30125,
30126,
30127,
30128,
30129,
30130,
30131,
30132,
30134,
30135,
30136,
30137,
30138,
30143,
30144,
30146,
30147,
30148,
30149,
30150,
30151,
30152,
30153,
30154,
30155,
30156,
30157,
30158,
30159,
30160,
30201,
30202,
30204,
30207,
30210,
30214,
30248,
30301,
30302,
30303,
30304,
30391,
30392,
30393,
30415,
30426,
30427,
30428,
30429,
30430,
30446,
30447,
30448,
30455,
30456,
30457,
30901,
30904,
30905,
30906,
30911,
30912,
30913,
30923,
30925,
30928,
30929,
30930,
30931,
30932,
31217,
31221,
31222,
31223,
31224,
31225,
31226,
31227,
31228,
31230,
31231,
31232,
31233,
31349,
31350,
31351,
31357,
31358,
31428,
31429,
31431,
31432,
31433,
31434,
31435,
31438,
31439,
31443,
31468,
31469,
31477,
32101,
32102,
32123,
32124,
32125,
32126,
32127,
32128,
32129,
32130,
32131,
32132,
32133,
32134,
32135,
32136,
32137,
32138,
32139,
32140,
32141,
32142,
32143,
32145,
32146,
32147,
32148,
32149,
32150,
32151,
32152,
32153,
32154,
32155,
32156,
32158,
32159,
32160,
32162,
32163,
32164,
32165,
32166,
32167,
32168,
32169,
32170,
32171,
32172,
32173,
32174,
32175,
32176,
32178,
32179,
32181,
32182,
32183,
32202,
32203,
32204,
32205,
32206,
32207,
32208,
32209,
32210,
32211,
32212,
32213,
32214,
32215,
32216,
32217,
32218,
32219,
32220,
32221,
32223,
32224,
32225,
32226,
32227,
32228,
32229,
32230,
32231,
32232,
32233,
32234,
32235,
32236,
32237,
32238,
32239,
32240,
32241,
32242,
32243,
32244,
32245,
32246,
32247,
32248,
32249,
32250,
32251,
32252,
32253,
32254,
32255,
32302,
32303,
32304,
32305,
32306,
32308,
32309,
32310,
32312,
32313,
32314,
32315,
32316,
32317,
32318,
32319,
32320,
32321,
32322,
32347,
32348,
32349,
32350,
32351,
32352,
32353,
32354,
32355,
32356,
32357,
32358,
32363,
32364,
32376,
32377,
32401,
32402,
32403,
32404,
32405,
32406,
32407,
32408,
32409,
32410,
32411,
32418,
32419,
32420,
32421,
32422,
32423,
32424,
32425,
32426,
32427,
32428,
32429,
32430,
32431,
32432,
32433,
32434,
32435,
32436,
32437,
32438,
32439,
32440,
32441,
32442,
32443,
32444,
32445,
32447,
32448,
32449,
32451,
32452,
32453,
32454,
32455,
32456,
32457,
32458,
32459,
32460,
32461,
32462,
32463,
32464,
32465,
32466,
32467,
32468,
32469,
32470,
32471,
32473,
32474,
32475,
32476,
32477,
32478,
32479,
32480,
32481,
33101,
33102,
33104,
33105,
33106,
33107,
33108,
33109,
33110,
33111,
33112,
33114,
33115,
33116,
33117,
33118,
33119,
33120,
33121,
33122,
33123,
33124,
33125,
33126,
33127,
33128,
33129,
33130,
33131,
33132,
33133,
33135,
33136,
33137,
33201,
33202,
33205,
33206,
33207,
33208,
33209,
33210,
33211,
33214,
33215,
33216,
33218,
33219,
33220,
33222,
33224,
33225,
33226,
33227,
33228,
33229,
33230,
33231,
33301,
33302,
33303,
33305,
33306,
33307,
33310,
33311,
33312,
33313,
33314,
33315,
33316,
33317,
33318,
33319,
33320,
33321,
33322,
33323,
33325
 then

	skip case;
endif;
PROC XCLUSTER


