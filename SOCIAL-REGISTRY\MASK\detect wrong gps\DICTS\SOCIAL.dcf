﻿[Dictionary]
Version=CSPro 7.7
Label=SOCIAL
Name=SOCIAL_DICT
RecordTypeStart=1
RecordTypeLen=1
Positions=Relative
ZeroFill=No
DecimalChar=Yes
SecurityOptions=725C045E6C289DD395523CD457DAAE577DBA001A3BDCAF3E9F72B2010A91F41F

[Level]
Label=SOCIAL Level
Name=SOCIAL_LEVEL

[IdItems]

[Item]
Label=EA number
Name=A08
Start=2
Len=5
DataType=Numeric
ZeroFill=Yes

[Item]
Label=Household number
Name=A10
Start=7
Len=4
DataType=Numeric
ZeroFill=Yes

[Record]
Label=IDENTIFICATION PANEL
Name=SECTA
RecordTypeValue='1'
RecordLen=378

[Item]
Label=Date of the interview
Name=A01
Start=11
Len=8
DataType=Numeric

[Item]
Label=Interviewer
Name=A02
Start=19
Len=3
DataType=Numeric

[Item]
Label=Supervisor
Name=A03
Start=22
Len=3
DataType=Numeric

[Item]
Label=Area
Name=A04
Start=25
Len=1
DataType=Numeric

[ValueSet]
Label=Area
Name=A04_VS1
Value=1;Urban
Value=2;Rural

[Item]
Label=LGA
Name=A05
Start=26
Len=1
DataType=Numeric

[ValueSet]
Label=LGA
Name=A05_VS1
Value=1;Banjul
Value=2;Kanifing
Value=3;Brikama
Value=4;Mansakonko
Value=5;Kerewan
Value=6;Kuntaur
Value=7;Janjanbureh
Value=8;Basse

[Item]
Label=District
Name=A06
Start=27
Len=3
DataType=Numeric

[Item]
Label=Ward
Name=A07
Start=30
Len=3
DataType=Numeric

[Item]
Label=Town/ Settlement
Name=A09
Start=33
Len=5
DataType=Numeric

[Item]
Label=Take GPS
Name=A10GPS
Start=38
Len=1
DataType=Numeric

[ValueSet]
Label=Take GPS
Name=A10GPS_VS1
Value=1;TAKE GPS NOW
Value=2;RETAKE GPS
Value=3;TAKE LATER

[Item]
Label=Longitude
Name=A10A
Start=39
Len=13
DataType=Numeric
Decimal=6
DecimalChar=Yes

[Item]
Label=Latitude
Name=A10B
Start=52
Len=13
DataType=Numeric
Decimal=6
DecimalChar=Yes

[Item]
Label=Full name of respondent
Name=A11
Start=65
Len=50
DataType=Alpha

[Item]
Label=Telephone number
Name=A12
Start=115
Len=9
DataType=Numeric

[Item]
Label=Address
Name=A13
Start=124
Len=150
DataType=Alpha

[Item]
Label=Full name of head of household
Name=A14
Start=274
Len=50
DataType=Alpha

[Item]
Label=Result of the household interview
Name=A15
Start=324
Len=2
DataType=Numeric

[ValueSet]
Label=Result of the household interview
Name=A15_VS1
Value=1;COMPLETED
Value=2;NO HOUSEHOLD MEMBER AT HOME OR NO COMPETENT RESPONDENT AT HOME AT TIME OF VISIT
Value=3;ENTIRE HOUSEHOLD ABSENT FOR EXTENDED PERIOD OF TIME
Value=4;REFUSED
Value=5;POSTPONED
Value=6;DWELLING VACANT OR ADDRESS NOT A DWELLING
Value=7;DWELLING DESTROYED
Value=8;DWELLING NOT FOUND
Value=9;PARTIALLY COMPLETED
Value=96;OTHER

[Item]
Label=Other result
Name=A15X
Start=326
Len=50
DataType=Alpha

[Item]
Label=Consent
Name=A16
Start=376
Len=1
DataType=Numeric

[ValueSet]
Label=Consent
Name=A16_VS1
Value=1;YES
Value=2;NO / NOT ASKED

[Item]
Label=House size
Name=HHSIZE
Start=377
Len=2
DataType=Numeric

[Record]
Label=DEMOGRAPHY
Name=SECTB
RecordTypeValue='2'
Required=No
MaxRecords=90
RecordLen=364

[Item]
Label=Line number
Name=B01
Start=11
Len=2
DataType=Numeric

[Item]
Label=First name
Name=B02A
Start=13
Len=80
DataType=Alpha

[Item]
Label=Last name
Name=B02B
Start=93
Len=80
DataType=Alpha

[Item]
Label=Sex
Name=B03
Start=173
Len=1
DataType=Numeric

[ValueSet]
Label=Sex
Name=B03_VS1
Value=1;Male
Value=2;Female

[Item]
Label=Relationship to the head of household
Name=B04
Start=174
Len=2
DataType=Numeric

[ValueSet]
Label=Relationship to the head of household
Name=B04_VS1
Value=1;Head
Value=2;Spouse or Partner
Value=3;Co-wife
Value=4;Son or Daughter
Value=5;Adopted/ Foster/ Stepchild
Value=6;Aunt/Uncle
Value=7;Niece or nephew
Value=8;Brother or Sister
Value=16;Brother or Sister in law
Value=9;Son or Daughter in law
Value=10;Grandchild
Value=11;Parent
Value=12;Step parent
Value=13;Parent in law
Value=14;Other relative
Value=15;Not related

[Item]
Label=Residence status
Name=B05
Start=176
Len=2
DataType=Numeric

[ValueSet]
Label=Residence status
Name=B05_VS1
Value=1;Resident & Present
Value=2;Resident & Absent
Value=3;Displaced (IDP)
Value=4;Refugee
Value=5;Returnee
Value=6;Repatriated

[Item]
Label=Date of arrival
Name=B06
Start=178
Len=4
DataType=Numeric

[Item]
Label=Day of Birth
Name=B07A
Start=182
Len=2
DataType=Numeric

[ValueSet]
Label=Day of Birth
Name=B07A_VS1
Value=1:31
Value=98;DK

[Item]
Label=Month of Birth
Name=B07B
Start=184
Len=2
DataType=Numeric

[ValueSet]
Label=Month of Birth
Name=B07B_VS1
Value=1;January
Value=2;February
Value=3;March
Value=4;April
Value=5;May
Value=6;June
Value=7;July
Value=8;August
Value=9;September
Value=10;October
Value=11;November
Value=12;December
Value=98;DK

[Item]
Label=Year of birth
Name=B07
Start=186
Len=4
DataType=Numeric

[ValueSet]
Label=Year of birth
Name=B07_VS1
Value=1900:2023

[Item]
Label=Age
Name=B08
Start=190
Len=2
DataType=Numeric

[ValueSet]
Label=Age
Name=B08_VS1
Value=1:94
Value=0;Less than one year old
Value=95;For anyone above 95

[Item]
Label=Place of Birth
Name=B09
Start=192
Len=3
DataType=Numeric

[ValueSet]
Label=Place of Birth
Name=B09_VS1
Value=10;Banjul South
Value=11;Banjul Central
Value=12;Banjul North
Value=20;Bakau
Value=21;Jeshwang
Value=22;Serrekunda Central
Value=23;Serrekunda East
Value=24;Serrekunda West
Value=30;Kombo North
Value=31;Kombo South
Value=32;Kombo Central
Value=33;Kombo East
Value=34;Foni Brefet
Value=35;Foni Bintang Karanai
Value=36;Foni Kansala
Value=37;Foni Bondali
Value=38;Foni Jarrol
Value=40;Kiang West
Value=41;Kiang Central
Value=42;Kiang East
Value=43;Jarra West
Value=44;Jarra central
Value=45;Jarra East
Value=50;Lower Niumi
Value=51;Upper Niumi
Value=52;Jokadu
Value=53;Lower Badibu
Value=54;Central Badibu
Value=55;Illiasa
Value=56;Sabach Sanjar
Value=60;Lower Saloum
Value=61;Upper Saloum
Value=62;Nianija
Value=63;Niani
Value=64;Sami
Value=70;Niamina Dankunku
Value=71;Niamina West
Value=72;Niamina East
Value=73;Lower Fuladu West
Value=74;Upper Fuladu West
Value=75;Janjanbureh
Value=80;Jimara
Value=81;Basse
Value=82;Tumana
Value=83;Kantora
Value=84;Wuli West
Value=85;Wuli East
Value=86;Sandu
Value=95;Born outside Gambia

[Item]
Label=Nationality
Name=B10
Start=195
Len=2
DataType=Alpha

[ValueSet]
Label=Nationality
Name=B10_VS1
Value='A ';Gambian
Value='B ';Senegalese
Value='C ';Guinean (Conakry)
Value='D ';Guinean (Bissau)
Value='E ';Malian
Value='F ';Sierra Leonean
Value='G ';Mauritanian
Value='H ';Ghanaian
Value='I ';Nigerian
Value='J ';Liberian
Value='K ';Other West African
Value='L ';Other African
Value='M ';Non-African

[Item]
Label=Identification documents(check all documents in possession)
Name=B11
Start=197
Len=10
DataType=Alpha

[ValueSet]
Label=Identification documents(check all documents in possession)
Name=B11_VS1
Value='A         ';No identity document
Value='B         ';National identity card
Value='C         ';Birth certificate
Value='D         ';Infant Welfare card
Value='E         ';Passport
Value='F         ';Voting card
Value='G         ';Alien card
Value='H         ';Residential permit
Value='I         ';Driving license
Value='K         ';National Insurance Card
Value='Z         ';Other

[Item]
Label=Other document
Name=B11X
Start=207
Len=50
DataType=Alpha

[Item]
Label=Take picture of id document
Name=B11A
Start=257
Len=1
DataType=Numeric

[ValueSet]
Label=Take picture of id document
Name=B11A_VS1
Value=1;Take picture
Value=2;No document to capture
Value=7;Refused

[Item]
Label=Document to capture
Name=B11B
Start=258
Len=1
DataType=Alpha

[ValueSet]
Label=Document to capture
Name=B11B_VS1
Value='A';No identity document
Value='B';National identity card
Value='C';Birth certificate
Value='D';Infant Welfare card
Value='E';Passport
Value='F';Voting card
Value='G';Alien card
Value='H';Residential permit
Value='I';Driving license
Value='K';National Insurance Card
Value='Z';Other

[Item]
Label=have a birth certificate
Name=B12
Start=259
Len=1
DataType=Numeric

[ValueSet]
Label=have a birth certificate
Name=B12_VS1
Value=1;Yes, seen
Value=2;Yes, not seen
Value=3;No

[Item]
Label=Take picture of birth certificate
Name=B12A
Start=260
Len=1
DataType=Numeric

[ValueSet]
Label=Take picture of birth certificate
Name=B12A_VS1
Value=1;Take picture
Value=7;Refused

[Item]
Label=Ethnicity
Name=B13
Start=261
Len=2
DataType=Numeric

[ValueSet]
Label=Ethnicity
Name=B13_VS1
Value=1;Mandinka/ Jahanka
Value=2;Fula/ TukuIur/ Lorobo
Value=3;Wollof
Value=4;Jola/ Karoninka
Value=5;Sarahule
Value=6;Serere
Value=7;Creole/ Aku Marabout
Value=8;Manjago
Value=9;Bambara
Value=96;Other

[Item]
Label=Other ethnicity
Name=B13X
Start=263
Len=50
DataType=Alpha

[Item]
Label=Marital status
Name=B14
Start=313
Len=1
DataType=Numeric

[ValueSet]
Label=Marital status
Name=B14_VS1
Value=1;Single
Value=2;Married (monogamous)
Value=3;Married (polygamous with 2 wives)
Value=4;Married (polygamous with 3 wives)
Value=5;Married (polygamous with 4 or more wives)
Value=6;Live Together/ Cohabiting
Value=7;Divorced
Value=8;Separated
Value=9;Widowed

[Item]
Label=father alive
Name=B15A
Start=314
Len=2
DataType=Numeric

[ValueSet]
Label=father alive
Name=B15A_VS1
Value=1;Yes
Value=2;No
Value=98;Don't know

[Item]
Label=father in the household
Name=B15B
Start=316
Len=1
DataType=Numeric

[ValueSet]
Label=father in the household
Name=B15B_VS1
Value=1;Yes
Value=2;No

[Item]
Label=father line number
Name=B15C
Start=317
Len=2
DataType=Numeric

[Item]
Label=mother alive
Name=B16A
Start=319
Len=2
DataType=Numeric

[ValueSet]
Label=mother alive
Name=B16A_VS1
Value=1;Yes
Value=2;No
Value=98;Don't know

[Item]
Label=mother in the household
Name=B16B
Start=321
Len=1
DataType=Numeric

[ValueSet]
Label=mother in the household
Name=B16B_VS1
Value=1;Yes
Value=2;No

[Item]
Label=mother line number
Name=B16C
Start=322
Len=2
DataType=Numeric

[Item]
Label=More members
Name=MORE
Start=324
Len=1
DataType=Numeric

[ValueSet]
Label=More members
Name=MORE_VS1
Value=1;Yes
Value=2;No

[Item]
Label=Does name have a National Insurance Card
Name=B17
Start=325
Len=1
DataType=Numeric

[ValueSet]
Label=Does name have a National Insurance Card
Name=B17_VS1
Value=1;Yes, seen
Value=2;Yes, not seen
Value=3;No

[Item]
Label=National Insurance Card number
Name=B17A
Start=326
Len=12
DataType=Numeric

[ValueSet]
Label=National Insurance Card number
Name=B17A_VS1
Value=0:999999999998
Value=999999999999;MISSING NUMBERS/NOT READABLE

[Item]
Label=Take a picture of the card
Name=B17B
Start=338
Len=1
DataType=Numeric

[ValueSet]
Label=Take a picture of the card
Name=B17B_VS1
Value=1;Take picture of card
Value=7;Refused

[Item]
Label=Does name have a National ID card
Name=B18
Start=339
Len=1
DataType=Numeric

[ValueSet]
Label=Does name have a National ID card
Name=B18_VS1
Value=1;Yes, seen
Value=2;Yes, not seen
Value=3;No

[Item]
Label=ID number of the National ID card
Name=B18A
Start=340
Len=12
DataType=Numeric

[ValueSet]
Label=ID number of the National ID card
Name=B18A_VS1
Value=0:999999999998
Value=999999999999;MISSING NUMBERS/NOT READABLE

[Item]
Label=Take a picture of the  National ID card
Name=B18B
Start=352
Len=1
DataType=Numeric

[ValueSet]
Label=Take a picture of the  National ID card
Name=B18B_VS1
Value=1;Take picture of card
Value=7;Refused

[Item]
Label=Birth certificate number
Name=B12B
Start=353
Len=12
DataType=Numeric

[ValueSet]
Label=Birth certificate number
Name=B12B_VS1
Value=0:999999999998
Value=999999999999;MISSING NUMBERS/NOT READABLE

[Record]
Label=EDUCATION
Name=SECTC
RecordTypeValue='3'
Required=No
MaxRecords=90
RecordLen=123

[Item]
Label=Line number
Name=C01
Start=11
Len=2
DataType=Numeric

[Item]
Label=Has [NAME] ever been to school?
Name=C04
Start=13
Len=1
DataType=Numeric

[ValueSet]
Label=Has [NAME] ever been to school?
Name=C04_VS1
Value=1;Yes, currently attending
Value=2;Yes, but in the past
Value=3;Never

[Item]
Label=Which level Iis [NAME] attending ?
Name=C05A
Start=14
Len=1
DataType=Numeric

[ValueSet]
Label=Which level Iis [NAME] attending ?
Name=C05A_VS1
Value=1;Early childhood
Value=2;Primary
Value=3;Lower secondary
Value=4;Upper secondary
Value=5;Vocational
Value=6;Diploma
Value=7;Higher education

[Item]
Label=Which grade is [NAME] attending ?
Name=C05B
Start=15
Len=2
DataType=Numeric

[ValueSet]
Label=Which grade is [NAME] attending ?
Name=C05B_VS1
Value=1;1
Value=2;2
Value=3;3
Value=4;4
Value=5;5
Value=6;6
Value=7;7
Value=8;8
Value=9;9
Value=10;10
Value=11;11
Value=12;12

[Item]
Label=What type of school has [NAME] attended?
Name=C06
Start=17
Len=1
DataType=Numeric

[ValueSet]
Label=What type of school has [NAME] attended?
Name=C06_VS1
Value=1;Private
Value=2;Public

[Item]
Label=What was the previous level that [NAME] attained?
Name=C07A
Start=18
Len=1
DataType=Numeric

[ValueSet]
Label=What was the previous level that [NAME] attained?
Name=C07A_VS1
Value=0;NONE
Value=1;Early childhood
Value=2;Primary
Value=3;Lower secondary
Value=4;Upper secondary
Value=5;Vocational
Value=6;Diploma
Value=7;Higher education

[Item]
Label=What was the previous grade that [NAME] attained?
Name=C07B
Start=19
Len=2
DataType=Numeric

[ValueSet]
Label=What was the previous grade that [NAME] attained?
Name=C07B_VS1
Value=1;1
Value=2;2
Value=3;3
Value=4;4
Value=5;5
Value=6;6
Value=7;7
Value=8;8
Value=9;9
Value=10;10
Value=11;11
Value=12;12

[Item]
Label=Can [NAME] read and/or write in any language
Name=C08
Start=21
Len=1
DataType=Numeric

[ValueSet]
Label=Can [NAME] read and/or write in any language
Name=C08_VS1
Value=1;Read and write
Value=2;Read only
Value=3;No

[Item]
Label=Why has [NAME] never been to school?
Name=C09
Start=22
Len=2
DataType=Numeric

[ValueSet]
Label=Why has [NAME] never been to school?
Name=C09_VS1
Value=1;Work
Value=2;Too expensive
Value=3;Too far
Value=4;Not useful
Value=5;Married
Value=6;Religious
Value=7;Too young
Value=8;Disabled
Value=9;Illness
Value=96;Other
Value=98;Don't know

[Item]
Label=other reason
Name=C09X
Start=24
Len=100
DataType=Alpha

[Record]
Label=HEALTH
Name=SECTD
RecordTypeValue='4'
Required=No
MaxRecords=90
RecordLen=128

[Item]
Label=Line number
Name=D01
Start=11
Len=2
DataType=Numeric

[Item]
Label=Does [NAME] suffer from any chronic illness?
Name=D03
Start=13
Len=1
DataType=Numeric

[ValueSet]
Label=Does [NAME] suffer from any chronic illness?
Name=D03_VS1
Value=1;Yes
Value=2;No
Value=3;Don’t know

[Item]
Label=From what type of chronic illness does [NAME] suffer?
Name=D04
Start=14
Len=9
DataType=Alpha

[ValueSet]
Label=From what type of chronic illness does [NAME] suffer?
Name=D04_VS1
Value='A        ';Tuberculosis
Value='B        ';Respiratory diseases
Value='C        ';Schistosomiasis
Value='D        ';HIV/Aids
Value='E        ';Diabetes
Value='F        ';Hypertension
Value='G        ';Cancer
Value='H        ';Hepatitis
Value='Z        ';Other

[Item]
Label=Other illness
Name=D04X
Start=23
Len=100
DataType=Alpha

[Item]
Label=Does [NAME] have difficulty seeing?
Name=D05A
Start=123
Len=1
DataType=Numeric

[ValueSet]
Label=Does [NAME] have difficulty seeing?
Name=D05A_VS1
Value=1;No, no difficulty
Value=2;Yes, some difficulty
Value=3;Yes, a lot of difficulty
Value=4;Cannot do at all

[Item]
Label=Does [NAME] have difficulty hearing?
Name=D05B
Start=124
Len=1
DataType=Numeric

[ValueSet]
Label=Does [NAME] have difficulty hearing?
Name=D05B_VS1
Value=1;No, no difficulty
Value=2;Yes, some difficulty
Value=3;Yes, a lot of difficulty
Value=4;Cannot do at all

[Item]
Label=Does [NAME] have difficulty walking?
Name=D05C
Start=125
Len=1
DataType=Numeric

[ValueSet]
Label=Does [NAME] have difficulty walking?
Name=D05C_VS1
Value=1;No, no difficulty
Value=2;Yes, some difficulty
Value=3;Yes, a lot of difficulty
Value=4;Cannot do at all

[Item]
Label=Does [NAME] have difficulty remembering?
Name=D05D
Start=126
Len=1
DataType=Numeric

[ValueSet]
Label=Does [NAME] have difficulty remembering?
Name=D05D_VS1
Value=1;No, no difficulty
Value=2;Yes, some difficulty
Value=3;Yes, a lot of difficulty
Value=4;Cannot do at all

[Item]
Label=Does [NAME] have difficulty with self-care?
Name=D05E
Start=127
Len=1
DataType=Numeric

[ValueSet]
Label=Does [NAME] have difficulty with self-care?
Name=D05E_VS1
Value=1;No, no difficulty
Value=2;Yes, some difficulty
Value=3;Yes, a lot of difficulty
Value=4;Cannot do at all

[Item]
Label=Does [NAME] have difficulty communicating?
Name=D05F
Start=128
Len=1
DataType=Numeric

[ValueSet]
Label=Does [NAME] have difficulty communicating?
Name=D05F_VS1
Value=1;No, no difficulty
Value=2;Yes, some difficulty
Value=3;Yes, a lot of difficulty
Value=4;Cannot do at all

[Record]
Label=EMPLOYMENT
Name=SECTE
RecordTypeValue='5'
Required=No
MaxRecords=90
RecordLen=120

[Item]
Label=Line number
Name=E01
Start=11
Len=2
DataType=Numeric

[Item]
Label=What has been [NAME]’s MAIN job/ activity in the last 30 days?
Name=E02
Start=13
Len=1
DataType=Numeric

[ValueSet]
Label=What has been [NAME]’s MAIN job/ activity in the last 30 days?
Name=E02_VS1
Value=1;As an employee for wage, salary, commission or in-kind payment (including paid domestic work, farm work or apprentice)
Value=2;On their own account on a farm owned or rented by a household member (including cultivating crops, performing maintenance tasks or caring for livestock)
Value=3;On their own account or in a business enterprise belonging to a household member (e.g. as a taxi driver, carpenter, shop keeper, street seller, etc.)
Value=4;Unpaid work
Value=5;No work

[Item]
Label=If [NAME] has been working, How frequently?
Name=E03
Start=14
Len=1
DataType=Numeric

[ValueSet]
Label=If [NAME] has been working, How frequently?
Name=E03_VS1
Value=1;Full time – permanent work
Value=2;Part time – permanent work
Value=3;Full time – seasonal work
Value=4;Part time – seasonal work
Value=5;Full time – temporal work
Value=6;Occasionally (less than a week of work per month)

[Item]
Label=If [NAME] has been working, in which (main) sector?
Name=E04
Start=15
Len=2
DataType=Numeric

[ValueSet]
Label=If [NAME] has been working, in which (main) sector?
Name=E04_VS1
Value=1;Agriculture, forestry and fishing
Value=2;Mining and quarrying
Value=3;Manufacturing
Value=4;Electricity, gas, steam and air conditioning supply
Value=5;Water supply; sewerage, waste management and remediation activities
Value=6;Construction
Value=7;Wholesale and retail trade; repair of motor vehicles and motorcycles
Value=8;Transportation and storage
Value=9;Accommodation and food service activities
Value=10;Information and communication
Value=11;Financial and insurance activities
Value=12;Real estate activities
Value=13;Professional, scientific and technical activities
Value=14;Administrative and support service activities
Value=15;Public administration and defense; compulsory social security
Value=16;Education
Value=17;Human health and social work activities
Value=18;Arts, entertainment and recreation
Value=19;Other service activities
Value=20;Activities of households as employers; undifferentiated goods- and services-producing activities of households for own use
Value=21;Activities of extraterritorial organizations and bodies

[Item]
Label=If [NAME] has been working, which is his status?
Name=E05
Start=17
Len=2
DataType=Numeric

[ValueSet]
Label=If [NAME] has been working, which is his status?
Name=E05_VS1
Value=1;Employers in corporations
Value=2;Employers in household market enterprises
Value=3;Owner-operators of corporations without employees
Value=4;Own-account workers in household market enterprises without employee
Value=5;Dependent contractors
Value=6;Permanent employees
Value=7;Fixed-term employees
Value=8;Short-term and casual employees
Value=9;Paid apprentices, trainees and interns
Value=10;Contributing family workers

[Item]
Label=If  [NAME] has not been working, why?
Name=E06
Start=19
Len=2
DataType=Numeric

[ValueSet]
Label=If  [NAME] has not been working, why?
Name=E06_VS1
Value=1;Found work but waiting to start
Value=2;Awaiting replies to earlier enquiries
Value=3;Awaiting for the season to start
Value=4; attended school/training courses
Value=5;Family responsibilities or housework
Value=6;Illness, injury or disability
Value=7;Too young/old to find work
Value=8;Does not know where to look for work
Value=9;Lacks employers’ requirements (skills, experience, qualifications)
Value=10;No jobs available in the area
Value=11;Retired, pensioner, other sources of income
Value=96;Other reasons (specify)

[Item]
Label=Other reason
Name=E06X
Start=21
Len=100
DataType=Alpha

[Record]
Label=HOUSING
Name=SECTF
RecordTypeValue='6'
Required=No
RecordLen=830

[Item]
Label=What is the occupancy status of the dwelling?
Name=F01
Start=11
Len=1
DataType=Numeric

[ValueSet]
Label=What is the occupancy status of the dwelling?
Name=F01_VS1
Value=1;Owner
Value=2;Renting
Value=3;Dwelling provided rent-free.

[Item]
Label=What is the main construction material of the exterior wall of the main dwelling?
Name=F02
Start=12
Len=2
DataType=Numeric

[ValueSet]
Label=What is the main construction material of the exterior wall of the main dwelling?
Name=F02_VS1
Value=0;Natural walls
TextColor=Blue
Value=1;No walls
Value=2;Cane/palm/trunks
Value=0;Rudimentary walls
TextColor=Blue
Value=3;Bamboo with mud
Value=4;Stone with mood
Value=5;Uncovered adobe
Value=6;Plywood
Value=7;Cardboard
Value=8;Reused wood
Value=9;Mud/Mud bricks
Value=0;Finished walls
TextColor=Blue
Value=10;Cement / Cement blocks
Value=11;Stone with lime / cement
Value=96;Other

[Item]
Label=Other
Name=F02X
Start=14
Len=100
DataType=Alpha

[Item]
Label=What is the main material used for roofing of the main dwelling?
Name=F03
Start=114
Len=2
DataType=Numeric

[ValueSet]
Label=What is the main material used for roofing of the main dwelling?
Name=F03_VS1
Value=0;Natural roofing
TextColor=Blue
Value=1;No roof
Value=2;Thatch/palm leaves
Value=0;Rudimentary roofing
TextColor=Blue
Value=3;Palm/bamboo
Value=4;Wood planks
Value=5;Cardboard
Value=0;Finished roofing
TextColor=Blue
Value=6;Metal/Tin
Value=7;Wood
Value=8;Calamine / Cement fiber
Value=9;Ceramic tiles
Value=10;Cement
Value=11;Decra
Value=96;Other

[Item]
Label=Other
Name=F03X
Start=116
Len=100
DataType=Alpha

[Item]
Label=What is the main material used for the floor of the main dwelling?
Name=F04
Start=216
Len=2
DataType=Numeric

[ValueSet]
Label=What is the main material used for the floor of the main dwelling?
Name=F04_VS1
Value=0;Natural floor
TextColor=Blue
Value=1;Earth/Sand
Value=2;Dung
Value=0;Rudimentary floor
TextColor=Blue
Value=3;Wood planks
Value=0;Finished floor
TextColor=Blue
Value=4;Parquet or polished floor
Value=5;Linoleum (tapeh)/ vinyl
Value=6;Ceramic tiles
Value=7;Cement/ Concrete
Value=8;Carpet
Value=96;Other

[Item]
Label=Other
Name=F04X
Start=218
Len=100
DataType=Alpha

[Item]
Label=What is the household’s main source of lightning?
Name=F05
Start=318
Len=2
DataType=Numeric

[ValueSet]
Label=What is the household’s main source of lightning?
Name=F05_VS1
Value=1;Electricity (NAWEC)
Value=2;Electricity (Generator)
Value=3;Solar power
Value=4;Kerosene lamp with shade
Value=5;Other kerosene lamp
Value=6;Candles
Value=7;Battery powered light
Value=96;Other

[Item]
Label=Other
Name=F05X
Start=320
Len=100
DataType=Alpha

[Item]
Label=What is the household’s main cooking fuel?
Name=F06
Start=420
Len=2
DataType=Numeric

[ValueSet]
Label=What is the household’s main cooking fuel?
Name=F06_VS1
Value=1;Firewood collected
Value=2;Firewood purchased
Value=3;Charcoal
Value=4;Gas
Value=5;Electricity
Value=6;Solar power
Value=7;Animal/ plant waste
Value=8;Does not cook
Value=96;Other

[Item]
Label=Other
Name=F06X
Start=422
Len=100
DataType=Alpha

[Item]
Label=What type of toilet facility does your household use?
Name=F07
Start=522
Len=2
DataType=Numeric

[ValueSet]
Label=What type of toilet facility does your household use?
Name=F07_VS1
Value=1;Flush to piped sewer system
Value=2;Flush to septic tank
Value=3;Flush to pit latrine
Value=4;Ventilated improved pit latrine
Value=5;Pit latrine with slab
Value=6;Pit latrine without slab / Open pit
Value=7;No facility/Bush/ Open space
Value=96;Other

[Item]
Label=Other
Name=F07X
Start=524
Len=100
DataType=Alpha

[Item]
Label=Is the toilet facility shared with other households?
Name=F08
Start=624
Len=1
DataType=Numeric

[ValueSet]
Label=Is the toilet facility shared with other households?
Name=F08_VS1
Value=1;Yes
Value=2;No

[Item]
Label=How many household in total use this toilet facility including your own household?
Name=F09
Start=625
Len=2
DataType=Numeric

[ValueSet]
Label=How many household in total use this toilet facility including your own household?
Name=F09_VS1
Value=2:9
Value=10;Ten or more households
Value=98;DK

[Item]
Label=What is the main source of drinking water used by the household?
Name=F10
Start=627
Len=2
DataType=Numeric

[ValueSet]
Label=What is the main source of drinking water used by the household?
Name=F10_VS1
Value=1;Piped into dwelling
Value=2;Piped into compound
Value=11;Piped to neighbour
Value=3;Public stand pipe
Value=4;Protected well in compound
Value=5;Unprotected well in compound
Value=6;Well with pump (public)
Value=7;Well without pump (public)
Value=8;River, stream
Value=9;Bottled / sachet water
Value=10;Borehole
Value=96;Other

[Item]
Label=Other
Name=F10X
Start=629
Len=100
DataType=Alpha

[Item]
Label=How does the household usually dispose of rubbish/refuse?
Name=F11
Start=729
Len=2
DataType=Numeric

[ValueSet]
Label=How does the household usually dispose of rubbish/refuse?
Name=F11_VS1
Value=1;Landfill / Bury
Value=2;Burn
Value=3;Use as compost
Value=4;Recycle
Value=5;Collected by municipality (household provides containers)
Value=6;Collected by municipality (municipality provides containers)
Value=7;Collected by private body
Value=8;Set Setal (community cleaning days)
Value=9;Public dump (authorized)
Value=10;In the bush or open space
Value=11;Collected by carts
Value=96;Other

[Item]
Label=Other
Name=F11X
Start=731
Len=100
DataType=Alpha

[Record]
Label=DISTANCE FROM SERVICES
Name=SECTG
RecordTypeValue='7'
Required=No
MaxRecords=13
RecordLen=123

[Item]
Label=Service facility
Name=G00
Start=11
Len=2
DataType=Numeric

[ValueSet]
Label=Service facility
Name=G00_VS1
Value=1;Drinking water supply
Value=2;Market/Lumo
Value=3;Pre-school
Value=4;Primary school.
Value=5;Secondary school
Value=6;Health center / Dispensary
Value=7;Post office
Value=8;Police post
Value=9;All seasons road
Value=10;Fire and Rescue services
Value=11;Hospital
Value=12;Financial services
Value=13;Tertiary (including TVET)

[Item]
Label=How far (in km) are these service facilities from your household?
Name=G01
Start=13
Len=6
DataType=Numeric
Decimal=2
DecimalChar=Yes

[Item]
Label=How long (in minutes) does your household take to reach these service facilities?
Name=G02
Start=19
Len=3
DataType=Numeric

[Item]
Label=Which means does your household mainly use to reach these service facilities, ?
Name=G03
Start=22
Len=2
DataType=Numeric

[ValueSet]
Label=Which means does your household mainly use to reach these service facilities, ?
Name=G03_VS1
Value=1;Vehicle
Value=2;Motorcycle
Value=3;Bicycle
Value=4;Foot
Value=5;Animal cart
Value=6;Boat
Value=96;Other

[Item]
Label=Other mean
Name=G03X
Start=24
Len=100
DataType=Alpha

[Record]
Label=HOUSEHOLD ASSETS
Name=SECTH
RecordTypeValue='8'
Required=No
MaxRecords=12
RecordLen=21

[Item]
Label=Item
Name=H00
Start=11
Len=2
DataType=Numeric

[ValueSet]
Label=Item
Name=H00_VS1
Value=1;Radio
Value=2;TV
Value=3;Mobile phone
Value=4;Land telephone
Value=5;Computer/ Laptop
Value=6;Bicycle
Value=7;Motorcycle
Value=8;Car/Van
Value=9;Truck/Lorry
Value=10;Bus
Value=11;Boat / Canoe
Value=12;Animal drawn cart

[Item]
Label=Does household/anyone in the household own [ITEM]?
Name=H01
Start=13
Len=1
DataType=Numeric

[ValueSet]
Label=Does household/anyone in the household own [ITEM]?
Name=H01_VS1
Value=1;Yes
Value=2;No

[Item]
Label=What is the total number of [ITEMS] owned?
Name=H02
Start=14
Len=2
DataType=Numeric

[ValueSet]
Label=What is the total number of [ITEMS] owned?
Name=H02_VS1
Value=1:99

[Item]
Label=How long ago the item 1 was obtained?
Name=H03A
Start=16
Len=3
DataType=Numeric

[ValueSet]
Label=How long ago the item 1 was obtained?
Name=H03A_VS1
Value=0:50

[Item]
Label=How long ago the item 2 was obtained?
Name=H03B
Start=19
Len=3
DataType=Numeric

[ValueSet]
Label=How long ago the item 2 was obtained?
Name=H03B_VS1
Value=0:50

[Record]
Label=INCOME SOURCES
Name=SECTI
RecordTypeValue='9'
Required=No
RecordLen=248

[Item]
Label=What is the household’s main source of income?
Name=I01
Start=11
Len=2
DataType=Numeric

[ValueSet]
Label=What is the household’s main source of income?
Name=I01_VS1
Value=1;Sale of food crops production (including garden produce)
Value=2;Sale of cash crops (e.g. groundnuts)
Value=3;Sale of animals/ livestock, animal produce
Value=4;Fishing
Value=5;Forest
Value=6;Sand and gravel mining
Value=7;Agricultural wage labor (paid in kind)
Value=8;Agricultural hired labor
Value=9;Non agriculture wage labor (e.g. construction workers)
Value=10;Self-employed services (e.g. taxi, carpenter, crafts)
Value=11;Self-employed shopkeepers, traders
Value=12;Self-employed street vendors
Value=13;Salaried employee – NGO / private
Value=14;Salaried employee – Public
Value=15;Business / entrepreneur
Value=16;Pensions / allowances
Value=17;Remittances
Value=18;Project/NGO support
Value=19;Begging
Value=96;Other

[Item]
Label=Other
Name=I01X
Start=13
Len=50
DataType=Alpha

[Item]
Label=What is the household’s second main income source?
Name=I02
Start=63
Len=2
DataType=Numeric

[ValueSet]
Label=What is the household’s second main income source?
Name=I02_VS1
Value=1;Sale of food crops production (including garden produce)
Value=2;Sale of cash crops (e.g. groundnuts)
Value=3;Sale of animals/ livestock, animal produce
Value=4;Fishing
Value=5;Forest
Value=6;Sand and gravel mining
Value=7;Agricultural wage labor (paid in kind)
Value=8;Agricultural hired labor
Value=9;Non agriculture wage labor (e.g. construction workers)
Value=10;Self-employed services (e.g. taxi, carpenter, crafts)
Value=11;Self-employed shopkeepers, traders
Value=12;Self-employed street vendors
Value=13;Salaried employee – NGO / private
Value=14;Salaried employee – Public
Value=15;Business / entrepreneur
Value=16;Pensions / allowances
Value=17;Remittances
Value=18;Credit / Loan
Value=19;Project/NGO support
Value=20;Begging
Value=21;None
Value=96;Other

[Item]
Label=Ohter
Name=I02X
Start=65
Len=50
DataType=Alpha

[Item]
Label=In the past 12 months, did the household receive help in the form of money (and goods) from individual(s) not living in the household?
Name=I03
Start=115
Len=1
DataType=Numeric

[ValueSet]
Label=In the past 12 months, did the household receive help in the form of money (and goods) from individual(s) not living in the household?
Name=I03_VS1
Value=1;Yes
Value=2;No

[Item]
Label=If yes, how many times?
Name=I04
Start=116
Len=2
DataType=Numeric

[ValueSet]
Label=If yes, how many times?
Name=I04_VS1
Value=1:99

[Item]
Label=In total, what was the amount received in the last 12 months?
Name=I05
Start=118
Len=10
DataType=Numeric

[ValueSet]
Label=In total, what was the amount received in the last 12 months?
Name=I05_VS1
Value=1:9999999995
Value=9999999998;Don't know
Value=9999999999;Do not want to disclose

[Item]
Label=In the past 12 months, has this household received or collected any aid (money and/or goods) from any organization?
Name=I06
Start=128
Len=1
DataType=Numeric

[ValueSet]
Label=In the past 12 months, has this household received or collected any aid (money and/or goods) from any organization?
Name=I06_VS1
Value=1;Yes
Value=2;No

[Item]
Label=If yes, what type of aid has the household received?
Name=I07
Start=129
Len=12
DataType=Alpha

[ValueSet]
Label=If yes, what type of aid has the household received?
Name=I07_VS1
Value='A           ';Cash
Value='B           ';Food
Value='C           ';Agricultural inputs
Value='D           ';Livestock
Value='E           ';Training
Value='F           ';Health services
Value='G           ';School support
Value='H           ';Water
Value='I           ';Household rehabilitation
Value='J           ';Clothing
Value='K           ';Household furniture
Value='Z           ';Other

[Item]
Label=Other
Name=I07X
Start=141
Len=50
DataType=Alpha

[Item]
Label=How frequently?
Name=I08
Start=191
Len=2
DataType=Numeric

[ValueSet]
Label=How frequently?
Name=I08_VS1
Value=1;Weekly
Value=2;Monthly
Value=3;Quarterly
Value=4;Bi-Annually
Value=5;Annually
Value=6;one-off
Value=7;Ad-hoc
Value=96;Other

[Item]
Label=Other
Name=I08X
Start=193
Len=50
DataType=Alpha

[Item]
Label=From which type of organization?
Name=I09
Start=243
Len=6
DataType=Alpha

[ValueSet]
Label=From which type of organization?
Name=I09_VS1
Value='A     ';Religious
Value='B     ';Community Organisation
Value='C     ';Local NGO
Value='D     ';Government
Value='E     ';International agency
Value='F     ';Don’t know

[Record]
Label=AGRICULTURAL ACTIVITY 1 (singles items)
Name=SECTJ1
RecordTypeValue='A'
Required=No
RecordLen=118

[Item]
Label=Does anyone in your household cultivate land?
Name=J01
Start=11
Len=1
DataType=Numeric

[ValueSet]
Label=Does anyone in your household cultivate land?
Name=J01_VS1
Value=1;Yes
Value=2;No

[Item]
Label=Was any household member involved in catching or farming fish for sale or family use in the last 12 months?
Name=J07
Start=12
Len=1
DataType=Numeric

[ValueSet]
Label=Was any household member involved in catching or farming fish for sale or family use in the last 12 months?
Name=J07_VS1
Value=1;Yes
Value=2;No

[Item]
Label=List of member involved
Name=J07X
Start=13
Len=100
DataType=Alpha

[Item]
Label=How many male household members
Name=J08A
Start=113
Len=2
DataType=Numeric

[Item]
Label=How many female household members
Name=J08B
Start=115
Len=2
DataType=Numeric

[Item]
Label=Does anyone of your household own livestock?
Name=J09
Start=117
Len=2
DataType=Numeric

[ValueSet]
Label=Does anyone of your household own livestock?
Name=J09_VS1
Value=1;Yes
Value=2;No

[Record]
Label=AGRICULTURAL ACTIVITY 2 (land)
Name=SECTJ2
RecordTypeValue='B'
Required=No
MaxRecords=3
RecordLen=19

[Item]
Label=Type of land
Name=J02A
Start=11
Len=1
DataType=Numeric

[ValueSet]
Label=Type of land
Name=J02A_VS1
Value=1;Owned
Value=2;Rented
Value=3;Used for free

[Item]
Label=How much land does the household cultivate?
Name=J02
Start=12
Len=7
DataType=Numeric
Decimal=2
DecimalChar=Yes

[ValueSet]
Label=How much land does the household cultivate?
Name=J02_VS1
Value=0,00:999,00

[Item]
Label=If owned, by whom?
Name=J03
Start=19
Len=1
DataType=Numeric

[ValueSet]
Label=If owned, by whom?
Name=J03_VS1
Value=1;Female members
Value=2;Male members
Value=3;Both

[Record]
Label=AGRICULTURAL ACTIVITY 3 (ecology)
Name=SECTJ3
RecordTypeValue='C'
Required=No
MaxRecords=4
RecordLen=18

[Item]
Label=Type of ecology
Name=J04A
Start=11
Len=1
DataType=Numeric

[ValueSet]
Label=Type of ecology
Name=J04A_VS1
Value=1;Rain fed, Low
Value=2;Rain fed, High
Value=3;Irrigated
Value=4;Pasture

[Item]
Label=how many hectares
Name=J04
Start=12
Len=7
DataType=Numeric
Decimal=2
DecimalChar=Yes

[Record]
Label=AGRICULTURAL ACTIVITY 4 (crops)
Name=SECTJ4
RecordTypeValue='D'
Required=No
MaxRecords=6
RecordLen=122

[Item]
Label=Crops
Name=J05A
Start=11
Len=1
DataType=Numeric

[ValueSet]
Label=Crops
Name=J05A_VS1
Value=1;Cereals
Value=2;Groundnut
Value=3;Vegetables
Value=4;Fruit trees
Value=5;Legumes
Value=6;Tubers

[Item]
Label=how many hectares of cultivated crops
Name=J05
Start=12
Len=7
DataType=Numeric
Decimal=2
DecimalChar=Yes

[Item]
Label=List of members responsibles for cultivation of crops
Name=J06
Start=19
Len=100
DataType=Alpha

[Item]
Label=Number of males responsibles for cultivation
Name=J06A
Start=119
Len=2
DataType=Numeric

[Item]
Label=Number of females responsibles for cultivation
Name=J06B
Start=121
Len=2
DataType=Numeric

[Record]
Label=AGRICULTURAL ACTIVITY 5 (livestock)
Name=SECTJ5
RecordTypeValue='E'
Required=No
MaxRecords=9
RecordLen=70

[Item]
Label=Animals
Name=J10A
Start=11
Len=2
DataType=Numeric

[ValueSet]
Label=Animals
Name=J10A_VS1
Value=1;Cattle
Value=2;Sheep
Value=3;Goats
Value=4;Pigs
Value=5;Chicken
Value=6;Ducks
Value=7;Horses
Value=8;Donkeys
Value=9;Other animals

[Item]
Label=How many of the following livestock does the household own?
Name=J10
Start=13
Len=5
DataType=Numeric

[ValueSet]
Label=How many of the following livestock does the household own?
Name=J10_VS1
Value=0:99999

[Item]
Label=Other
Name=J10X
Start=18
Len=50
DataType=Alpha

[Item]
Label=Who is responsible for breeding the livestock?
Name=J11
Start=68
Len=3
DataType=Alpha

[ValueSet]
Label=Who is responsible for breeding the livestock?
Name=J11_VS1
Value='A  ';Male member
Value='B  ';Female Member
Value='C  ';People outside the household

[Record]
Label=IMPACT OF SHOCKS 1 (single item)
Name=SECTK1
RecordTypeValue='F'
Required=No
RecordLen=11

[Item]
Label=In the last 12 months, have the household’s livelihood activities been affected by any major negative event?
Name=K01
Start=11
Len=1
DataType=Numeric

[ValueSet]
Label=In the last 12 months, have the household’s livelihood activities been affected by any major negative event?
Name=K01_VS1
Value=1;Yes
Value=2;No

[Record]
Label=IMPACT OF SHOCKS 2
Name=SECTK2
RecordTypeValue='G'
Required=No
MaxRecords=4
RecordLen=123

[Item]
Label=livelihood
Name=K02A
Start=11
Len=1
DataType=Numeric

[ValueSet]
Label=livelihood
Name=K02A_VS1
Value=1;Crops
Value=2;Livestock
Value=3;Labour/employment
Value=4;Other livelihood

[Item]
Label=livelihood was affected?
Name=K02B
Start=12
Len=1
DataType=Numeric

[ValueSet]
Label=livelihood was affected?
Name=K02B_VS1
Value=1;Yes
Value=2;No

[Item]
Label=Other livelihood
Name=K02AX
Start=13
Len=50
DataType=Alpha

[Item]
Label=What type of shock affected the household activities?
Name=K03
Start=63
Len=10
DataType=Alpha

[ValueSet]
Label=What type of shock affected the household activities?
Name=K03_VS1
Value='A         ';Pest infestation
Value='B         ';Rain storm
Value='C         ';Drought
Value='D         ';Wind Storm
Value='E         ';Flood
Value='F         ';Bush/House fire
Value='G         ';High food prices
Value='H         ';Loss of income/employment
Value='I         ';Loss of the breadwinner
Value='Z         ';Other

[Item]
Label=Other shock
Name=K03X
Start=73
Len=50
DataType=Alpha

[Item]
Label=How do you judge the severity of the losses caused by the shocks in each activity?
Name=K04
Start=123
Len=1
DataType=Numeric

[ValueSet]
Label=How do you judge the severity of the losses caused by the shocks in each activity?
Name=K04_VS1
Value=1;Very severe
Value=2;Severe
Value=3;Mild / moderate

[Record]
Label=COPING STRATEGIES
Name=SECTL
RecordTypeValue='H'
Required=No
RecordLen=28

[Item]
Label=In the last 12 months, was someone in this household was in need of resorting to any of these livelihood coping strategies?
Name=L01
Start=11
Len=1
DataType=Numeric
Occurrences=9
OccurrenceLabel=1,Engage in casual labor
OccurrenceLabel=2,Sell property/assets (including livestock)
OccurrenceLabel=3,Borrow money
OccurrenceLabel=4,Seek assistance from friends, community and relatives
OccurrenceLabel=5,Seek assistance from relief agencies
OccurrenceLabel=6,Rely on remittances
OccurrenceLabel=7,Sand and gravel mining
OccurrenceLabel=8,Relocate family
OccurrenceLabel=9,Begging

[ValueSet]
Label=In the last 12 months, was someone in this household was in need of resorting to any of these livelihood coping strategies?
Name=L01_VS1
Value=1;Yes, frequently
Value=2;Only during the dry period
Value=3;Occasionally
Value=4;Never

[Item]
Label=In the last 12 months, how often did the household have to resort to any of these food coping strategies?
Name=L02
Start=20
Len=1
DataType=Numeric
Occurrences=9
OccurrenceLabel=1,Consume less preferred or less expensive food
OccurrenceLabel=2,Borrow food or money from a friend/relative/people from community
OccurrenceLabel=3,Reduce the size of food portions
OccurrenceLabel=4,Reduce the number of daily meals
OccurrenceLabel=5,Restrict consumption of adults for children to eat
OccurrenceLabel=6,Spend entire day(s) without eating
OccurrenceLabel=7,Harvest wild food /hunting
OccurrenceLabel=8,Merging households to eat together
OccurrenceLabel=9,Begging

[ValueSet]
Label=In the last 12 months, how often did the household have to resort to any of these food coping strategies?
Name=L02_VS1
Value=1;Yes, Frequently
Value=2;Only during the lean period
Value=3;Occasionally
Value=4;Never

[Record]
Label=Other variables
Name=OTHER_VARIABLES
RecordTypeValue='I'
RecordLen=31

[Item]
Label=Other items
Name=BADCLOSED
Start=11
Len=1
DataType=Numeric

[ValueSet]
Label=Other items
Name=BADCLOSED_VS1
Value=0;Bad closed
Value=1;Well closed

[Item]
Label=Start time
Name=START_TIME
Start=12
Len=10
DataType=Numeric

[Item]
Label=End time
Name=END_TIME
Start=22
Len=10
DataType=Numeric
