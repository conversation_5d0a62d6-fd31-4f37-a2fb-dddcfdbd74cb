﻿{Application 'GPS_123' logic file generated by CSPro}
PROC GLOBAL
	MAP mymap;
	numeric id_marker;
	file listeGPSmauvais;
	
function isLeft(P0x, P0y, P1x, P1y, P2x, P2y)

    isLeft = (P1x - P0x) * (P2y - P0y)
            - (P2x -  P0x) * (P1y - P0y);
end

function IdentifierPointsDansPolygone(string Xagent,string nomFichier)
	//Cette fonction va permettre de parcourir les points de l'ilôt et dire s'ils sont dans un polygone
	
	//Dans un premier temps, on Extrait les points (latitude et longitude) d'un fichier Geojson 

	file tmpfile;
	setfile(tmpfile,nomFichier);
	setfile(listeGPSmauvais,"mauvaisGPS.txt");
	filewrite(listeGPSmauvais,"EA;HH;int;lat;lon");
	
	string text;
	fileread(tmpfile,text);
	close(tmpfile);

	numeric a_min = pos("[[[",text);
	numeric a_max = pos("]]]",text);

	text = text[a_min+2:a_max-a_min-1];

	numeric total = 0;
	list numeric POLY_LAT, POLY_LON;

	string message = text + "\n";
	while text <> "" do
		numeric virgule = pos(",",text);
		numeric FinCrochet = pos("]",text);
		inc(total);
		POLY_LAT(total) = tonumber(text[2:virgule-2]);
		POLY_LON(total) = tonumber(text[virgule+1:FinCrochet-virgule-1]);
		text = text[FinCrochet+2:length(text)-FinCrochet-1];
		//message = message + maketext("Lat%d = %3.14f, Lon%d = %3.14f, \n",total-1,xlat(total-1),total-1,xlon(total-1));
	enddo;	
	// Ici s'achève l'extraction des points
	
	//Maintenant on parcourt les points du fichier structures et on regarde ceux qui sont à l'intérieur du polygone
	numeric totalPoint = 0;
	numeric bad = 0;
	forcase SOCIAL_DICT where A10GPS = 1 do //on ne prend que les points de l'ilôt en cours
		inc(totalPoint);
		
		numeric wn = 0;    // the  winding number counter
		// numeric n = count(POLYS_REC) - 1;
		numeric n = total - 1;
		
		// loop through all edges of the polygon
		do numeric i=1 while i <= n    // edge from V[i] to  V[i+1]
			numeric LON = a10b; //Je ne sais pas pourquoi, mais c'est en faisant Lon = latitude de notre fichier que le programme marche
			numeric LAT = a10a; //meme explication que ci-dessus
			if POLY_LAT(i) <= LAT then         // start y <= P.y
				if POLY_LAT(i+1) > LAT then      // an upward crossing
					 if isLeft(POLY_LON(i), POLY_LAT(i), POLY_LON(i+1), POLY_LAT(i+1), LON, LAT) > 0 then  // P left of  edge
						 wn = wn + 1;            // have  a valid up intersect
					 endif;
				endif;
			else                         // start y > P.y (no test needed)
				if POLY_LAT(i+1)  <= LAT then     // a downward crossing
					 if isLeft(POLY_LON(i), POLY_LAT(i), POLY_LON(i+1), POLY_LAT(i+1), LON, LAT) < 0 then // P right of  edge
						 wn = wn -1;            // have  a valid down intersect
					 endif;
				endif;
			endif;
		enddo;
		
		if wn = 0 then
			//errmsg("Point outside");
			id_marker = mymap.addmarker(a10b,a10a);
			mymap.setmarkerdescription(id_marker,maketext("EA %d_%d, int = %03d",A08,a10,a02));
			filewrite(listeGPSmauvais,"%05d;%03d;%03d;%5.9f;%5.9f",A08,a10,a02,a10b,a10a);
			inc(bad);

		else
			//errmsg("Point inside");

		endif;
		
	endfor;
	close(listeGPSmauvais);
	errmsg("bad = %d, totalPoint = %d", bad,totalPoint);

end;

PROC GPS_FF

PROC GPS_ID

PREPROC

	
	setfile(SOCIAL_DICT,"all.csdb");
	
	
	// geometry polygon;

	// forcase SOCIAL_DICT where A10GPS = 1 do
		// id_marker = mymap.addmarker(a10b,a10a);
		// mymap.setmarkerdescription(id_marker,maketext("EA %d_%d, int = %03d",A08,a10,a02));
	// endfor;	
	IdentifierPointsDansPolygone("", "gambia.geojson");
	// polygon.load("gambia.geojson");
	// mymap.addgeometry(polygon);
	// if polygon.tracepolygon(myMap) then
		// polygon.save(maketext("../REF/gambia.geojson"));
	// endif;
	mymap.show();
