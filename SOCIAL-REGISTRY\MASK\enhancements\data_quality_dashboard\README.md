# Data Quality Monitoring Dashboard

## Overview

The Data Quality Monitoring Dashboard provides real-time visualization and monitoring of data quality metrics for the Social Registry System. It enables supervisors and data managers to quickly identify and address data quality issues, monitor interviewer performance, and track survey progress.

## Features

- **Real-time data quality indicators**
- **Interactive visualizations**
- **Interviewer performance metrics**
- **Alert system for quality issues**
- **Customizable dashboards**
- **Historical trend analysis**

## Architecture

The dashboard is built using a Flask backend with a React frontend. It connects to the Social Registry System database and provides RESTful APIs for data access.

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  CSPro Database │────▶│  Flask Backend  │────▶│  React Frontend │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │
                               ▼
                        ┌─────────────────┐
                        │  Data Quality   │
                        │   Processing    │
                        └─────────────────┘
```

## Requirements

- Python 3.8+
- Node.js 14+
- Flask
- React
- Pandas
- Plotly/Dash
- SQLAlchemy

## Installation

1. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Install Node.js dependencies:
   ```
   cd frontend
   npm install
   ```

3. Configure the database connection in `config.py`

4. Run the development server:
   ```
   python run.py
   ```

## Integration with Social Registry System

To integrate the dashboard with the main system:

1. Add the dashboard module to the orchestration configuration
2. Configure database access permissions
3. Set up scheduled data processing tasks
4. Add dashboard launch to the main menu

## Usage

### Starting the Dashboard

```python
from enhancements.data_quality_dashboard import app

# Launch the dashboard server
app.run(host='0.0.0.0', port=5000)
```

### Accessing the Dashboard

Open a web browser and navigate to:
```
http://localhost:5000/dashboard
```

### Authentication

The dashboard uses the same authentication system as the main Social Registry System. Users need appropriate permissions to access the dashboard features.

## Data Quality Metrics

The dashboard tracks the following data quality metrics:

1. **Completeness**
   - Percentage of required fields completed
   - Missing data rates by section

2. **Consistency**
   - Internal consistency checks
   - Logical relationship validations

3. **Timeliness**
   - Interview duration
   - Data submission delays

4. **Accuracy**
   - Outlier detection
   - Response distribution analysis

5. **Interviewer Performance**
   - Productivity metrics
   - Error rates
   - Interview duration statistics

## Customization

The dashboard can be customized through the configuration file:

```python
# config.py
DASHBOARD_CONFIG = {
    'metrics': {
        'completeness': True,
        'consistency': True,
        'timeliness': True,
        'accuracy': True,
        'interviewer_performance': True
    },
    'refresh_interval': 300,  # seconds
    'alert_thresholds': {
        'missing_data': 0.05,  # 5%
        'consistency_errors': 0.02,  # 2%
        'outlier_rate': 0.03  # 3%
    }
}
```

## Screenshots

![Dashboard Overview](screenshots/dashboard_overview.png)
![Data Quality Metrics](screenshots/data_quality_metrics.png)
![Interviewer Performance](screenshots/interviewer_performance.png)
![Alert System](screenshots/alert_system.png)

## Roadmap

- [ ] Add machine learning-based anomaly detection
- [ ] Implement real-time notifications
- [ ] Add mobile-responsive design
- [ ] Create exportable reports
- [ ] Add integration with external BI tools