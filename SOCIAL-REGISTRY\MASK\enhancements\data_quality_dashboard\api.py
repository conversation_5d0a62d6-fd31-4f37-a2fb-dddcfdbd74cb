"""
API module for the Data Quality Dashboard.

This module provides API endpoints for accessing data quality metrics.
"""

from flask import Blueprint, jsonify, request, current_app
from flask_login import login_required
from data_processor import DataProcessor

# Create API blueprint
api_bp = Blueprint('api', __name__)

# Initialize data processor (will be properly initialized in a request context)
data_processor = None

@api_bp.before_request
def initialize_data_processor():
    """Initialize the data processor before handling a request."""
    global data_processor
    if data_processor is None:
        data_processor = DataProcessor(current_app.config['CSPRO_DATA_DIR'])

@api_bp.route('/summary', methods=['GET'])
@login_required
def get_summary():
    """Get a summary of the data files.
    
    Returns:
        JSON response with data summary
    """
    summary = data_processor.get_data_summary()
    return jsonify(summary)

@api_bp.route('/quality', methods=['GET'])
@login_required
def get_quality_metrics():
    """Get data quality metrics.
    
    Returns:
        JSON response with data quality metrics
    """
    metrics = data_processor.process_data_quality()
    return jsonify(metrics)

@api_bp.route('/completeness', methods=['GET'])
@login_required
def get_completeness():
    """Get completeness metrics.
    
    Returns:
        JSON response with completeness metrics
    """
    metrics = data_processor.process_data_quality()
    return jsonify(metrics['completeness'])

@api_bp.route('/consistency', methods=['GET'])
@login_required
def get_consistency():
    """Get consistency metrics.
    
    Returns:
        JSON response with consistency metrics
    """
    metrics = data_processor.process_data_quality()
    return jsonify(metrics['consistency'])

@api_bp.route('/timeliness', methods=['GET'])
@login_required
def get_timeliness():
    """Get timeliness metrics.
    
    Returns:
        JSON response with timeliness metrics
    """
    metrics = data_processor.process_data_quality()
    return jsonify(metrics['timeliness'])

@api_bp.route('/accuracy', methods=['GET'])
@login_required
def get_accuracy():
    """Get accuracy metrics.
    
    Returns:
        JSON response with accuracy metrics
    """
    metrics = data_processor.process_data_quality()
    return jsonify(metrics['accuracy'])

@api_bp.route('/interviewer-performance', methods=['GET'])
@login_required
def get_interviewer_performance():
    """Get interviewer performance metrics.
    
    Returns:
        JSON response with interviewer performance metrics
    """
    metrics = data_processor.process_data_quality()
    return jsonify(metrics['interviewer_performance'])

@api_bp.route('/alerts', methods=['GET'])
@login_required
def get_alerts():
    """Get data quality alerts.
    
    Returns:
        JSON response with data quality alerts
    """
    # Get alert thresholds from configuration
    thresholds = current_app.config['DASHBOARD_CONFIG']['alert_thresholds']
    
    # Get alerts from data processor
    alerts = data_processor.get_alerts(thresholds)
    
    return jsonify({
        'alerts': alerts,
        'count': len(alerts)
    })

@api_bp.route('/refresh', methods=['POST'])
@login_required
def refresh_data():
    """Refresh data from the data directory.
    
    Returns:
        JSON response indicating success
    """
    data_processor.refresh_data_files()
    return jsonify({
        'status': 'success',
        'message': 'Data refreshed successfully'
    })