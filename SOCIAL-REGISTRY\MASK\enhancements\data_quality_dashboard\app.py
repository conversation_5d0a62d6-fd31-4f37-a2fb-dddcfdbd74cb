"""
Data Quality Dashboard Application.

This module provides a Flask application for monitoring data quality
in the Social Registry System.
"""

import os
from flask import Flask, render_template, jsonify, request, redirect, url_for
from flask_cors import CORS
from flask_login import LoginManager, login_required, current_user, login_user, logout_user

from config import get_config
from models import db, User
from api import api_bp
from auth import auth_bp
from dashboard import dashboard_bp
from data_processor import DataProcessor

def create_app(config_name='default'):
    """Create and configure the Flask application.

    Args:
        config_name: Configuration environment to use

    Returns:
        Configured Flask application
    """
    app = Flask(__name__,
                static_folder='static',
                template_folder='templates')

    # Load configuration
    app_config = get_config()
    app.config.from_object(app_config)

    # Ensure SQLALCHEMY_DATABASE_URI is set
    if not app.config.get('SQLALCHEMY_DATABASE_URI'):
        app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('SQLALCHEMY_DATABASE_URI', 'sqlite:///dashboard.db')

    # Initialize extensions
    db.init_app(app)
    CORS(app)

    # Set up login manager
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # Register blueprints
    app.register_blueprint(api_bp, url_prefix=app_config.API_PREFIX)
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')

    # Create database tables
    with app.app_context():
        db.create_all()

    # Set up data processor
    data_processor = DataProcessor(app_config.CSPRO_DATA_DIR)

    @app.route('/')
    def index():
        """Render the index page."""
        return redirect(url_for('dashboard.index'))

    @app.route('/health')
    def health():
        """Health check endpoint."""
        return jsonify({
            'status': 'ok',
            'version': '1.0.0'
        })

    @app.errorhandler(404)
    def page_not_found(e):
        """Handle 404 errors."""
        return render_template('404.html'), 404

    @app.errorhandler(500)
    def server_error(e):
        """Handle 500 errors."""
        return render_template('500.html'), 500

    return app


if __name__ == '__main__':
    app = create_app()
    app.run(
        host=app.config.get('HOST', '0.0.0.0'),
        port=app.config.get('PORT', 5000),
        debug=app.config.get('DEBUG', False)
    )