"""
Configuration for the Data Quality Dashboard.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '.env'))

class Config:
    """Base configuration."""
    # Secret key for session management
    SECRET_KEY = os.environ.get('SECRET_KEY', 'social-registry-dashboard-secret')

    # Database URI for the Social Registry System
    DATABASE_URI = os.environ.get('DATABASE_URI', 'sqlite:///../../DATA/dashboard.db')

    # SQLAlchemy database URI
    SQLALCHEMY_DATABASE_URI = os.environ.get('SQLALCHEMY_DATABASE_URI', 'sqlite:///dashboard.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # CSPro data directory
    CSPRO_DATA_DIR = os.environ.get('CSPRO_DATA_DIR', '../../DATA')

    # Dashboard settings
    DASHBOARD_CONFIG = {
        'metrics': {
            'completeness': True,
            'consistency': True,
            'timeliness': True,
            'accuracy': True,
            'interviewer_performance': True
        },
        'refresh_interval': 300,  # seconds
        'alert_thresholds': {
            'missing_data': 0.05,  # 5%
            'consistency_errors': 0.02,  # 2%
            'outlier_rate': 0.03  # 3%
        }
    }

    # API settings
    API_PREFIX = '/api/v1'

    # Authentication settings
    AUTH_REQUIRED = True

    # Debug settings
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

    # Server settings
    HOST = os.environ.get('HOST', '0.0.0.0')
    PORT = int(os.environ.get('PORT', 5000))


class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True


class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False


class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    DEBUG = True
    DATABASE_URI = 'sqlite:///:memory:'


# Configuration dictionary for app setup
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# Get current configuration
def get_config():
    """Return the appropriate configuration object based on the environment."""
    env = os.environ.get('FLASK_ENV', 'default')
    return config.get(env, config['default'])