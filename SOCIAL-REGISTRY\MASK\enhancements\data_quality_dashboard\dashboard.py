"""
Dashboard module for the Data Quality Dashboard.

This module provides the dashboard views and functionality.
"""

from flask import Blueprint, render_template, jsonify, request, current_app
from flask_login import login_required, current_user
from data_processor import DataProcessor

# Create dashboard blueprint
dashboard_bp = Blueprint('dashboard', __name__, template_folder='templates')

# Initialize data processor (will be properly initialized in a request context)
data_processor = None

@dashboard_bp.before_request
def initialize_data_processor():
    """Initialize the data processor before handling a request."""
    global data_processor
    if data_processor is None:
        data_processor = DataProcessor(current_app.config['CSPRO_DATA_DIR'])

@dashboard_bp.route('/')
@login_required
def index():
    """Render the main dashboard page.
    
    Returns:
        Rendered template
    """
    # Get data summary
    summary = data_processor.get_data_summary()
    
    # Get alert thresholds from configuration
    thresholds = current_app.config['DASHBOARD_CONFIG']['alert_thresholds']
    
    # Get alerts from data processor
    alerts = data_processor.get_alerts(thresholds)
    
    # Render dashboard template
    return render_template(
        'dashboard/index.html',
        summary=summary,
        alerts=alerts,
        alert_count=len(alerts),
        refresh_interval=current_app.config['DASHBOARD_CONFIG']['refresh_interval']
    )

@dashboard_bp.route('/completeness')
@login_required
def completeness():
    """Render the completeness dashboard page.
    
    Returns:
        Rendered template
    """
    # Process data quality
    metrics = data_processor.process_data_quality()
    
    # Render completeness template
    return render_template(
        'dashboard/completeness.html',
        metrics=metrics,
        completeness=metrics['completeness']
    )

@dashboard_bp.route('/consistency')
@login_required
def consistency():
    """Render the consistency dashboard page.
    
    Returns:
        Rendered template
    """
    # Process data quality
    metrics = data_processor.process_data_quality()
    
    # Render consistency template
    return render_template(
        'dashboard/consistency.html',
        metrics=metrics,
        consistency=metrics['consistency']
    )

@dashboard_bp.route('/timeliness')
@login_required
def timeliness():
    """Render the timeliness dashboard page.
    
    Returns:
        Rendered template
    """
    # Process data quality
    metrics = data_processor.process_data_quality()
    
    # Render timeliness template
    return render_template(
        'dashboard/timeliness.html',
        metrics=metrics,
        timeliness=metrics['timeliness']
    )

@dashboard_bp.route('/accuracy')
@login_required
def accuracy():
    """Render the accuracy dashboard page.
    
    Returns:
        Rendered template
    """
    # Process data quality
    metrics = data_processor.process_data_quality()
    
    # Render accuracy template
    return render_template(
        'dashboard/accuracy.html',
        metrics=metrics,
        accuracy=metrics['accuracy']
    )

@dashboard_bp.route('/interviewer-performance')
@login_required
def interviewer_performance():
    """Render the interviewer performance dashboard page.
    
    Returns:
        Rendered template
    """
    # Process data quality
    metrics = data_processor.process_data_quality()
    
    # Render interviewer performance template
    return render_template(
        'dashboard/interviewer_performance.html',
        metrics=metrics,
        interviewer_performance=metrics['interviewer_performance']
    )

@dashboard_bp.route('/alerts')
@login_required
def alerts():
    """Render the alerts dashboard page.
    
    Returns:
        Rendered template
    """
    # Get alert thresholds from configuration
    thresholds = current_app.config['DASHBOARD_CONFIG']['alert_thresholds']
    
    # Get alerts from data processor
    alerts = data_processor.get_alerts(thresholds)
    
    # Render alerts template
    return render_template(
        'dashboard/alerts.html',
        alerts=alerts,
        alert_count=len(alerts)
    )

@dashboard_bp.route('/settings')
@login_required
def settings():
    """Render the dashboard settings page.
    
    Returns:
        Rendered template
    """
    # Get dashboard configuration
    dashboard_config = current_app.config['DASHBOARD_CONFIG']
    
    # Render settings template
    return render_template(
        'dashboard/settings.html',
        dashboard_config=dashboard_config
    )

@dashboard_bp.route('/data-explorer')
@login_required
def data_explorer():
    """Render the data explorer page.
    
    Returns:
        Rendered template
    """
    # Get data summary
    summary = data_processor.get_data_summary()
    
    # Render data explorer template
    return render_template(
        'dashboard/data_explorer.html',
        summary=summary
    )