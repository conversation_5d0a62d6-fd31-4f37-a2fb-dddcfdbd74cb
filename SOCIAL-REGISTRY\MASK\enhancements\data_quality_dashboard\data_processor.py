"""
Data Processor for the Data Quality Dashboard.

This module processes CSPro data files to extract data quality metrics.
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import re
import logging
from typing import Dict, List, Any, Optional, Union, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('data_processor')

class DataProcessor:
    """Process CSPro data files for quality metrics."""
    
    def __init__(self, data_dir: str):
        """Initialize the data processor.
        
        Args:
            data_dir: Directory containing CSPro data files
        """
        self.data_dir = Path(data_dir)
        self.data_files = []
        self.metrics = {}
        self.refresh_data_files()
        
    def refresh_data_files(self) -> None:
        """Refresh the list of data files from the data directory."""
        self.data_files = list(self.data_dir.glob('*.csdb'))
        logger.info(f"Found {len(self.data_files)} data files in {self.data_dir}")
    
    def get_data_summary(self) -> Dict[str, Any]:
        """Get a summary of the data files.
        
        Returns:
            Dictionary with data summary information
        """
        summary = {
            'total_files': len(self.data_files),
            'total_size_bytes': sum(f.stat().st_size for f in self.data_files),
            'regions': {},
            'last_update': datetime.now().isoformat()
        }
        
        # Extract region codes from filenames (assuming format M[Region][District][Ward][Settlement].csdb)
        region_pattern = re.compile(r'M(\d{2})(\d{2})(\d{2})(\d{2})\.csdb', re.IGNORECASE)
        
        for file in self.data_files:
            match = region_pattern.match(file.name)
            if match:
                region_code = match.group(1)
                district_code = match.group(2)
                
                if region_code not in summary['regions']:
                    summary['regions'][region_code] = {
                        'count': 0,
                        'districts': {}
                    }
                
                summary['regions'][region_code]['count'] += 1
                
                if district_code not in summary['regions'][region_code]['districts']:
                    summary['regions'][region_code]['districts'][district_code] = 0
                
                summary['regions'][region_code]['districts'][district_code] += 1
        
        return summary
    
    def process_data_quality(self) -> Dict[str, Any]:
        """Process data files to extract data quality metrics.
        
        Returns:
            Dictionary with data quality metrics
        """
        # For now, we'll return simulated metrics
        # In a real implementation, this would parse the CSDB files
        # or connect to a database to extract actual metrics
        
        metrics = {
            'completeness': self._calculate_completeness(),
            'consistency': self._calculate_consistency(),
            'timeliness': self._calculate_timeliness(),
            'accuracy': self._calculate_accuracy(),
            'interviewer_performance': self._calculate_interviewer_performance(),
            'last_update': datetime.now().isoformat()
        }
        
        self.metrics = metrics
        return metrics
    
    def _calculate_completeness(self) -> Dict[str, Any]:
        """Calculate completeness metrics.
        
        Returns:
            Dictionary with completeness metrics
        """
        # Simulate completeness metrics
        return {
            'overall_completion_rate': 0.94,  # 94% completion rate
            'sections': {
                'identification': 0.99,
                'household_roster': 0.97,
                'education': 0.95,
                'health': 0.92,
                'employment': 0.91,
                'housing': 0.96,
                'assets': 0.93,
                'consumption': 0.89,
                'agriculture': 0.88
            },
            'missing_data_by_region': {
                '01': 0.03,
                '02': 0.05,
                '03': 0.07,
                '04': 0.04,
                '05': 0.06
            }
        }
    
    def _calculate_consistency(self) -> Dict[str, Any]:
        """Calculate consistency metrics.
        
        Returns:
            Dictionary with consistency metrics
        """
        # Simulate consistency metrics
        return {
            'overall_consistency_rate': 0.96,  # 96% consistency rate
            'common_issues': [
                {
                    'description': 'Age/education level mismatch',
                    'frequency': 128,
                    'severity': 'high'
                },
                {
                    'description': 'Employment status conflicts',
                    'frequency': 83,
                    'severity': 'medium'
                },
                {
                    'description': 'Household size/rooms mismatch',
                    'frequency': 67,
                    'severity': 'low'
                }
            ],
            'consistency_by_region': {
                '01': 0.97,
                '02': 0.96,
                '03': 0.94,
                '04': 0.98,
                '05': 0.95
            }
        }
    
    def _calculate_timeliness(self) -> Dict[str, Any]:
        """Calculate timeliness metrics.
        
        Returns:
            Dictionary with timeliness metrics
        """
        # Simulate timeliness metrics
        return {
            'average_interview_duration_minutes': 42.5,
            'interview_duration_distribution': {
                '<30min': 0.15,
                '30-45min': 0.45,
                '45-60min': 0.30,
                '>60min': 0.10
            },
            'submission_delay_days': {
                '0': 0.75,  # Same day
                '1': 0.15,  # Next day
                '2-3': 0.07,
                '>3': 0.03
            },
            'timeliness_by_region': {
                '01': {
                    'avg_duration': 40.2,
                    'avg_delay': 0.4
                },
                '02': {
                    'avg_duration': 43.5,
                    'avg_delay': 0.6
                },
                '03': {
                    'avg_duration': 44.8,
                    'avg_delay': 0.8
                },
                '04': {
                    'avg_duration': 41.3,
                    'avg_delay': 0.3
                },
                '05': {
                    'avg_duration': 42.7,
                    'avg_delay': 0.5
                }
            }
        }
    
    def _calculate_accuracy(self) -> Dict[str, Any]:
        """Calculate accuracy metrics.
        
        Returns:
            Dictionary with accuracy metrics
        """
        # Simulate accuracy metrics
        return {
            'outlier_rate': 0.03,  # 3% outliers
            'suspicious_patterns': [
                {
                    'description': 'Identical household compositions',
                    'frequency': 12,
                    'severity': 'high'
                },
                {
                    'description': 'Uniform income distribution',
                    'frequency': 18,
                    'severity': 'medium'
                },
                {
                    'description': 'Repeated asset patterns',
                    'frequency': 23,
                    'severity': 'low'
                }
            ],
            'accuracy_by_region': {
                '01': 0.98,
                '02': 0.97,
                '03': 0.96,
                '04': 0.97,
                '05': 0.96
            }
        }
    
    def _calculate_interviewer_performance(self) -> Dict[str, Any]:
        """Calculate interviewer performance metrics.
        
        Returns:
            Dictionary with interviewer performance metrics
        """
        # Simulate interviewer performance metrics
        return {
            'interviewers': [
                {
                    'id': 'INT001',
                    'completed_interviews': 42,
                    'avg_duration_minutes': 38.5,
                    'error_rate': 0.02,
                    'productivity_score': 0.94
                },
                {
                    'id': 'INT002',
                    'completed_interviews': 38,
                    'avg_duration_minutes': 40.2,
                    'error_rate': 0.03,
                    'productivity_score': 0.89
                },
                {
                    'id': 'INT003',
                    'completed_interviews': 45,
                    'avg_duration_minutes': 36.8,
                    'error_rate': 0.01,
                    'productivity_score': 0.97
                },
                {
                    'id': 'INT004',
                    'completed_interviews': 36,
                    'avg_duration_minutes': 43.5,
                    'error_rate': 0.04,
                    'productivity_score': 0.85
                },
                {
                    'id': 'INT005',
                    'completed_interviews': 40,
                    'avg_duration_minutes': 39.7,
                    'error_rate': 0.02,
                    'productivity_score': 0.92
                }
            ],
            'performance_by_region': {
                '01': {
                    'avg_productivity': 0.93,
                    'avg_error_rate': 0.02
                },
                '02': {
                    'avg_productivity': 0.90,
                    'avg_error_rate': 0.03
                },
                '03': {
                    'avg_productivity': 0.88,
                    'avg_error_rate': 0.04
                },
                '04': {
                    'avg_productivity': 0.92,
                    'avg_error_rate': 0.02
                },
                '05': {
                    'avg_productivity': 0.91,
                    'avg_error_rate': 0.03
                }
            }
        }
    
    def get_alerts(self, thresholds: Dict[str, float]) -> List[Dict[str, Any]]:
        """Get alerts based on data quality metrics.
        
        Args:
            thresholds: Dictionary with threshold values for alerts
            
        Returns:
            List of alert dictionaries
        """
        if not self.metrics:
            self.process_data_quality()
        
        alerts = []
        
        # Check completeness alerts
        missing_data_threshold = thresholds.get('missing_data', 0.05)
        for region, value in self.metrics['completeness']['missing_data_by_region'].items():
            if value > missing_data_threshold:
                alerts.append({
                    'type': 'completeness',
                    'level': 'warning' if value < missing_data_threshold * 1.5 else 'critical',
                    'message': f'High missing data rate in region {region}: {value:.1%}',
                    'region': region,
                    'value': value,
                    'threshold': missing_data_threshold
                })
        
        # Check consistency alerts
        consistency_threshold = thresholds.get('consistency_errors', 0.02)
        for issue in self.metrics['consistency']['common_issues']:
            # Normalize frequency to a rate (assuming 1000 total interviews)
            rate = issue['frequency'] / 1000
            if rate > consistency_threshold:
                alerts.append({
                    'type': 'consistency',
                    'level': 'warning' if issue['severity'] == 'medium' else 'critical',
                    'message': f'Common consistency issue: {issue["description"]}',
                    'frequency': issue['frequency'],
                    'severity': issue['severity'],
                    'rate': rate,
                    'threshold': consistency_threshold
                })
        
        # Check accuracy alerts
        outlier_threshold = thresholds.get('outlier_rate', 0.03)
        if self.metrics['accuracy']['outlier_rate'] > outlier_threshold:
            alerts.append({
                'type': 'accuracy',
                'level': 'warning',
                'message': f'High outlier rate: {self.metrics["accuracy"]["outlier_rate"]:.1%}',
                'value': self.metrics['accuracy']['outlier_rate'],
                'threshold': outlier_threshold
            })
        
        # Check interviewer performance alerts
        for interviewer in self.metrics['interviewer_performance']['interviewers']:
            if interviewer['error_rate'] > 0.03:
                alerts.append({
                    'type': 'interviewer_performance',
                    'level': 'warning' if interviewer['error_rate'] < 0.05 else 'critical',
                    'message': f'High error rate for interviewer {interviewer["id"]}: {interviewer["error_rate"]:.1%}',
                    'interviewer_id': interviewer['id'],
                    'value': interviewer['error_rate'],
                    'threshold': 0.03
                })
        
        return alerts