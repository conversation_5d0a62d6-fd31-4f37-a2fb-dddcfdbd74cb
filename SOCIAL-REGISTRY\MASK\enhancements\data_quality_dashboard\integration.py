"""
Integration module for the Data Quality Dashboard.

This module provides functions for integrating the dashboard with the main orchestration.
"""

import os
import sys
import subprocess
import threading
import time
import logging
import webbrowser
from pathlib import Path

# Add parent directory to path to allow importing from orchestration
parent_dir = str(Path(__file__).resolve().parent.parent.parent)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import from orchestration
from orchestration_config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('dashboard_integration')

def start_dashboard(host='0.0.0.0', port=5000, open_browser=True):
    """Start the Data Quality Dashboard.
    
    Args:
        host: Host to bind to
        port: Port to bind to
        open_browser: Whether to open a browser window
        
    Returns:
        Dashboard process
    """
    # Set environment variables
    os.environ['HOST'] = host
    os.environ['PORT'] = str(port)
    
    # Get dashboard directory
    dashboard_dir = Path(__file__).resolve().parent
    
    # Run dashboard in a separate process
    cmd = [sys.executable, str(dashboard_dir / 'run.py')]
    
    logger.info(f"Starting dashboard: {' '.join(cmd)}")
    
    # Start process
    process = subprocess.Popen(
        cmd,
        cwd=str(dashboard_dir),
        env=os.environ.copy()
    )
    
    # Give the server a moment to start
    time.sleep(2)
    
    # Open browser if requested
    if open_browser and process.poll() is None:  # Check if process is still running
        url = f"http://localhost:{port}"
        logger.info(f"Opening dashboard in browser: {url}")
        webbrowser.open(url)
    
    return process

def stop_dashboard(process):
    """Stop the Data Quality Dashboard.
    
    Args:
        process: Dashboard process to stop
    """
    if process.poll() is None:  # Check if process is still running
        logger.info("Stopping dashboard")
        process.terminate()
        process.wait(timeout=5)
    else:
        logger.info("Dashboard is not running")

class DashboardRunner:
    """Class for running the dashboard in a background thread."""
    
    def __init__(self, host='0.0.0.0', port=5000, open_browser=True):
        """Initialize the dashboard runner.
        
        Args:
            host: Host to bind to
            port: Port to bind to
            open_browser: Whether to open a browser window
        """
        self.host = host
        self.port = port
        self.open_browser = open_browser
        self.process = None
        self.thread = None
        self.running = False
    
    def start(self):
        """Start the dashboard in a background thread."""
        if self.running:
            logger.warning("Dashboard is already running")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._run)
        self.thread.daemon = True
        self.thread.start()
        
        logger.info("Started dashboard in background thread")
    
    def stop(self):
        """Stop the dashboard."""
        if not self.running:
            logger.warning("Dashboard is not running")
            return
        
        self.running = False
        if self.process:
            stop_dashboard(self.process)
        
        # Wait for thread to end
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        logger.info("Stopped dashboard")
    
    def _run(self):
        """Run the dashboard in a loop."""
        while self.running:
            try:
                self.process = start_dashboard(
                    host=self.host,
                    port=self.port,
                    open_browser=self.open_browser
                )
                
                # Wait for process to end
                self.process.wait()
                
                # If we get here, the process ended
                if self.running:
                    logger.warning("Dashboard process ended unexpectedly, restarting...")
                    time.sleep(2)  # Wait before restarting
                
            except Exception as e:
                logger.error(f"Error running dashboard: {e}")
                if self.running:
                    logger.info("Restarting dashboard in 5 seconds...")
                    time.sleep(5)  # Wait before restarting

def add_to_orchestration():
    """Add the dashboard to the main orchestration."""
    # This function would be called to integrate the dashboard with the main orchestration
    
    # Example integration code:
    # 1. Get orchestration configuration
    # config = get_config()
    # 
    # 2. Add dashboard configuration
    # config['dashboard'] = {
    #     'enabled': True,
    #     'host': '0.0.0.0',
    #     'port': 5000,
    #     'open_browser': True
    # }
    # 
    # 3. Save configuration
    # save_config(config)
    
    logger.info("Dashboard added to orchestration")

if __name__ == '__main__':
    # Simple command-line interface for testing
    import argparse
    
    parser = argparse.ArgumentParser(description='Data Quality Dashboard Integration')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to')
    parser.add_argument('--no-browser', action='store_true', help='Do not open browser')
    
    args = parser.parse_args()
    
    # Start dashboard
    process = start_dashboard(
        host=args.host,
        port=args.port,
        open_browser=not args.no_browser
    )
    
    try:
        # Keep running until interrupted
        while process.poll() is None:
            time.sleep(1)
    except KeyboardInterrupt:
        # Stop dashboard on interrupt
        stop_dashboard(process)
        print("\nDashboard stopped")