"""
Models for the Data Quality Dashboard.

This module defines database models for the dashboard.
"""

from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from datetime import datetime

# Initialize SQLAlchemy
db = SQLAlchemy()

class User(UserMixin, db.Model):
    """User model for authentication."""
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    role = db.Column(db.String(20), default='user')
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        """Set the user's password hash."""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if the provided password matches the hash."""
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'


class Alert(db.Model):
    """Alert model for storing data quality alerts."""
    
    id = db.Column(db.Integer, primary_key=True)
    type = db.Column(db.String(50), nullable=False)
    level = db.Column(db.String(20), nullable=False)
    message = db.Column(db.String(255), nullable=False)
    region = db.Column(db.String(10))
    value = db.Column(db.Float)
    threshold = db.Column(db.Float)
    is_resolved = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    resolved_at = db.Column(db.DateTime)
    resolved_by_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    resolved_by = db.relationship('User')
    
    def __repr__(self):
        return f'<Alert {self.id}: {self.message[:30]}...>'


class DataQualityMetric(db.Model):
    """Model for storing historical data quality metrics."""
    
    id = db.Column(db.Integer, primary_key=True)
    metric_type = db.Column(db.String(50), nullable=False)
    metric_name = db.Column(db.String(50), nullable=False)
    value = db.Column(db.Float, nullable=False)
    region = db.Column(db.String(10))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<DataQualityMetric {self.metric_type}/{self.metric_name}: {self.value}>'


class InterviewerPerformance(db.Model):
    """Model for storing interviewer performance metrics."""
    
    id = db.Column(db.Integer, primary_key=True)
    interviewer_id = db.Column(db.String(20), nullable=False)
    completed_interviews = db.Column(db.Integer, default=0)
    avg_duration = db.Column(db.Float)
    error_rate = db.Column(db.Float)
    productivity_score = db.Column(db.Float)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<InterviewerPerformance {self.interviewer_id}: {self.productivity_score}>'


class DashboardSettings(db.Model):
    """Model for storing user dashboard settings."""
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    user = db.relationship('User')
    settings_json = db.Column(db.Text)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<DashboardSettings for User {self.user_id}>'