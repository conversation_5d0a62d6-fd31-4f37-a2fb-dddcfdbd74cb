#!/usr/bin/env python3
"""
Run script for the Data Quality Dashboard.

This script runs the Flask application for the Data Quality Dashboard.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

from app import create_app
from models import db, User

def init_admin_user(app):
    """Initialize the admin user if it doesn't exist.

    Args:
        app: Flask application instance
    """
    with app.app_context():
        # Check if admin user exists
        admin_user = User.query.filter_by(username='admin').first()
        if admin_user is None:
            # Create admin user
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin_user.set_password('admin')  # Default password, should be changed

            # Add to database
            db.session.add(admin_user)
            db.session.commit()

            print("Created admin user with username 'admin' and password 'admin'")
            print("Please change this password immediately!")
        else:
            print("Admin user already exists")

def main():
    """Main entry point for the application."""
    # Get configuration environment from environment variable
    config_name = os.environ.get('FLASK_ENV', 'default')

    # Create Flask application
    app = create_app(config_name)

    # Initialize admin user
    init_admin_user(app)

    # Get host and port from environment variables or configuration
    host = os.environ.get('HOST', app.config.get('HOST', '0.0.0.0'))
    port = int(os.environ.get('PORT', app.config.get('PORT', 5000)))

    # Run application
    app.run(host=host, port=port, debug=app.config.get('DEBUG', False))

if __name__ == '__main__':
    main()