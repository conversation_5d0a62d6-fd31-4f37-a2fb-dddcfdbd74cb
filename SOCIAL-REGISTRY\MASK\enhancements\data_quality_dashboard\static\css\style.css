/* Main styles for Data Quality Dashboard */

/* Global styles */
body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main {
    flex: 1;
}

/* Card styles */
.card {
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
}

.card-header {
    border-radius: 8px 8px 0 0;
    font-weight: 500;
}

/* Dashboard metrics */
.metric-card {
    text-align: center;
    padding: 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

/* Alert styles */
.alert-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.alert-critical {
    background-color: #dc3545;
    color: white;
}

.alert-warning {
    background-color: #ffc107;
    color: #212529;
}

/* Chart containers */
.chart-container {
    height: 300px;
    position: relative;
}

/* Table styles */
.table-dashboard th {
    background-color: #f8f9fa;
    border-top: none;
}

.table-dashboard tr:hover {
    background-color: #f8f9fa;
}

/* Progress bars */
.progress {
    height: 20px;
    border-radius: 10px;
}

.progress-bar {
    text-align: center;
    line-height: 20px;
    font-weight: 600;
}

/* Sidebar (for larger screens) */
@media (min-width: 992px) {
    .sidebar {
        position: fixed;
        top: 56px;
        bottom: 0;
        left: 0;
        z-index: 1000;
        padding: 20px 0;
        overflow-x: hidden;
        overflow-y: auto;
        background-color: #f8f9fa;
        border-right: 1px solid #dee2e6;
    }
    
    .sidebar-sticky {
        position: sticky;
        top: 0;
        height: calc(100vh - 56px);
        padding-top: 0.5rem;
        overflow-x: hidden;
        overflow-y: auto;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .metric-value {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 250px;
    }
}

/* Animation for refreshing data */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Error page styles */
.error-container {
    text-align: center;
    padding: 50px 0;
}

.error-code {
    font-size: 6rem;
    font-weight: 700;
    color: #6c757d;
}

.error-message {
    font-size: 1.5rem;
    margin-bottom: 30px;
}

/* Notifications */
.notification-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 3px 6px;
    border-radius: 50%;
    font-size: 0.6rem;
}

/* Theme toggle button */
.theme-toggle {
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 34px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.theme-toggle:hover {
    background-color: #e9ecef;
}

/* Dark mode styles (for future implementation) */
.dark-mode {
    background-color: #343a40;
    color: #f8f9fa;
}

.dark-mode .card {
    background-color: #454d55;
    border-color: #495057;
}

.dark-mode .card-header {
    background-color: #495057;
    color: #f8f9fa;
}

.dark-mode .table {
    color: #f8f9fa;
}

.dark-mode .table-dashboard th {
    background-color: #495057;
}

.dark-mode .table-dashboard tr:hover {
    background-color: #495057;
}

/* Profile page styles */
.profile-header {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-right: 20px;
}

/* Settings page styles */
.settings-section {
    margin-bottom: 30px;
}

.settings-section h3 {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* Alert page styles */
.alert-item {
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    border-left: 5px solid;
}

.alert-item.critical {
    border-left-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.alert-item.warning {
    border-left-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

/* Data explorer styles */
.filter-bar {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.data-grid {
    height: 500px;
    overflow-y: auto;
}