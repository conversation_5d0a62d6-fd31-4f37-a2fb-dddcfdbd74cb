/**
 * Main JavaScript for the Data Quality Dashboard
 */

// Global variables
let refreshTimer = null;
let refreshInterval = 300000; // Default: 5 minutes

/**
 * Initialize the dashboard
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    initTooltips();
    
    // Set up refresh timer
    setupRefreshTimer();
    
    // Set up event listeners
    setupEventListeners();
    
    // Update last updated time
    updateLastUpdated();
});

/**
 * Initialize Bootstrap tooltips
 */
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Set up the refresh timer
 */
function setupRefreshTimer() {
    // Get refresh interval from data attribute if available
    const refreshElement = document.getElementById('refreshInterval');
    if (refreshElement) {
        refreshInterval = parseInt(refreshElement.dataset.interval) * 1000;
    }
    
    // Clear existing timer
    if (refreshTimer) {
        clearTimeout(refreshTimer);
    }
    
    // Set new timer
    refreshTimer = setTimeout(function() {
        // Get refresh button
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.click();
        } else {
            // No refresh button, reload the page
            location.reload();
        }
    }, refreshInterval);
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // Refresh button
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // Add spinning animation
            const icon = refreshBtn.querySelector('i');
            if (icon) {
                icon.classList.add('spin');
            }
            
            // Disable button during refresh
            refreshBtn.disabled = true;
            
            // Call refresh API
            fetch('/api/v1/refresh', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Data refreshed:', data);
                
                // Reload page to show updated data
                location.reload();
            })
            .catch(error => {
                console.error('Error refreshing data:', error);
                
                // Remove spinning animation
                if (icon) {
                    icon.classList.remove('spin');
                }
                
                // Re-enable button
                refreshBtn.disabled = false;
                
                // Show error alert
                showAlert('Error refreshing data: ' + error.message, 'danger');
            });
        });
    }
    
    // Alert dismissal
    const alertCloseButtons = document.querySelectorAll('.alert .btn-close');
    alertCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const alert = this.closest('.alert');
            alert.classList.remove('show');
            setTimeout(function() {
                alert.remove();
            }, 150);
        });
    });
}

/**
 * Update the last updated timestamp
 */
function updateLastUpdated() {
    const lastUpdatedElement = document.getElementById('lastUpdated');
    if (lastUpdatedElement) {
        const now = new Date();
        lastUpdatedElement.textContent = 'Last updated: ' + now.toLocaleTimeString();
    }
}

/**
 * Show an alert message
 * 
 * @param {string} message - The message to display
 * @param {string} type - The alert type (success, info, warning, danger)
 */
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    
    // Add content
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add to page
    const main = document.querySelector('main');
    if (main) {
        main.insertBefore(alertDiv, main.firstChild);
    } else {
        document.body.insertBefore(alertDiv, document.body.firstChild);
    }
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        alertDiv.classList.remove('show');
        setTimeout(function() {
            alertDiv.remove();
        }, 150);
    }, 5000);
}

/**
 * Format a number as a percentage
 * 
 * @param {number} value - The value to format (0-1)
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted percentage
 */
function formatPercent(value, decimals = 1) {
    return (value * 100).toFixed(decimals) + '%';
}

/**
 * Format bytes to a human-readable size
 * 
 * @param {number} bytes - The size in bytes
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted size
 */
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Get the color class for a value
 * 
 * @param {number} value - The value (0-1)
 * @param {boolean} inverse - Whether higher is worse
 * @returns {string} CSS class name
 */
function getColorClass(value, inverse = false) {
    if (inverse) value = 1 - value;
    
    if (value >= 0.9) return 'success';
    if (value >= 0.7) return 'info';
    if (value >= 0.5) return 'warning';
    return 'danger';
}

/**
 * Create a chart using Plotly
 * 
 * @param {string} elementId - ID of the element to render the chart in
 * @param {Array} data - Chart data
 * @param {Object} layout - Chart layout
 * @param {Object} config - Chart configuration
 */
function createChart(elementId, data, layout, config = {}) {
    const element = document.getElementById(elementId);
    if (!element) {
        console.error(`Element with ID "${elementId}" not found`);
        return;
    }
    
    // Default config
    const defaultConfig = {
        responsive: true,
        displayModeBar: false,
        ...config
    };
    
    // Create chart
    Plotly.newPlot(elementId, data, layout, defaultConfig);
}