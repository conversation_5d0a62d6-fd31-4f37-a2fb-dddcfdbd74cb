{% extends "base.html" %}

{% block title %}Data Quality Alerts - Social Registry Data Quality{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1><i class="fas fa-exclamation-triangle me-2"></i>Data Quality Alerts</h1>
            <div>
                <button id="refreshBtn" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <span class="ms-2 text-muted" id="lastUpdated">Last updated: Just now</span>
            </div>
        </div>
        <p class="lead">View and manage data quality alerts for the Social Registry System</p>
    </div>
</div>

<!-- Alert filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter me-1"></i>Filter Alerts</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="typeFilter">Alert Type</label>
                            <select id="typeFilter" class="form-select">
                                <option value="all">All Types</option>
                                <option value="completeness">Completeness</option>
                                <option value="consistency">Consistency</option>
                                <option value="timeliness">Timeliness</option>
                                <option value="accuracy">Accuracy</option>
                                <option value="interviewer_performance">Interviewer Performance</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="levelFilter">Alert Level</label>
                            <select id="levelFilter" class="form-select">
                                <option value="all">All Levels</option>
                                <option value="warning">Warning</option>
                                <option value="critical">Critical</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="regionFilter">Region</label>
                            <select id="regionFilter" class="form-select">
                                <option value="all">All Regions</option>
                                <option value="01">Region 01</option>
                                <option value="02">Region 02</option>
                                <option value="03">Region 03</option>
                                <option value="04">Region 04</option>
                                <option value="05">Region 05</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button id="applyFilters" class="btn btn-primary w-100">Apply Filters</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alert list -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-1"></i>Alert List</h5>
                <span class="badge bg-danger">{{ alert_count }} Alerts</span>
            </div>
            <div class="card-body">
                {% if alerts %}
                <div class="list-group">
                    {% for alert in alerts %}
                    <div class="list-group-item list-group-item-{{ 'danger' if alert['level'] == 'critical' else 'warning' }}">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ alert['message'] }}</h5>
                            <small>{{ alert['type']|capitalize }}</small>
                        </div>
                        <p class="mb-1">
                            {% if 'value' in alert and 'threshold' in alert %}
                                Value: {{ '%.1f%%'|format(alert['value'] * 100) }} (Threshold: {{ '%.1f%%'|format(alert['threshold'] * 100) }})
                            {% elif 'rate' in alert and 'threshold' in alert %}
                                Rate: {{ '%.1f%%'|format(alert['rate'] * 100) }} (Threshold: {{ '%.1f%%'|format(alert['threshold'] * 100) }})
                            {% else %}
                                Details: {{ alert['level']|capitalize }}
                            {% endif %}
                        </p>
                        <div class="d-flex justify-content-end">
                            <button class="btn btn-sm btn-outline-secondary me-2 view-details-btn" data-alert-id="{{ loop.index }}">
                                <i class="fas fa-eye me-1"></i>View Details
                            </button>
                            <button class="btn btn-sm btn-outline-success resolve-btn" data-alert-id="{{ loop.index }}">
                                <i class="fas fa-check me-1"></i>Mark as Resolved
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-1"></i>No alerts found. All data quality metrics are within acceptable thresholds.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Update last updated time
    function updateLastUpdated() {
        const now = new Date();
        document.getElementById('lastUpdated').textContent = 'Last updated: ' + now.toLocaleTimeString();
    }

    // Refresh data
    document.getElementById('refreshBtn').addEventListener('click', function() {
        fetch('/api/v1/refresh', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            console.log('Data refreshed:', data);
            // Reload the page to show updated data
            location.reload();
        })
        .catch(error => {
            console.error('Error refreshing data:', error);
        });
    });

    // Apply filters
    document.getElementById('applyFilters').addEventListener('click', function() {
        const typeFilter = document.getElementById('typeFilter').value;
        const levelFilter = document.getElementById('levelFilter').value;
        const regionFilter = document.getElementById('regionFilter').value;
        
        // Build query string
        const params = new URLSearchParams();
        if (typeFilter !== 'all') params.append('type', typeFilter);
        if (levelFilter !== 'all') params.append('level', levelFilter);
        if (regionFilter !== 'all') params.append('region', regionFilter);
        
        // Reload page with filters
        window.location.href = '/dashboard/alerts?' + params.toString();
    });

    // View details button
    document.querySelectorAll('.view-details-btn').forEach(button => {
        button.addEventListener('click', function() {
            const alertId = this.getAttribute('data-alert-id');
            // In a real implementation, this would open a modal with details
            alert('View details for alert ' + alertId + ' (Not implemented in demo)');
        });
    });

    // Resolve button
    document.querySelectorAll('.resolve-btn').forEach(button => {
        button.addEventListener('click', function() {
            const alertId = this.getAttribute('data-alert-id');
            // In a real implementation, this would call an API to resolve the alert
            alert('Mark alert ' + alertId + ' as resolved (Not implemented in demo)');
        });
    });

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        updateLastUpdated();
        
        // Set filter values from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('type')) document.getElementById('typeFilter').value = urlParams.get('type');
        if (urlParams.has('level')) document.getElementById('levelFilter').value = urlParams.get('level');
        if (urlParams.has('region')) document.getElementById('regionFilter').value = urlParams.get('region');
    });
</script>
{% endblock %}
