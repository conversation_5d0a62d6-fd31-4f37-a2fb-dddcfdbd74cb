{% extends "base.html" %}

{% block title %}Dashboard Overview - Social Registry Data Quality{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1><i class="fas fa-tachometer-alt me-2"></i>Data Quality Dashboard</h1>
            <div>
                <button id="refreshBtn" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <span class="ms-2 text-muted" id="lastUpdated">Last updated: Just now</span>
            </div>
        </div>
        <p class="lead">Overview of data quality metrics for the Social Registry System</p>
    </div>
</div>

<!-- Alert notifications -->
{% if alerts %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Data Quality Alerts ({{ alert_count }})
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for alert in alerts[:5] %}
                    <div class="list-group-item list-group-item-{{ 'danger' if alert['level'] == 'critical' else 'warning' }}">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ alert['message'] }}</h5>
                            <small>{{ alert['type']|capitalize }}</small>
                        </div>
                        <p class="mb-1">
                            {% if 'value' in alert and 'threshold' in alert %}
                                Value: {{ '%.1f%%'|format(alert['value'] * 100) }} (Threshold: {{ '%.1f%%'|format(alert['threshold'] * 100) }})
                            {% elif 'rate' in alert and 'threshold' in alert %}
                                Rate: {{ '%.1f%%'|format(alert['rate'] * 100) }} (Threshold: {{ '%.1f%%'|format(alert['threshold'] * 100) }})
                            {% else %}
                                Details: {{ alert['level']|capitalize }}
                            {% endif %}
                        </p>
                    </div>
                    {% endfor %}
                </div>
                {% if alert_count > 5 %}
                <div class="mt-2 text-end">
                    <a href="{{ url_for('dashboard.alerts') }}" class="btn btn-outline-danger btn-sm">
                        View all {{ alert_count }} alerts
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Summary metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card h-100 border-primary">
            <div class="card-body text-center">
                <h1 class="display-4 text-primary" id="completenessRate">94%</h1>
                <h5 class="card-title">Completeness</h5>
                <p class="card-text">Overall completion rate</p>
                <a href="{{ url_for('dashboard.completeness') }}" class="btn btn-sm btn-outline-primary">Details</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card h-100 border-success">
            <div class="card-body text-center">
                <h1 class="display-4 text-success" id="consistencyRate">96%</h1>
                <h5 class="card-title">Consistency</h5>
                <p class="card-text">Data consistency rate</p>
                <a href="{{ url_for('dashboard.consistency') }}" class="btn btn-sm btn-outline-success">Details</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card h-100 border-info">
            <div class="card-body text-center">
                <h1 class="display-4 text-info" id="timelinessRate">43<small>min</small></h1>
                <h5 class="card-title">Timeliness</h5>
                <p class="card-text">Average interview duration</p>
                <a href="{{ url_for('dashboard.timeliness') }}" class="btn btn-sm btn-outline-info">Details</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card h-100 border-warning">
            <div class="card-body text-center">
                <h1 class="display-4 text-warning" id="accuracyRate">97%</h1>
                <h5 class="card-title">Accuracy</h5>
                <p class="card-text">Data accuracy rate</p>
                <a href="{{ url_for('dashboard.accuracy') }}" class="btn btn-sm btn-outline-warning">Details</a>
            </div>
        </div>
    </div>
</div>

<!-- Data summary -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-1"></i>Data Collection Progress</h5>
            </div>
            <div class="card-body">
                <div id="regionChart" style="height: 300px;"></div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-1"></i>Data Summary</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Total Data Files
                        <span class="badge bg-primary rounded-pill">{{ summary.total_files }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Regions
                        <span class="badge bg-primary rounded-pill">{{ summary.regions|length }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Total Size
                        <span class="badge bg-primary rounded-pill">{{ (summary.total_size_bytes / 1048576)|round(2) }} MB</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Interviewer performance -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-tie me-1"></i>Top Interviewer Performance</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Interviewer ID</th>
                                <th>Completed Interviews</th>
                                <th>Avg Duration</th>
                                <th>Error Rate</th>
                                <th>Productivity Score</th>
                            </tr>
                        </thead>
                        <tbody id="interviewerTable">
                            <!-- Will be filled by JavaScript -->
                        </tbody>
                    </table>
                </div>
                <div class="text-end">
                    <a href="{{ url_for('dashboard.interviewer_performance') }}" class="btn btn-outline-primary btn-sm">
                        View all interviewers
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Update last updated time
    function updateLastUpdated() {
        const now = new Date();
        document.getElementById('lastUpdated').textContent = 'Last updated: ' + now.toLocaleTimeString();
    }

    // Refresh data
    document.getElementById('refreshBtn').addEventListener('click', function() {
        fetch('/api/v1/refresh', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            console.log('Data refreshed:', data);
            // Reload the page to show updated data
            location.reload();
        })
        .catch(error => {
            console.error('Error refreshing data:', error);
        });
    });

    // Auto-refresh timer
    let refreshInterval = {{ refresh_interval|default(300) }} * 1000; // Convert seconds to milliseconds
    let refreshTimer = null;

    function startRefreshTimer() {
        if (refreshTimer) {
            clearTimeout(refreshTimer);
        }
        refreshTimer = setTimeout(function() {
            document.getElementById('refreshBtn').click();
        }, refreshInterval);
    }

    // Load initial data
    document.addEventListener('DOMContentLoaded', function() {
        updateLastUpdated();
        loadQualityMetrics();
        loadInterviewerData();
        renderRegionChart();
        startRefreshTimer();
    });

    // Load quality metrics
    function loadQualityMetrics() {
        fetch('/api/v1/quality')
            .then(response => response.json())
            .then(data => {
                // Update completeness
                document.getElementById('completenessRate').textContent = (data.completeness.overall_completion_rate * 100).toFixed(0) + '%';

                // Update consistency
                document.getElementById('consistencyRate').textContent = (data.consistency.overall_consistency_rate * 100).toFixed(0) + '%';

                // Update timeliness
                document.getElementById('timelinessRate').innerHTML = data.timeliness.average_interview_duration_minutes.toFixed(0) + '<small>min</small>';

                // Update accuracy
                document.getElementById('accuracyRate').textContent = ((1 - data.accuracy.outlier_rate) * 100).toFixed(0) + '%';
            })
            .catch(error => {
                console.error('Error loading quality metrics:', error);
            });
    }

    // Load interviewer data
    function loadInterviewerData() {
        fetch('/api/v1/interviewer-performance')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('interviewerTable');
                tableBody.innerHTML = '';

                // Sort interviewers by productivity score
                const interviewers = data.interviewers.sort((a, b) => b.productivity_score - a.productivity_score);

                // Display top 5 interviewers
                interviewers.slice(0, 5).forEach(interviewer => {
                    const row = document.createElement('tr');

                    row.innerHTML = `
                        <td>${interviewer.id}</td>
                        <td>${interviewer.completed_interviews}</td>
                        <td>${interviewer.avg_duration_minutes.toFixed(1)} min</td>
                        <td>${(interviewer.error_rate * 100).toFixed(1)}%</td>
                        <td>
                            <div class="progress">
                                <div class="progress-bar ${getProgressBarColor(interviewer.productivity_score)}"
                                     role="progressbar"
                                     style="width: ${interviewer.productivity_score * 100}%">
                                    ${(interviewer.productivity_score * 100).toFixed(0)}%
                                </div>
                            </div>
                        </td>
                    `;

                    tableBody.appendChild(row);
                });
            })
            .catch(error => {
                console.error('Error loading interviewer data:', error);
            });
    }

    // Get progress bar color based on value
    function getProgressBarColor(value) {
        if (value >= 0.9) return 'bg-success';
        if (value >= 0.7) return 'bg-info';
        if (value >= 0.5) return 'bg-warning';
        return 'bg-danger';
    }

    // Render region chart
    function renderRegionChart() {
        fetch('/api/v1/summary')
            .then(response => response.json())
            .then(data => {
                const regions = Object.keys(data.regions).sort();
                const counts = regions.map(region => data.regions[region].count);

                const trace = {
                    x: regions.map(r => 'Region ' + r),
                    y: counts,
                    type: 'bar',
                    marker: {
                        color: 'rgba(54, 162, 235, 0.7)',
                        line: {
                            color: 'rgba(54, 162, 235, 1)',
                            width: 1
                        }
                    }
                };

                const layout = {
                    margin: { t: 10, b: 40, l: 40, r: 10 },
                    xaxis: {
                        title: 'Region'
                    },
                    yaxis: {
                        title: 'Number of Files'
                    }
                };

                Plotly.newPlot('regionChart', [trace], layout);
            })
            .catch(error => {
                console.error('Error rendering region chart:', error);
            });
    }
</script>
{% endblock %}