"""
Entry point for the Mobile Integration application.
"""

import logging
import os
from src.app import create_app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

logger.info("Starting Mobile Integration application")

# Create Flask application
app = create_app()

if __name__ == '__main__':
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'True').lower() == 'true'

    logger.info(f"Running application on {host}:{port} (debug={debug})")
    app.run(host=host, port=port, debug=debug)
