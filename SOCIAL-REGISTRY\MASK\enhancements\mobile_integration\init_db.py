"""
Database initialization script for the Mobile Integration module.

This script initializes the database with default data.
"""

import os
import sys
from datetime import datetime

from src.app import create_app
from src.models.database import db
from src.models.user import User
from src.utils.logging import get_logger

# Create logger
logger = get_logger('init_db')


def init_db():
    """Initialize the database with default data."""
    # Create Flask app
    app = create_app()
    
    with app.app_context():
        # Create tables
        db.create_all()
        
        # Check if admin user exists
        admin = User.query.filter_by(username='admin').first()
        if admin:
            logger.info('Admin user already exists')
        else:
            # Create admin user
            admin = User(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                role='admin'
            )
            db.session.add(admin)
            db.session.commit()
            logger.info('Admin user created')
        
        # Create test user if in development mode
        if app.config['DEBUG']:
            test_user = User.query.filter_by(username='fieldworker').first()
            if test_user:
                logger.info('Test user already exists')
            else:
                # Create test user
                test_user = User(
                    username='fieldworker',
                    email='<EMAIL>',
                    password='field123',
                    role='fieldworker'
                )
                db.session.add(test_user)
                db.session.commit()
                logger.info('Test user created')


if __name__ == '__main__':
    init_db()
