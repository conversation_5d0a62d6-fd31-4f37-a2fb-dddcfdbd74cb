"""
Initial database migration.

This migration creates the initial database schema for the Mobile Integration module.
"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '001_initial_migration'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Upgrade database schema."""
    # Create users table
    op.create_table(
        'users',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('username', sa.String(64), unique=True, nullable=False),
        sa.Column('email', sa.String(120), unique=True, nullable=False),
        sa.Column('password_hash', sa.String(256), nullable=False),
        sa.Column('role', sa.String(20), default='fieldworker'),
        sa.Column('is_active', sa.Bo<PERSON>an, default=True),
        sa.Column('created_at', sa.DateTime),
        sa.Column('updated_at', sa.DateTime),
        sa.Column('last_login', sa.DateTime),
        sa.Column('odk_username', sa.String(64)),
        sa.<PERSON>umn('odk_password_hash', sa.String(256)),
        sa.<PERSON>umn('surveycto_username', sa.String(64)),
        sa.Column('surveycto_password_hash', sa.String(256)),
        sa.Column('kobo_username', sa.String(64)),
        sa.Column('kobo_password_hash', sa.String(256))
    )
    
    # Create forms table
    op.create_table(
        'forms',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('title', sa.String(255), nullable=False),
        sa.Column('description', sa.Text),
        sa.Column('version', sa.String(20), nullable=False),
        sa.Column('created_at', sa.DateTime),
        sa.Column('updated_at', sa.DateTime),
        sa.Column('cspro_file_path', sa.String(255)),
        sa.Column('xlsform_file_path', sa.String(255)),
        sa.Column('form_schema', sa.Text),
        sa.Column('creator_id', sa.String(36), sa.ForeignKey('users.id')),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('is_published', sa.Boolean, default=False)
    )
    
    # Create form_deployments table
    op.create_table(
        'form_deployments',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('form_id', sa.String(36), sa.ForeignKey('forms.id'), nullable=False),
        sa.Column('platform', sa.String(20), nullable=False),
        sa.Column('platform_form_id', sa.String(255)),
        sa.Column('deployed_at', sa.DateTime),
        sa.Column('deployed_by', sa.String(36), sa.ForeignKey('users.id')),
        sa.Column('status', sa.String(20), default='pending'),
        sa.Column('status_message', sa.Text)
    )
    
    # Create submissions table
    op.create_table(
        'submissions',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('form_id', sa.String(36), sa.ForeignKey('forms.id'), nullable=False),
        sa.Column('user_id', sa.String(36), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('platform', sa.String(20), nullable=False),
        sa.Column('platform_submission_id', sa.String(255)),
        sa.Column('data', sa.Text),
        sa.Column('submitted_at', sa.DateTime, nullable=False),
        sa.Column('received_at', sa.DateTime),
        sa.Column('status', sa.String(20), default='received'),
        sa.Column('status_message', sa.Text),
        sa.Column('latitude', sa.Float),
        sa.Column('longitude', sa.Float),
        sa.Column('accuracy', sa.Float),
        sa.Column('is_synced', sa.Boolean, default=False),
        sa.Column('synced_at', sa.DateTime),
        sa.Column('sync_status', sa.String(20))
    )
    
    # Create sync_logs table
    op.create_table(
        'sync_logs',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('user_id', sa.String(36), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('device_id', sa.String(255)),
        sa.Column('sync_type', sa.String(20), nullable=False),
        sa.Column('started_at', sa.DateTime),
        sa.Column('completed_at', sa.DateTime),
        sa.Column('status', sa.String(20), default='in_progress'),
        sa.Column('items_sent', sa.Integer, default=0),
        sa.Column('items_received', sa.Integer, default=0),
        sa.Column('error_message', sa.Text)
    )
    
    # Create indexes
    op.create_index('idx_users_username', 'users', ['username'])
    op.create_index('idx_users_email', 'users', ['email'])
    op.create_index('idx_forms_creator', 'forms', ['creator_id'])
    op.create_index('idx_forms_active', 'forms', ['is_active'])
    op.create_index('idx_deployments_form', 'form_deployments', ['form_id'])
    op.create_index('idx_deployments_platform', 'form_deployments', ['platform'])
    op.create_index('idx_submissions_form', 'submissions', ['form_id'])
    op.create_index('idx_submissions_user', 'submissions', ['user_id'])
    op.create_index('idx_submissions_platform', 'submissions', ['platform'])
    op.create_index('idx_submissions_synced', 'submissions', ['is_synced'])
    op.create_index('idx_sync_logs_user', 'sync_logs', ['user_id'])
    op.create_index('idx_sync_logs_status', 'sync_logs', ['status'])


def downgrade():
    """Downgrade database schema."""
    # Drop tables in reverse order
    op.drop_table('sync_logs')
    op.drop_table('submissions')
    op.drop_table('form_deployments')
    op.drop_table('forms')
    op.drop_table('users')
