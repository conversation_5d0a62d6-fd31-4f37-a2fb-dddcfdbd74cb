"""
Setup script for the Mobile Integration module.

This script installs the Mobile Integration module as a Python package.
"""

from setuptools import setup, find_packages

with open('README.md', 'r') as f:
    long_description = f.read()

with open('requirements.txt', 'r') as f:
    requirements = f.read().splitlines()

setup(
    name='mobile_integration',
    version='0.1.0',
    description='Mobile Data Collection Integration for Social Registry System',
    long_description=long_description,
    long_description_content_type='text/markdown',
    author='Social Registry Team',
    author_email='<EMAIL>',
    url='https://github.com/example/social-registry',
    packages=find_packages(),
    include_package_data=True,
    install_requires=requirements,
    classifiers=[
        'Development Status :: 3 - Alpha',
        'Intended Audience :: Developers',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
    ],
    python_requires='>=3.8',
    entry_points={
        'console_scripts': [
            'mobile-integration=src.app:main',
        ],
    },
)
