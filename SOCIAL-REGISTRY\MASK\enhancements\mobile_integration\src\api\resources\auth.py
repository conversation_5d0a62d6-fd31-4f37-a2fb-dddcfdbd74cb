"""
Authentication resources for the Mobile Integration API.

This module provides the API endpoints for authentication.
"""

from flask import request, current_app
from flask_restful import Resource
from flask_jwt_extended import (
    create_access_token, create_refresh_token, 
    jwt_required, get_jwt_identity
)

from ...models.user import User
from ...utils.logging import get_logger

# Create logger
logger = get_logger('api.resources.auth')


class LoginResource(Resource):
    """Resource for user login and token generation."""
    
    def post(self):
        """Authenticate user and generate access token.
        
        Returns:
            JSON response with access token
        """
        # Get credentials from request
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        # Validate input
        if not username or not password:
            return {
                'message': 'Username and password are required'
            }, 400
        
        # Authenticate user
        user = User.authenticate(username, password)
        if not user:
            logger.warning(f'Failed login attempt for user: {username}')
            return {
                'message': 'Invalid credentials'
            }, 401
        
        # Generate tokens
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)
        
        logger.info(f'User logged in: {username}')
        
        # Return tokens
        return {
            'message': 'Login successful',
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': {
                'id': user.id,
                'username': user.username,
                'role': user.role
            }
        }, 200


class RefreshTokenResource(Resource):
    """Resource for refreshing access tokens."""
    
    @jwt_required(refresh=True)
    def post(self):
        """Refresh access token using refresh token.
        
        Returns:
            JSON response with new access token
        """
        # Get user identity from refresh token
        current_user_id = get_jwt_identity()
        
        # Generate new access token
        access_token = create_access_token(identity=current_user_id)
        
        logger.info(f'Token refreshed for user ID: {current_user_id}')
        
        # Return new access token
        return {
            'message': 'Token refreshed',
            'access_token': access_token
        }, 200
