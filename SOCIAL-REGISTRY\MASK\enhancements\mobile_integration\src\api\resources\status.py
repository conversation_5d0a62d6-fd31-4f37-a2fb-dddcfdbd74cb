"""
Status resources for the Mobile Integration API.

This module provides the API endpoints for system status.
"""

import os
from datetime import datetime
from flask import current_app
from flask_restful import Resource
from flask_jwt_extended import jwt_required

from ...utils.logging import get_logger
from ...models.database import db

# Create logger
logger = get_logger('api.resources.status')


class StatusResource(Resource):
    """Resource for system status information."""
    
    def get(self):
        """Get system status information.
        
        Returns:
            JSON response with system status
        """
        # Check database connection
        db_status = 'ok'
        try:
            db.session.execute('SELECT 1')
        except Exception as e:
            db_status = f'error: {str(e)}'
            logger.error(f'Database connection error: {str(e)}')
        
        # Get version information
        version = os.environ.get('VERSION', current_app.config.get('API_VERSION', '1.0'))
        
        # Return status information
        return {
            'status': 'online',
            'version': version,
            'timestamp': datetime.utcnow().isoformat(),
            'database': db_status,
            'environment': os.environ.get('FLASK_ENV', 'development')
        }, 200
    
    @jwt_required()
    def post(self):
        """Get detailed system status information (requires authentication).
        
        Returns:
            JSON response with detailed system status
        """
        # Get basic status
        basic_status = self.get()[0]
        
        # Add detailed information
        detailed_status = {
            **basic_status,
            'config': {
                'sync_interval': current_app.config.get('SYNC_INTERVAL'),
                'max_sync_batch_size': current_app.config.get('MAX_SYNC_BATCH_SIZE'),
                'api_prefix': current_app.config.get('API_PREFIX')
            },
            'platforms': {
                'odk': bool(current_app.config.get('ODK_SERVER_URL')),
                'surveycto': bool(current_app.config.get('SURVEYCTO_SERVER_URL')),
                'kobo': bool(current_app.config.get('KOBO_SERVER_URL'))
            }
        }
        
        logger.info('Detailed status requested')
        
        return detailed_status, 200
