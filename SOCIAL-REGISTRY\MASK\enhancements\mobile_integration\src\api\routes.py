"""
API routes for the Mobile Integration module.

This module defines the API endpoints and route registration.
"""

from flask import Blueprint, jsonify, current_app
from flask_restful import Api

from .resources.auth import LoginResource, RefreshTokenResource
from .resources.forms import FormListResource, FormResource, FormDeployResource
from .resources.data import DataSubmissionResource, DataSyncResource
from .resources.users import UserListResource, UserResource
from .resources.status import StatusResource
from ..utils.logging import get_logger

# Create logger
logger = get_logger('api.routes')

# Create blueprint
api_bp = Blueprint('api', __name__)


def register_routes(app):
    """Register API routes with the Flask application.
    
    Args:
        app: Flask application instance
    """
    # Get API prefix from config
    api_prefix = app.config.get('API_PREFIX', '/api/v1')
    
    # Register blueprint with app
    app.register_blueprint(api_bp, url_prefix=api_prefix)
    
    # Create API
    api = Api(api_bp)
    
    # Add resources
    api.add_resource(LoginResource, '/auth/login')
    api.add_resource(RefreshTokenResource, '/auth/refresh')
    
    api.add_resource(FormListResource, '/forms')
    api.add_resource(FormResource, '/forms/<form_id>')
    api.add_resource(FormDeployResource, '/forms/<form_id>/deploy')
    
    api.add_resource(DataSubmissionResource, '/data/submit')
    api.add_resource(DataSyncResource, '/data/sync')
    
    api.add_resource(UserListResource, '/users')
    api.add_resource(UserResource, '/users/<user_id>')
    
    api.add_resource(StatusResource, '/status')
    
    # Register error handlers
    register_error_handlers(app)
    
    logger.info(f'API routes registered with prefix: {api_prefix}')


def register_error_handlers(app):
    """Register error handlers with the Flask application.
    
    Args:
        app: Flask application instance
    """
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'error': 'Bad Request',
            'message': str(error)
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({
            'error': 'Unauthorized',
            'message': 'Authentication required'
        }), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({
            'error': 'Forbidden',
            'message': 'You do not have permission to access this resource'
        }), 403
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'error': 'Not Found',
            'message': 'The requested resource was not found'
        }), 404
    
    @app.errorhandler(500)
    def server_error(error):
        logger.error(f'Server error: {str(error)}')
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500
