"""
Main application entry point for the Mobile Integration module.

This module initializes the Flask application and sets up the API endpoints.
"""

import os
import logging
from flask import Flask, jsonify, render_template, send_from_directory, request
from flask_cors import CORS
from flask_jwt_extended import JWTManager

from .config.settings import load_config


def create_app(config_name=None):
    """Create and configure the Flask application.

    Args:
        config_name: Configuration environment to use

    Returns:
        Configured Flask application
    """
    app = Flask(__name__,
                static_folder='static',
                template_folder='templates')

    # Load configuration
    config = load_config(config_name)
    app.config.from_object(config)

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Enable CORS
    CORS(app)

    # Initialize JWT
    jwt = JWTManager(app)

    # Register routes
    register_routes(app)

    return app


def register_routes(app):
    """Register routes with the Flask application."""
    logger = logging.getLogger('routes')

    @app.route('/')
    def index():
        """Render the index page."""
        logger.info("Rendering index page")
        return render_template('index.html')

    @app.route('/static/<path:filename>')
    def serve_static(filename):
        """Serve static files."""
        logger.debug(f"Serving static file: {filename}")
        return send_from_directory(app.static_folder, filename)

    @app.route('/api/status')
    def status():
        """Return API status."""
        logger.info("API status requested")
        return jsonify({
            'status': 'online',
            'version': '0.1.0',
            'message': 'Mobile Integration API is running'
        })

    @app.route('/api/auth/login', methods=['POST'])
    def api_login():
        """Handle login API requests."""
        logger.info("Login API requested")

        # Get request data
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        # Simple validation
        if not username or not password:
            logger.warning(f"Login attempt with missing credentials")
            return jsonify({
                'error': 'Missing credentials',
                'message': 'Username and password are required'
            }), 400

        # For demo purposes, accept admin/admin
        if username == 'admin' and password == 'admin':
            logger.info(f"Successful login for user: {username}")
            return jsonify({
                'message': 'Login successful',
                'access_token': 'demo-token-12345',
                'user': {
                    'username': username,
                    'role': 'admin'
                }
            })

        # Login failed
        logger.warning(f"Failed login attempt for user: {username}")
        return jsonify({
            'error': 'Invalid credentials',
            'message': 'Invalid username or password'
        }), 401

    @app.route('/login')
    def login():
        """Render the login page."""
        logger.info("Login page requested")
        return render_template('login.html')

    @app.route('/dashboard')
    def dashboard():
        """Render the dashboard page."""
        logger.info("Dashboard page requested")
        return render_template('dashboard.html')

    # Register error handlers
    @app.errorhandler(404)
    def not_found(error):
        logger.warning(f"404 error: {request.path}")
        return jsonify({
            'error': 'Not Found',
            'message': f"The requested URL {request.path} was not found on this server"
        }), 404

    @app.errorhandler(500)
    def server_error(error):
        logger.error(f"500 error: {error}")
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred'
        }), 500

    logger.info("Routes registered successfully")


def main():
    """Run the application."""
    app = create_app()
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'False').lower() == 'true'

    app.run(host=host, port=port, debug=debug)


if __name__ == '__main__':
    main()
