"""
Configuration settings for the Mobile Integration module.

This module provides the configuration classes and loading functions.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class BaseConfig:
    """Base configuration class."""
    
    # Application settings
    APP_NAME = 'Mobile Integration'
    SECRET_KEY = os.environ.get('SECRET_KEY', 'mobile-integration-secret-key')
    DEBUG = False
    TESTING = False
    
    # API settings
    API_PREFIX = '/api/v1'
    API_TITLE = 'Mobile Integration API'
    API_VERSION = '1.0'
    API_DESCRIPTION = 'API for Mobile Data Collection Integration'
    
    # Database settings
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        'DATABASE_URI', 
        'sqlite:///mobile_integration.db'
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Authentication settings
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'jwt-secret-key')
    JWT_ACCESS_TOKEN_EXPIRES = 3600  # 1 hour
    
    # Synchronization settings
    SYNC_INTERVAL = int(os.environ.get('SYNC_INTERVAL', 15))  # minutes
    MAX_SYNC_BATCH_SIZE = int(os.environ.get('MAX_SYNC_BATCH_SIZE', 1000))
    
    # Mobile platform settings
    ODK_SERVER_URL = os.environ.get('ODK_SERVER_URL', '')
    ODK_USERNAME = os.environ.get('ODK_USERNAME', '')
    ODK_PASSWORD = os.environ.get('ODK_PASSWORD', '')
    
    SURVEYCTO_SERVER_URL = os.environ.get('SURVEYCTO_SERVER_URL', '')
    SURVEYCTO_USERNAME = os.environ.get('SURVEYCTO_USERNAME', '')
    SURVEYCTO_PASSWORD = os.environ.get('SURVEYCTO_PASSWORD', '')
    
    KOBO_SERVER_URL = os.environ.get('KOBO_SERVER_URL', '')
    KOBO_USERNAME = os.environ.get('KOBO_USERNAME', '')
    KOBO_PASSWORD = os.environ.get('KOBO_PASSWORD', '')
    
    # Form conversion settings
    FORM_UPLOAD_FOLDER = os.environ.get('FORM_UPLOAD_FOLDER', 'uploads')
    FORM_OUTPUT_FOLDER = os.environ.get('FORM_OUTPUT_FOLDER', 'forms')
    
    # Logging settings
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'


class DevelopmentConfig(BaseConfig):
    """Development configuration."""
    
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        'DATABASE_URI', 
        'sqlite:///mobile_integration_dev.db'
    )


class TestingConfig(BaseConfig):
    """Testing configuration."""
    
    TESTING = True
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        'DATABASE_URI', 
        'sqlite:///:memory:'
    )


class ProductionConfig(BaseConfig):
    """Production configuration."""
    
    # Production should use a more secure database
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        'DATABASE_URI', 
        'postgresql://user:password@localhost/mobile_integration'
    )


# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}


def load_config(config_name=None):
    """Load the appropriate configuration based on the environment.
    
    Args:
        config_name: Name of the configuration to load
        
    Returns:
        Configuration object
    """
    if not config_name:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    return config.get(config_name, config['default'])
