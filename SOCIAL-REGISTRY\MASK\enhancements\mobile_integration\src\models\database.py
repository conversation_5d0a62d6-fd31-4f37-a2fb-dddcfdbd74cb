"""
Database configuration for the Mobile Integration module.

This module provides the SQLAlchemy database instance and initialization functions.
"""

from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

from ..utils.logging import get_logger

# Create logger
logger = get_logger('models.database')

# Create database instance
db = SQLAlchemy()

# Create migrate instance
migrate = Migrate()


def init_db(app):
    """Initialize the database with the Flask application.
    
    Args:
        app: Flask application instance
    """
    # Initialize SQLAlchemy with app
    db.init_app(app)
    
    # Initialize Flask-Migrate
    migrate.init_app(app, db)
    
    # Create tables if they don't exist
    with app.app_context():
        db.create_all()
        logger.info('Database initialized')


def reset_db(app):
    """Reset the database (for testing purposes).
    
    Args:
        app: Flask application instance
    """
    with app.app_context():
        db.drop_all()
        db.create_all()
        logger.warning('Database reset')
