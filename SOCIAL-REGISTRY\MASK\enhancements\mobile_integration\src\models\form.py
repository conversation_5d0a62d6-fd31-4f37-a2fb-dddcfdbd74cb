"""
Form model for the Mobile Integration module.

This module defines the Form model for questionnaire management.
"""

import uuid
import json
from datetime import datetime

from .database import db
from ..utils.logging import get_logger

# Create logger
logger = get_logger('models.form')


class Form(db.Model):
    """Form model for questionnaire management."""
    
    __tablename__ = 'forms'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    version = db.Column(db.String(20), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Form content
    cspro_file_path = db.Column(db.String(255))
    xlsform_file_path = db.Column(db.String(255))
    form_schema = db.Column(db.Text)  # JSON schema of the form
    
    # Metadata
    creator_id = db.Column(db.String(36), db.ForeignKey('users.id'))
    is_active = db.Column(db.Boolean, default=True)
    is_published = db.Column(db.Boolean, default=False)
    
    # Relationships
    # creator = db.relationship('User', backref='forms')
    deployments = db.relationship('FormDeployment', backref='form', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, title, version, description=None, creator_id=None):
        """Initialize a new form.
        
        Args:
            title: Form title
            version: Form version
            description: Form description
            creator_id: ID of the user who created the form
        """
        self.title = title
        self.version = version
        self.description = description
        self.creator_id = creator_id
    
    def set_schema(self, schema):
        """Set the form schema.
        
        Args:
            schema: Form schema as dictionary
        """
        self.form_schema = json.dumps(schema)
    
    def get_schema(self):
        """Get the form schema.
        
        Returns:
            Form schema as dictionary
        """
        if self.form_schema:
            return json.loads(self.form_schema)
        return None
    
    def publish(self):
        """Publish the form."""
        self.is_published = True
        db.session.commit()
        logger.info(f'Form published: {self.id} - {self.title}')
    
    def unpublish(self):
        """Unpublish the form."""
        self.is_published = False
        db.session.commit()
        logger.info(f'Form unpublished: {self.id} - {self.title}')
    
    def to_dict(self):
        """Convert form to dictionary.
        
        Returns:
            Dictionary representation of form
        """
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'version': self.version,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'creator_id': self.creator_id,
            'is_active': self.is_active,
            'is_published': self.is_published,
            'deployments': [d.to_dict() for d in self.deployments]
        }
    
    def __repr__(self):
        return f'<Form {self.title} v{self.version}>'


class FormDeployment(db.Model):
    """Model for tracking form deployments to mobile platforms."""
    
    __tablename__ = 'form_deployments'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    form_id = db.Column(db.String(36), db.ForeignKey('forms.id'), nullable=False)
    platform = db.Column(db.String(20), nullable=False)  # odk, surveycto, kobo
    platform_form_id = db.Column(db.String(255))  # ID of the form on the platform
    deployed_at = db.Column(db.DateTime, default=datetime.utcnow)
    deployed_by = db.Column(db.String(36), db.ForeignKey('users.id'))
    status = db.Column(db.String(20), default='pending')  # pending, success, failed
    status_message = db.Column(db.Text)
    
    def __init__(self, form_id, platform, deployed_by):
        """Initialize a new form deployment.
        
        Args:
            form_id: ID of the form being deployed
            platform: Target platform (odk, surveycto, kobo)
            deployed_by: ID of the user who initiated the deployment
        """
        self.form_id = form_id
        self.platform = platform
        self.deployed_by = deployed_by
    
    def set_status(self, status, message=None):
        """Update deployment status.
        
        Args:
            status: New status (pending, success, failed)
            message: Status message
        """
        self.status = status
        self.status_message = message
        db.session.commit()
        logger.info(f'Deployment status updated: {self.id} - {status}')
    
    def to_dict(self):
        """Convert deployment to dictionary.
        
        Returns:
            Dictionary representation of deployment
        """
        return {
            'id': self.id,
            'form_id': self.form_id,
            'platform': self.platform,
            'platform_form_id': self.platform_form_id,
            'deployed_at': self.deployed_at.isoformat() if self.deployed_at else None,
            'deployed_by': self.deployed_by,
            'status': self.status,
            'status_message': self.status_message
        }
    
    def __repr__(self):
        return f'<FormDeployment {self.form_id} to {self.platform}>'
