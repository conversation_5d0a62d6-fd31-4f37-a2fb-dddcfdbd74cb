"""
Submission model for the Mobile Integration module.

This module defines the Submission model for data collection.
"""

import uuid
import json
from datetime import datetime

from .database import db
from ..utils.logging import get_logger

# Create logger
logger = get_logger('models.submission')


class Submission(db.Model):
    """Submission model for data collection."""
    
    __tablename__ = 'submissions'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    form_id = db.Column(db.String(36), db.<PERSON><PERSON>ey('forms.id'), nullable=False)
    user_id = db.Column(db.String(36), db.<PERSON>Key('users.id'), nullable=False)
    platform = db.Column(db.String(20), nullable=False)  # odk, surveycto, kobo, cspro
    platform_submission_id = db.Column(db.String(255))  # ID of the submission on the platform
    
    # Submission data
    data = db.Column(db.Text)  # JSON data of the submission
    
    # Metadata
    submitted_at = db.Column(db.DateTime, nullable=False)
    received_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='received')  # received, processed, error
    status_message = db.Column(db.Text)
    
    # Geolocation
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    accuracy = db.Column(db.Float)
    
    # Synchronization
    is_synced = db.Column(db.Boolean, default=False)
    synced_at = db.Column(db.DateTime)
    sync_status = db.Column(db.String(20))
    
    # Relationships
    # form = db.relationship('Form', backref='submissions')
    # user = db.relationship('User', backref='submissions')
    
    def __init__(self, form_id, user_id, platform, submitted_at, data=None):
        """Initialize a new submission.
        
        Args:
            form_id: ID of the form
            user_id: ID of the user who submitted the data
            platform: Source platform (odk, surveycto, kobo, cspro)
            submitted_at: Timestamp when the data was submitted
            data: Submission data as dictionary
        """
        self.form_id = form_id
        self.user_id = user_id
        self.platform = platform
        self.submitted_at = submitted_at
        if data:
            self.set_data(data)
    
    def set_data(self, data):
        """Set the submission data.
        
        Args:
            data: Submission data as dictionary
        """
        self.data = json.dumps(data)
        
        # Extract geolocation if available
        if isinstance(data, dict):
            if 'location' in data:
                loc = data['location']
                if isinstance(loc, dict):
                    self.latitude = loc.get('latitude')
                    self.longitude = loc.get('longitude')
                    self.accuracy = loc.get('accuracy')
    
    def get_data(self):
        """Get the submission data.
        
        Returns:
            Submission data as dictionary
        """
        if self.data:
            return json.loads(self.data)
        return None
    
    def set_status(self, status, message=None):
        """Update submission status.
        
        Args:
            status: New status (received, processed, error)
            message: Status message
        """
        self.status = status
        self.status_message = message
        db.session.commit()
        logger.info(f'Submission status updated: {self.id} - {status}')
    
    def mark_synced(self):
        """Mark the submission as synced."""
        self.is_synced = True
        self.synced_at = datetime.utcnow()
        self.sync_status = 'success'
        db.session.commit()
        logger.info(f'Submission marked as synced: {self.id}')
    
    def to_dict(self):
        """Convert submission to dictionary.
        
        Returns:
            Dictionary representation of submission
        """
        return {
            'id': self.id,
            'form_id': self.form_id,
            'user_id': self.user_id,
            'platform': self.platform,
            'platform_submission_id': self.platform_submission_id,
            'submitted_at': self.submitted_at.isoformat() if self.submitted_at else None,
            'received_at': self.received_at.isoformat() if self.received_at else None,
            'status': self.status,
            'status_message': self.status_message,
            'location': {
                'latitude': self.latitude,
                'longitude': self.longitude,
                'accuracy': self.accuracy
            } if self.latitude and self.longitude else None,
            'is_synced': self.is_synced,
            'synced_at': self.synced_at.isoformat() if self.synced_at else None,
            'sync_status': self.sync_status
        }
    
    def __repr__(self):
        return f'<Submission {self.id} for form {self.form_id}>'


class SyncLog(db.Model):
    """Model for tracking synchronization events."""
    
    __tablename__ = 'sync_logs'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False)
    device_id = db.Column(db.String(255))
    sync_type = db.Column(db.String(20), nullable=False)  # pull, push
    started_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='in_progress')  # in_progress, success, failed
    items_sent = db.Column(db.Integer, default=0)
    items_received = db.Column(db.Integer, default=0)
    error_message = db.Column(db.Text)
    
    def __init__(self, user_id, sync_type, device_id=None):
        """Initialize a new sync log.
        
        Args:
            user_id: ID of the user performing the sync
            sync_type: Type of synchronization (pull, push)
            device_id: ID of the device
        """
        self.user_id = user_id
        self.sync_type = sync_type
        self.device_id = device_id
    
    def complete(self, status, items_sent=0, items_received=0, error_message=None):
        """Mark the sync as complete.
        
        Args:
            status: Final status (success, failed)
            items_sent: Number of items sent
            items_received: Number of items received
            error_message: Error message if failed
        """
        self.completed_at = datetime.utcnow()
        self.status = status
        self.items_sent = items_sent
        self.items_received = items_received
        self.error_message = error_message
        db.session.commit()
        logger.info(f'Sync completed: {self.id} - {status}')
    
    def to_dict(self):
        """Convert sync log to dictionary.
        
        Returns:
            Dictionary representation of sync log
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'device_id': self.device_id,
            'sync_type': self.sync_type,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'status': self.status,
            'items_sent': self.items_sent,
            'items_received': self.items_received,
            'error_message': self.error_message
        }
    
    def __repr__(self):
        return f'<SyncLog {self.id} - {self.sync_type}>'
