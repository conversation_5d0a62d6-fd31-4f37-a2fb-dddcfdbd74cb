"""
User model for the Mobile Integration module.

This module defines the User model for authentication and authorization.
"""

import uuid
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

from .database import db
from ..utils.logging import get_logger

# Create logger
logger = get_logger('models.user')


class User(db.Model):
    """User model for authentication and authorization."""
    
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)
    role = db.Column(db.String(20), default='fieldworker')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Platform-specific credentials
    odk_username = db.Column(db.String(64))
    odk_password_hash = db.Column(db.String(256))
    
    surveycto_username = db.Column(db.String(64))
    surveycto_password_hash = db.Column(db.String(256))
    
    kobo_username = db.Column(db.String(64))
    kobo_password_hash = db.Column(db.String(256))
    
    # Relationships
    # forms = db.relationship('Form', backref='creator', lazy=True)
    # submissions = db.relationship('Submission', backref='user', lazy=True)
    
    def __init__(self, username, email, password, role='fieldworker'):
        """Initialize a new user.
        
        Args:
            username: User's username
            email: User's email address
            password: User's password (will be hashed)
            role: User's role (default: fieldworker)
        """
        self.username = username
        self.email = email
        self.set_password(password)
        self.role = role
    
    def set_password(self, password):
        """Set the user's password hash.
        
        Args:
            password: Plain text password
        """
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if the provided password matches the hash.
        
        Args:
            password: Plain text password to check
            
        Returns:
            True if password matches, False otherwise
        """
        return check_password_hash(self.password_hash, password)
    
    def set_platform_password(self, platform, password):
        """Set a platform-specific password.
        
        Args:
            platform: Platform name (odk, surveycto, kobo)
            password: Plain text password
        """
        if platform == 'odk':
            self.odk_password_hash = generate_password_hash(password)
        elif platform == 'surveycto':
            self.surveycto_password_hash = generate_password_hash(password)
        elif platform == 'kobo':
            self.kobo_password_hash = generate_password_hash(password)
    
    def check_platform_password(self, platform, password):
        """Check a platform-specific password.
        
        Args:
            platform: Platform name (odk, surveycto, kobo)
            password: Plain text password to check
            
        Returns:
            True if password matches, False otherwise
        """
        if platform == 'odk' and self.odk_password_hash:
            return check_password_hash(self.odk_password_hash, password)
        elif platform == 'surveycto' and self.surveycto_password_hash:
            return check_password_hash(self.surveycto_password_hash, password)
        elif platform == 'kobo' and self.kobo_password_hash:
            return check_password_hash(self.kobo_password_hash, password)
        return False
    
    def update_last_login(self):
        """Update the user's last login timestamp."""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self):
        """Convert user to dictionary.
        
        Returns:
            Dictionary representation of user
        """
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'platforms': {
                'odk': bool(self.odk_username),
                'surveycto': bool(self.surveycto_username),
                'kobo': bool(self.kobo_username)
            }
        }
    
    @classmethod
    def authenticate(cls, username, password):
        """Authenticate a user with username and password.
        
        Args:
            username: User's username
            password: User's password
            
        Returns:
            User object if authentication successful, None otherwise
        """
        user = cls.query.filter_by(username=username, is_active=True).first()
        if user and user.check_password(password):
            user.update_last_login()
            logger.info(f'User authenticated: {username}')
            return user
        return None
    
    def __repr__(self):
        return f'<User {self.username}>'
