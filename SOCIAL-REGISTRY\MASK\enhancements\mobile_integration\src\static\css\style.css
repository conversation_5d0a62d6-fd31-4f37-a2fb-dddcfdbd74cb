/* 
 * Mobile Integration Module - Custom Styles
 * Modern UI with responsive design
 */

:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --success-color: #2ecc71;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #217dbb;
    text-decoration: none;
}

/* Header & Navigation */
.navbar {
    background-color: var(--dark-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.4rem;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 5rem 0;
    margin-bottom: 3rem;
    position: relative;
    overflow: hidden;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
}

/* Cards */
.feature-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

/* Buttons */
.btn {
    border-radius: 5px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #217dbb;
    border-color: #217dbb;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-success {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success:hover {
    background-color: #25a25a;
    border-color: #25a25a;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-outline-light:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: white;
    padding: 3rem 0 2rem;
    margin-top: 4rem;
}

.footer h5 {
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer h5::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer a {
    color: #ecf0f1;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--primary-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
    }
    
    .feature-card {
        margin-bottom: 1.5rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 1s ease-in-out;
}

.slide-up {
    animation: slideUp 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Code Blocks */
pre {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 1rem;
    border-left: 4px solid var(--primary-color);
}

code {
    color: var(--dark-color);
    font-family: 'Courier New', Courier, monospace;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
