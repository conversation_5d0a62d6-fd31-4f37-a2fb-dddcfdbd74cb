/**
 * Mobile Integration Module - Main JavaScript
 * Handles UI interactions and API calls
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Mobile Integration Module - UI Initialized');
    
    // Initialize animations
    initAnimations();
    
    // Add smooth scrolling for anchor links
    initSmoothScrolling();
    
    // Check API status
    checkApiStatus();
    
    // Add event listeners
    addEventListeners();
});

/**
 * Initialize animations for page elements
 */
function initAnimations() {
    // Add fade-in class to feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, 100 * index);
    });
    
    // Add slide-up animation to sections
    const sections = document.querySelectorAll('section');
    
    // Create an Intersection Observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('slide-up');
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });
    
    // Observe each section
    sections.forEach(section => {
        observer.observe(section);
    });
}

/**
 * Initialize smooth scrolling for anchor links
 */
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 70,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Check API status and update UI accordingly
 */
function checkApiStatus() {
    fetch('/api/status')
        .then(response => response.json())
        .then(data => {
            console.log('API Status:', data);
            
            // Update API status button if it exists
            const statusBtn = document.querySelector('a[href="/api/status"]');
            if (statusBtn && data.status === 'online') {
                statusBtn.classList.remove('btn-secondary');
                statusBtn.classList.add('btn-success');
                statusBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> API Online';
            }
        })
        .catch(error => {
            console.error('Error checking API status:', error);
            
            // Update API status button if it exists
            const statusBtn = document.querySelector('a[href="/api/status"]');
            if (statusBtn) {
                statusBtn.classList.remove('btn-success');
                statusBtn.classList.add('btn-danger');
                statusBtn.innerHTML = '<i class="fas fa-exclamation-circle me-1"></i> API Offline';
            }
        });
}

/**
 * Add event listeners to interactive elements
 */
function addEventListeners() {
    // Toggle active class on nav links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Add hover effect to feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
}

/**
 * Show a notification message
 * @param {string} message - The message to display
 * @param {string} type - The type of notification (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getIconForType(type)} me-2"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add to the DOM
    document.body.appendChild(notification);
    
    // Show with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Add close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

/**
 * Get the appropriate icon for notification type
 * @param {string} type - The type of notification
 * @returns {string} - The Font Awesome icon class
 */
function getIconForType(type) {
    switch (type) {
        case 'success':
            return 'fa-check-circle';
        case 'error':
            return 'fa-exclamation-circle';
        case 'warning':
            return 'fa-exclamation-triangle';
        case 'info':
        default:
            return 'fa-info-circle';
    }
}
