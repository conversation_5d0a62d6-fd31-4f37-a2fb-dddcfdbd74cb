<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Mobile Integration</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background-color: var(--dark-color);
            color: white;
            padding-top: 1rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .sidebar-collapsed {
            width: 70px;
        }
        
        .sidebar-header {
            padding: 0.5rem 1.5rem;
            margin-bottom: 1rem;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.25rem;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar-menu .menu-icon {
            margin-right: 1rem;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        
        .main-content-expanded {
            margin-left: 70px;
        }
        
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            background-color: white;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            display: inline-block;
            padding: 1rem;
            border-radius: 50%;
        }
        
        .bg-primary-light {
            background-color: rgba(52, 152, 219, 0.1);
            color: var(--primary-color);
        }
        
        .bg-success-light {
            background-color: rgba(46, 204, 113, 0.1);
            color: var(--secondary-color);
        }
        
        .bg-warning-light {
            background-color: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }
        
        .bg-danger-light {
            background-color: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
        }
        
        .user-dropdown img {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
            }
            
            .sidebar-collapsed {
                width: 0;
                overflow: hidden;
            }
            
            .main-content {
                margin-left: 70px;
            }
            
            .main-content-expanded {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h5 class="sidebar-title">
                <i class="fas fa-mobile-alt me-2"></i>
                <span class="menu-text">Mobile Integration</span>
            </h5>
        </div>
        
        <ul class="sidebar-menu">
            <li>
                <a href="#" class="active">
                    <i class="fas fa-tachometer-alt menu-icon"></i>
                    <span class="menu-text">Dashboard</span>
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-file-alt menu-icon"></i>
                    <span class="menu-text">Forms</span>
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-sync-alt menu-icon"></i>
                    <span class="menu-text">Synchronization</span>
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-database menu-icon"></i>
                    <span class="menu-text">Data</span>
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-chart-line menu-icon"></i>
                    <span class="menu-text">Monitoring</span>
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-users menu-icon"></i>
                    <span class="menu-text">Users</span>
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="fas fa-cog menu-icon"></i>
                    <span class="menu-text">Settings</span>
                </a>
            </li>
            <li>
                <a href="#" id="logoutBtn">
                    <i class="fas fa-sign-out-alt menu-icon"></i>
                    <span class="menu-text">Logout</span>
                </a>
            </li>
        </ul>
    </div>
    
    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <button class="btn btn-sm" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <div class="dropdown user-dropdown">
                <button class="btn dropdown-toggle d-flex align-items-center" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <img src="https://ui-avatars.com/api/?name=Admin+User&background=0D8ABC&color=fff" alt="User" class="me-2">
                    <span id="username">Admin User</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i> Profile</a></li>
                    <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i> Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" id="logoutDropdownBtn"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                </ul>
            </div>
        </div>
        
        <!-- Dashboard Content -->
        <div class="container-fluid">
            <h1 class="mb-4">Dashboard</h1>
            
            <!-- Stats Row -->
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0">24</h3>
                                <p class="text-muted mb-0">Active Forms</p>
                            </div>
                            <div class="stat-icon bg-primary-light">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0">1,254</h3>
                                <p class="text-muted mb-0">Submissions</p>
                            </div>
                            <div class="stat-icon bg-success-light">
                                <i class="fas fa-database"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0">42</h3>
                                <p class="text-muted mb-0">Field Workers</p>
                            </div>
                            <div class="stat-icon bg-warning-light">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="mb-0">98%</h3>
                                <p class="text-muted mb-0">Sync Rate</p>
                            </div>
                            <div class="stat-icon bg-danger-light">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="row mt-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Activity</th>
                                            <th>Form</th>
                                            <th>Time</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>John Doe</td>
                                            <td>Submitted data</td>
                                            <td>Household Survey</td>
                                            <td>10 min ago</td>
                                        </tr>
                                        <tr>
                                            <td>Jane Smith</td>
                                            <td>Synchronized</td>
                                            <td>Health Assessment</td>
                                            <td>25 min ago</td>
                                        </tr>
                                        <tr>
                                            <td>Admin</td>
                                            <td>Created form</td>
                                            <td>Education Survey</td>
                                            <td>1 hour ago</td>
                                        </tr>
                                        <tr>
                                            <td>Mike Johnson</td>
                                            <td>Submitted data</td>
                                            <td>Income Assessment</td>
                                            <td>2 hours ago</td>
                                        </tr>
                                        <tr>
                                            <td>Sarah Williams</td>
                                            <td>Synchronized</td>
                                            <td>Household Survey</td>
                                            <td>3 hours ago</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">Platform Status</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    ODK Collect
                                    <span class="badge bg-success rounded-pill">Online</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    SurveyCTO
                                    <span class="badge bg-success rounded-pill">Online</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    KoBoToolbox
                                    <span class="badge bg-warning rounded-pill">Syncing</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Survey Solutions
                                    <span class="badge bg-danger rounded-pill">Offline</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header bg-white">
                            <h5 class="card-title mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i> Create New Form
                                </button>
                                <button class="btn btn-success">
                                    <i class="fas fa-sync me-2"></i> Sync All Devices
                                </button>
                                <button class="btn btn-info text-white">
                                    <i class="fas fa-download me-2"></i> Export Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login';
                return;
            }
            
            // Sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('sidebar-collapsed');
                mainContent.classList.toggle('main-content-expanded');
            });
            
            // Logout functionality
            const logoutBtn = document.getElementById('logoutBtn');
            const logoutDropdownBtn = document.getElementById('logoutDropdownBtn');
            
            function logout() {
                localStorage.removeItem('token');
                window.location.href = '/login';
            }
            
            logoutBtn.addEventListener('click', logout);
            logoutDropdownBtn.addEventListener('click', logout);
        });
    </script>
</body>
</html>
