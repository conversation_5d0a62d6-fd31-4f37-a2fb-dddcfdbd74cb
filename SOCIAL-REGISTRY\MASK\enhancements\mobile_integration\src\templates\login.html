<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Mobile Integration</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    <style>
        .login-container {
            max-width: 450px;
            margin: 0 auto;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            background-color: white;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-form .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .login-form .btn-login {
            width: 100%;
            padding: 0.75rem;
            font-weight: 500;
        }
        
        .login-bg {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 2rem 0;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 1.5rem;
            color: #6c757d;
        }
        
        .platform-icons {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
        }
        
        .platform-icon {
            font-size: 1.5rem;
            margin: 0 0.5rem;
            color: var(--dark-color);
            transition: transform 0.3s ease, color 0.3s ease;
        }
        
        .platform-icon:hover {
            transform: scale(1.2);
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="login-bg">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="login-container">
                        <div class="login-header">
                            <h2 class="mb-3">
                                <i class="fas fa-mobile-alt me-2 text-primary"></i>
                                Mobile Integration
                            </h2>
                            <p class="text-muted">Sign in to access the mobile data collection platform</p>
                        </div>
                        
                        <form class="login-form" id="loginForm">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" placeholder="Username" required>
                                <label for="username">Username</label>
                            </div>
                            
                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" placeholder="Password" required>
                                <label for="password">Password</label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    Remember me
                                </label>
                                <a href="#" class="float-end">Forgot password?</a>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-login">
                                <i class="fas fa-sign-in-alt me-2"></i> Sign In
                            </button>
                            
                            <div class="alert alert-danger mt-3 d-none" id="loginError">
                                Invalid username or password. Please try again.
                            </div>
                        </form>
                        
                        <div class="login-footer">
                            <p>Supported Platforms</p>
                            <div class="platform-icons">
                                <i class="fas fa-tablet-alt platform-icon" title="ODK Collect"></i>
                                <i class="fas fa-mobile-alt platform-icon" title="SurveyCTO"></i>
                                <i class="fas fa-clipboard-list platform-icon" title="KoBoToolbox"></i>
                                <i class="fas fa-poll platform-icon" title="Survey Solutions"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center text-white mt-4">
                        <p>Don't have an account? Contact your administrator</p>
                        <a href="/" class="text-white">
                            <i class="fas fa-arrow-left me-1"></i> Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginError = document.getElementById('loginError');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                // Simple validation
                if (!username || !password) {
                    loginError.classList.remove('d-none');
                    return;
                }
                
                // Simulate API call
                fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Login failed');
                    }
                    return response.json();
                })
                .then(data => {
                    // Store token in localStorage
                    localStorage.setItem('token', data.access_token);
                    
                    // Redirect to dashboard
                    window.location.href = '/dashboard';
                })
                .catch(error => {
                    console.error('Error:', error);
                    loginError.classList.remove('d-none');
                });
            });
        });
    </script>
</body>
</html>
