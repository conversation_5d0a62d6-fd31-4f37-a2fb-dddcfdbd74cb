"""
Logging utilities for the Mobile Integration module.

This module provides logging configuration and helper functions.
"""

import os
import logging
from logging.handlers import RotatingFileHandler

from ..config.settings import load_config


def setup_logging(app=None):
    """Set up logging for the application.
    
    Args:
        app: Flask application instance
        
    Returns:
        Logger instance
    """
    config = load_config()
    log_level = getattr(logging, config.LOG_LEVEL)
    log_format = config.LOG_FORMAT
    
    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format=log_format
    )
    
    # Create file handler
    file_handler = RotatingFileHandler(
        'logs/mobile_integration.log',
        maxBytes=10485760,  # 10MB
        backupCount=10
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(logging.Formatter(log_format))
    
    # Get logger
    logger = logging.getLogger('mobile_integration')
    logger.addHandler(file_handler)
    
    # Add handler to Flask app if provided
    if app:
        app.logger.addHandler(file_handler)
    
    return logger


def get_logger(name=None):
    """Get a logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    if name:
        return logging.getLogger(f'mobile_integration.{name}')
    return logging.getLogger('mobile_integration')
