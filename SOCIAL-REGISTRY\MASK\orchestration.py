#!/usr/bin/env python3
"""
Social Registry System Orchestration

This module provides orchestration capabilities for the Social Registry System,
automating workflows and processes related to household survey data collection,
validation, and management.

The orchestration handles:
1. Sample management and assignment
2. Data collection workflow
3. Data validation and quality checks
4. Batch processing automation
5. Reporting and data export
"""

import os
import sys
import logging
import shutil
import subprocess
import datetime
from typing import Dict, List, Optional, Tuple, Union
import csv
import json
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("orchestration.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("SocialRegistryOrchestration")


class SocialRegistryOrchestrator:
    """
    Main orchestrator class for the Social Registry System.
    
    This class manages the workflow of the Social Registry System,
    providing methods for sample management, data collection,
    validation, batch processing, and reporting.
    """
    
    def __init__(self, base_dir: str = '.'):
        """
        Initialize the orchestrator with the base directory of the Social Registry System.
        
        Args:
            base_dir: Base directory where the Social Registry System is installed
        """
        self.base_dir = Path(base_dir).resolve()
        self.data_dir = self.base_dir / 'DATA'
        self.entry_dir = self.base_dir / 'ENTRY'
        self.ref_dir = self.base_dir / 'REF'
        self.dicts_dir = self.base_dir / 'DICTS'
        self.reports_dir = self.base_dir / 'REPORTS'
        self.backup_dir = self.base_dir / 'BACKUP'
        self.batch_dir = self.base_dir / 'bacth exclude EA'
        
        # Verify required directories exist
        self._verify_directories()
        
        # CSPro executable path (may need to be configured)
        self.cspro_path = self._find_cspro_executable()
        
        logger.info(f"Social Registry Orchestrator initialized at {self.base_dir}")
    
    def _verify_directories(self) -> None:
        """Verify that all required directories exist."""
        required_dirs = [
            self.data_dir, self.entry_dir, self.ref_dir,
            self.dicts_dir, self.reports_dir, self.backup_dir
        ]
        
        for directory in required_dirs:
            if not directory.exists():
                logger.error(f"Required directory not found: {directory}")
                raise FileNotFoundError(f"Required directory not found: {directory}")
    
    def _find_cspro_executable(self) -> str:
        """
        Find the CSPro executable on the system.
        
        Returns:
            Path to the CSPro executable
        """
        # Check common installation locations
        possible_paths = [
            r"C:\Program Files (x86)\CSPro 7.7\CSPro.exe",
            r"C:\Program Files\CSPro 7.7\CSPro.exe",
            r"C:\Program Files (x86)\CSPro 7.6\CSPro.exe",
            r"C:\Program Files\CSPro 7.6\CSPro.exe",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found CSPro executable at {path}")
                return path
        
        logger.warning("CSPro executable not found in common locations. Using 'CSPro' as command.")
        return "CSPro"
    
    def create_backup(self) -> str:
        """
        Create a backup of the current data.
        
        Returns:
            Path to the created backup directory
        """
        timestamp = datetime.datetime.now().strftime("%d%m%Y Hour_%H%M%S")
        backup_name = f"{timestamp}"
        backup_path = self.backup_dir / backup_name
        
        try:
            # Create backup directory
            os.makedirs(backup_path, exist_ok=True)
            
            # Backup DATA directory
            for file in self.data_dir.glob("*.csdb"):
                shutil.copy2(file, backup_path)
            
            # Backup WORK directory
            work_dir = self.base_dir / 'WORK'
            if work_dir.exists():
                for file in work_dir.glob("*.csdb"):
                    shutil.copy2(file, backup_path)
            
            logger.info(f"Created backup at {backup_path}")
            return str(backup_path)
        
        except Exception as e:
            logger.error(f"Backup creation failed: {e}")
            raise
    
    def run_cspro_application(self, app_path: str, pff_path: Optional[str] = None) -> bool:
        """
        Run a CSPro application.
        
        Args:
            app_path: Path to the CSPro application (.ent)
            pff_path: Optional path to a PFF file for the application
        
        Returns:
            True if the application was successfully launched, False otherwise
        """
        try:
            cmd = [self.cspro_path]
            
            # Add the application path
            cmd.append(str(app_path))
            
            # Add the PFF path if provided
            if pff_path:
                cmd.append(str(pff_path))
            
            logger.info(f"Launching CSPro application: {' '.join(cmd)}")
            
            # Launch the process
            subprocess.Popen(cmd)
            return True
        
        except Exception as e:
            logger.error(f"Failed to launch CSPro application: {e}")
            return False
    
    def launch_menu(self) -> bool:
        """
        Launch the menu system.
        
        Returns:
            True if the menu was successfully launched, False otherwise
        """
        menu_path = self.entry_dir / 'MENU.ent'
        menu_pff = self.entry_dir / 'MENU.pff'
        
        if not menu_path.exists():
            logger.error(f"Menu application not found at {menu_path}")
            return False
        
        return self.run_cspro_application(menu_path, menu_pff if menu_pff.exists() else None)
    
    def launch_data_entry(self) -> bool:
        """
        Launch the data entry application (SOCIAL.ent).
        
        Returns:
            True if the application was successfully launched, False otherwise
        """
        app_path = self.entry_dir / 'SOCIAL.ent'
        pff_path = self.entry_dir / 'SOCIAL.pff'
        
        if not app_path.exists():
            logger.error(f"Data entry application not found at {app_path}")
            return False
        
        return self.run_cspro_application(app_path, pff_path if pff_path.exists() else None)
    
    def launch_sample_management(self) -> bool:
        """
        Launch the sample management application (SAMPLE.ent).
        
        Returns:
            True if the application was successfully launched, False otherwise
        """
        app_path = self.entry_dir / 'SAMPLE.ent'
        pff_path = self.entry_dir / 'SAMPLE.pff'
        
        if not app_path.exists():
            logger.error(f"Sample management application not found at {app_path}")
            return False
        
        return self.run_cspro_application(app_path, pff_path if pff_path.exists() else None)
    
    def run_batch_process(self, batch_name: str) -> bool:
        """
        Run a batch process.
        
        Args:
            batch_name: Name of the batch file to run (without path)
        
        Returns:
            True if the batch process was successfully launched, False otherwise
        """
        batch_path = self.batch_dir / batch_name
        
        if not batch_path.exists():
            logger.error(f"Batch file not found at {batch_path}")
            return False
        
        return self.run_cspro_application(batch_path)
    
    def exclude_completed_eas(self) -> bool:
        """
        Run the batch process to exclude completed enumeration areas.
        
        Returns:
            True if the batch process was successfully launched, False otherwise
        """
        return self.run_batch_process('RemoveCompletedEAsampleINFO.bch')
    
    def remove_eas_from_sample(self) -> bool:
        """
        Run the batch process to remove specified enumeration areas from the sample.
        
        Returns:
            True if the batch process was successfully launched, False otherwise
        """
        return self.run_batch_process('RemoveEAfromSample.bch')
    
    def list_data_files(self) -> List[str]:
        """
        List all data files in the DATA directory.
        
        Returns:
            List of data file paths
        """
        return [str(file) for file in self.data_dir.glob("*.csdb")]
    
    def get_data_summary(self) -> Dict[str, int]:
        """
        Get a summary of the data files.
        
        Returns:
            Dictionary with region codes as keys and counts as values
        """
        summary = {}
        
        for file in self.data_dir.glob("M*.csdb"):
            # Extract region code from filename (e.g., M01010101.csdb -> 01)
            try:
                region_code = file.name[1:3]
                summary[region_code] = summary.get(region_code, 0) + 1
            except:
                logger.warning(f"Could not parse region code from {file.name}")
        
        return summary
    
    def generate_report(self, report_name: str) -> str:
        """
        Generate a report based on the current data.
        
        Args:
            report_name: Name to give the report file
        
        Returns:
            Path to the generated report
        """
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d")
        report_filename = f"{report_name} - {timestamp}.html"
        report_path = self.reports_dir / report_filename
        
        # Generate a simple HTML report with data summary
        try:
            summary = self.get_data_summary()
            
            with open(report_path, 'w') as f:
                f.write("<html><head><title>Social Registry System Report</title>")
                f.write("<style>body{font-family:Arial;margin:20px}table{border-collapse:collapse;width:100%}")
                f.write("th,td{text-align:left;padding:8px;border:1px solid #ddd}")
                f.write("th{background-color:#f2f2f2}tr:nth-child(even){background-color:#f9f9f9}</style>")
                f.write("</head><body>")
                f.write(f"<h1>{report_name}</h1>")
                f.write(f"<p>Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>")
                
                # Data summary
                f.write("<h2>Data Summary</h2>")
                f.write("<table><tr><th>Region Code</th><th>Number of Files</th></tr>")
                
                for region, count in sorted(summary.items()):
                    f.write(f"<tr><td>{region}</td><td>{count}</td></tr>")
                
                f.write("</table>")
                
                # Total count
                total = sum(summary.values())
                f.write(f"<p>Total number of data files: {total}</p>")
                
                f.write("</body></html>")
            
            logger.info(f"Generated report at {report_path}")
            return str(report_path)
            
        except Exception as e:
            logger.error(f"Failed to generate report: {e}")
            raise
    
    def export_data(self, output_format: str = "stata") -> bool:
        """
        Export data to various formats.
        
        Args:
            output_format: Format to export to (stata, spss, csv)
        
        Returns:
            True if export was successful, False otherwise
        """
        try:
            # Launch the export batch file
            export_batch = self.base_dir / 'EXPORT' / 'EXPORT.bch'
            
            if not export_batch.exists():
                logger.error(f"Export batch file not found at {export_batch}")
                return False
            
            return self.run_cspro_application(export_batch)
            
        except Exception as e:
            logger.error(f"Data export failed: {e}")
            return False


def main():
    """Main entry point for the orchestration script."""
    try:
        # Initialize the orchestrator
        orchestrator = SocialRegistryOrchestrator()
        
        # Create a backup
        backup_path = orchestrator.create_backup()
        logger.info(f"Created backup at {backup_path}")
        
        # Generate a data report
        report_path = orchestrator.generate_report("Data Report")
        logger.info(f"Generated report at {report_path}")
        
        # Launch the menu system
        if orchestrator.launch_menu():
            logger.info("Menu system launched successfully")
        else:
            logger.error("Failed to launch menu system")
        
    except Exception as e:
        logger.error(f"Orchestration error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())