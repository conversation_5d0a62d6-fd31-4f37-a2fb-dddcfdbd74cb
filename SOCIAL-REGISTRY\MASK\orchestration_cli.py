#!/usr/bin/env python3
"""
Social Registry System Orchestration CLI

This module provides a command-line interface for the Social Registry System orchestration,
allowing users to interact with the system through the command line.

Usage:
  python orchestration_cli.py [command] [options]

Commands:
  launch-menu           - Launch the menu system
  launch-data-entry     - Launch the data entry application
  launch-sample         - Launch the sample management application
  backup                - Create a backup of the current data
  report                - Generate a report of the current data
  exclude-completed-eas - Run batch to exclude completed enumeration areas
  remove-eas            - Run batch to remove enumeration areas from sample
  export-data           - Export data to specified format
  help                  - Show this help message
"""

import sys
import os
import argparse
import logging
import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path

# Import the orchestration modules
from orchestration import SocialRegistryOrchestrator
from orchestration_config import get_config
import orchestration_utils as utils


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("orchestration_cli.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("SocialRegistryOrchestration.CLI")


def setup_parser() -> argparse.ArgumentParser:
    """
    Set up the command-line argument parser.
    
    Returns:
        Configured ArgumentParser instance
    """
    parser = argparse.ArgumentParser(
        description="Social Registry System Orchestration CLI",
        epilog="For more information, use the 'help' command"
    )
    
    # Add main command argument
    parser.add_argument(
        "command",
        choices=[
            "launch-menu",
            "launch-data-entry",
            "launch-sample",
            "backup",
            "report",
            "exclude-completed-eas",
            "remove-eas",
            "export-data",
            "help"
        ],
        help="Command to execute"
    )
    
    # Add general options
    parser.add_argument(
        "--base-dir",
        help="Base directory of the Social Registry System"
    )
    
    # Add command-specific options
    parser.add_argument(
        "--report-name",
        help="Name for the generated report"
    )
    parser.add_argument(
        "--export-format",
        choices=["stata", "spss", "csv", "json"],
        help="Format for data export"
    )
    parser.add_argument(
        "--backup-name",
        help="Name prefix for the backup"
    )
    
    return parser


def display_help() -> None:
    """Display the help information for the CLI."""
    print(__doc__)


def handle_launch_menu(orchestrator: SocialRegistryOrchestrator, args: argparse.Namespace) -> int:
    """
    Handle the launch-menu command.
    
    Args:
        orchestrator: SocialRegistryOrchestrator instance
        args: Command-line arguments
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    logger.info("Launching menu system...")
    if orchestrator.launch_menu():
        logger.info("Menu system launched successfully")
        return 0
    else:
        logger.error("Failed to launch menu system")
        return 1


def handle_launch_data_entry(orchestrator: SocialRegistryOrchestrator, args: argparse.Namespace) -> int:
    """
    Handle the launch-data-entry command.
    
    Args:
        orchestrator: SocialRegistryOrchestrator instance
        args: Command-line arguments
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    logger.info("Launching data entry application...")
    if orchestrator.launch_data_entry():
        logger.info("Data entry application launched successfully")
        return 0
    else:
        logger.error("Failed to launch data entry application")
        return 1


def handle_launch_sample(orchestrator: SocialRegistryOrchestrator, args: argparse.Namespace) -> int:
    """
    Handle the launch-sample command.
    
    Args:
        orchestrator: SocialRegistryOrchestrator instance
        args: Command-line arguments
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    logger.info("Launching sample management application...")
    if orchestrator.launch_sample_management():
        logger.info("Sample management application launched successfully")
        return 0
    else:
        logger.error("Failed to launch sample management application")
        return 1


def handle_backup(orchestrator: SocialRegistryOrchestrator, args: argparse.Namespace) -> int:
    """
    Handle the backup command.
    
    Args:
        orchestrator: SocialRegistryOrchestrator instance
        args: Command-line arguments
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    try:
        backup_name = args.backup_name if args.backup_name else ""
        logger.info(f"Creating backup with prefix '{backup_name}'...")
        
        backup_path = orchestrator.create_backup()
        logger.info(f"Backup created successfully at {backup_path}")
        return 0
    except Exception as e:
        logger.error(f"Backup creation failed: {e}")
        return 1


def handle_report(orchestrator: SocialRegistryOrchestrator, args: argparse.Namespace) -> int:
    """
    Handle the report command.
    
    Args:
        orchestrator: SocialRegistryOrchestrator instance
        args: Command-line arguments
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    try:
        report_name = args.report_name if args.report_name else "Data Report"
        logger.info(f"Generating report '{report_name}'...")
        
        report_path = orchestrator.generate_report(report_name)
        logger.info(f"Report generated successfully at {report_path}")
        
        # Try to open the report in the default browser
        try:
            import webbrowser
            webbrowser.open(f"file://{os.path.abspath(report_path)}")
        except:
            pass  # Ignore if browser opening fails
        
        return 0
    except Exception as e:
        logger.error(f"Report generation failed: {e}")
        return 1


def handle_exclude_completed_eas(orchestrator: SocialRegistryOrchestrator, args: argparse.Namespace) -> int:
    """
    Handle the exclude-completed-eas command.
    
    Args:
        orchestrator: SocialRegistryOrchestrator instance
        args: Command-line arguments
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    logger.info("Running batch to exclude completed enumeration areas...")
    if orchestrator.exclude_completed_eas():
        logger.info("Batch process launched successfully")
        return 0
    else:
        logger.error("Failed to launch batch process")
        return 1


def handle_remove_eas(orchestrator: SocialRegistryOrchestrator, args: argparse.Namespace) -> int:
    """
    Handle the remove-eas command.
    
    Args:
        orchestrator: SocialRegistryOrchestrator instance
        args: Command-line arguments
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    logger.info("Running batch to remove enumeration areas from sample...")
    if orchestrator.remove_eas_from_sample():
        logger.info("Batch process launched successfully")
        return 0
    else:
        logger.error("Failed to launch batch process")
        return 1


def handle_export_data(orchestrator: SocialRegistryOrchestrator, args: argparse.Namespace) -> int:
    """
    Handle the export-data command.
    
    Args:
        orchestrator: SocialRegistryOrchestrator instance
        args: Command-line arguments
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    export_format = args.export_format if args.export_format else "stata"
    logger.info(f"Exporting data to {export_format} format...")
    
    if orchestrator.export_data(export_format):
        logger.info("Data export process launched successfully")
        return 0
    else:
        logger.error("Failed to launch data export process")
        return 1


def main() -> int:
    """
    Main entry point for the CLI.
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    parser = setup_parser()
    args = parser.parse_args()
    
    # Special case for help command
    if args.command == "help":
        display_help()
        return 0
    
    try:
        # Initialize the orchestrator
        base_dir = args.base_dir if args.base_dir else "."
        orchestrator = SocialRegistryOrchestrator(base_dir)
        
        # Handle the requested command
        command_handlers = {
            "launch-menu": handle_launch_menu,
            "launch-data-entry": handle_launch_data_entry,
            "launch-sample": handle_launch_sample,
            "backup": handle_backup,
            "report": handle_report,
            "exclude-completed-eas": handle_exclude_completed_eas,
            "remove-eas": handle_remove_eas,
            "export-data": handle_export_data,
        }
        
        if args.command in command_handlers:
            return command_handlers[args.command](orchestrator, args)
        else:
            logger.error(f"Unknown command: {args.command}")
            return 1
        
    except Exception as e:
        logger.error(f"Command execution failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())