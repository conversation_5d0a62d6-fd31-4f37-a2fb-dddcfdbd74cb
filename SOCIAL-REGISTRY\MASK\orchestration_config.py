"""
Social Registry System Orchestration Configuration

This module contains configuration settings for the Social Registry System
orchestration module, including paths, system settings, and workflow parameters.
"""

from typing import Dict, List, Any
import os
from pathlib import Path

# Base configuration
BASE_CONFIG: Dict[str, Any] = {
    # System paths
    "cspro_paths": [
        r"C:\Program Files (x86)\CSPro 7.7\CSPro.exe",
        r"C:\Program Files\CSPro 7.7\CSPro.exe",
        r"C:\Program Files (x86)\CSPro 7.6\CSPro.exe",
        r"C:\Program Files\CSPro 7.6\CSPro.exe",
    ],
    
    # Directory structure
    "directories": {
        "data": "DATA",
        "entry": "ENTRY",
        "ref": "REF",
        "dicts": "DICTS",
        "reports": "REPORTS",
        "backup": "BACKUP",
        "batch": "bacth exclude EA",
        "export": "EXPORT",
        "work": "WORK",
    },
    
    # Backup settings
    "backup": {
        "automatic": True,
        "interval_hours": 24,
        "keep_backups": 10,  # Number of backups to keep
        "backup_data": True,
        "backup_work": True,
        "backup_entry": False,  # Entry files don't change often
    },
    
    # Data validation settings
    "validation": {
        "validate_on_export": True,
        "strict_mode": False,  # Strict mode requires all validations to pass
        "validation_rules": [
            "check_household_composition",
            "check_age_relationships",
            "check_employment_status",
            "check_education_levels",
            "check_housing_consistency",
        ],
    },
    
    # Reporting settings
    "reporting": {
        "default_format": "html",
        "available_formats": ["html", "csv", "pdf"],
        "standard_reports": [
            "data_summary",
            "completion_status",
            "error_report",
            "interviewer_performance",
        ],
    },
    
    # Export settings
    "export": {
        "default_format": "stata",
        "available_formats": ["stata", "spss", "csv", "json"],
        "export_structure": {
            "household": "HOUSEHOLD1.CSDB",
            "individual": "INDIVIDUAL1.CSDB",
        },
    },
    
    # Application files
    "applications": {
        "menu": {
            "entry": "MENU.ent",
            "pff": "MENU.pff",
        },
        "sample": {
            "entry": "SAMPLE.ent",
            "pff": "SAMPLE.pff",
        },
        "social": {
            "entry": "SOCIAL.ent",
            "pff": "SOCIAL.pff",
        },
    },
    
    # Batch processes
    "batch_processes": {
        "remove_completed_eas": "RemoveCompletedEAsampleINFO.bch",
        "remove_eas": "RemoveEAfromSample.bch",
        "export_data": "EXPORT.bch",
    },
}


def get_config() -> Dict[str, Any]:
    """
    Get the configuration settings, with environment-specific overrides.
    
    Returns:
        Dict containing configuration settings
    """
    config = BASE_CONFIG.copy()
    
    # Check for environment-specific configuration file
    env_config_path = Path(__file__).parent / "orchestration_config_env.py"
    if env_config_path.exists():
        # If environment-specific config exists, import and merge it
        import importlib.util
        spec = importlib.util.spec_from_file_location("env_config", env_config_path)
        env_config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(env_config_module)
        
        # Merge environment config with base config
        if hasattr(env_config_module, "ENV_CONFIG"):
            env_config = env_config_module.ENV_CONFIG
            _merge_configs(config, env_config)
    
    # Check for environment variables that might override config
    # Format: SRS_CONFIG_SECTION_KEY (e.g., SRS_CONFIG_BACKUP_AUTOMATIC)
    for env_name, env_value in os.environ.items():
        if env_name.startswith("SRS_CONFIG_"):
            parts = env_name[11:].lower().split("_", 1)
            if len(parts) == 2:
                section, key = parts
                if section in config and key in config[section]:
                    # Convert value to appropriate type based on existing config
                    config[section][key] = _convert_value(env_value, config[section][key])
    
    return config


def _merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> None:
    """
    Merge override_config into base_config recursively.
    
    Args:
        base_config: Base configuration dictionary to merge into
        override_config: Override configuration dictionary
    """
    for key, value in override_config.items():
        if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
            _merge_configs(base_config[key], value)
        else:
            base_config[key] = value


def _convert_value(value_str: str, existing_value: Any) -> Any:
    """
    Convert string value to the appropriate type based on existing value.
    
    Args:
        value_str: String value to convert
        existing_value: Existing value to determine the type
    
    Returns:
        Converted value with appropriate type
    """
    if isinstance(existing_value, bool):
        return value_str.lower() in ("true", "yes", "1", "on")
    elif isinstance(existing_value, int):
        return int(value_str)
    elif isinstance(existing_value, float):
        return float(value_str)
    elif isinstance(existing_value, list):
        return value_str.split(",")
    else:
        return value_str