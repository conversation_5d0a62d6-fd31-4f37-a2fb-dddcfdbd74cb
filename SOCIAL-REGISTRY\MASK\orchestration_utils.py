"""
Social Registry System Orchestration Utilities

This module provides utility functions for the Social Registry System orchestration,
including file operations, validation routines, and helper functions.
"""

import os
import sys
import logging
import datetime
import shutil
import re
import subprocess
from typing import Dict, List, Optional, Tuple, Union, Any
from pathlib import Path


logger = logging.getLogger("SocialRegistryOrchestration.utils")


def find_cspro_installation() -> Optional[str]:
    """
    Find the CSPro installation on the system.
    
    Returns:
        Path to CSPro executable if found, None otherwise
    """
    # Common installation paths
    possible_paths = [
        r"C:\Program Files (x86)\CSPro 7.7\CSPro.exe",
        r"C:\Program Files\CSPro 7.7\CSPro.exe",
        r"C:\Program Files (x86)\CSPro 7.6\CSPro.exe",
        r"C:\Program Files\CSPro 7.6\CSPro.exe",
    ]
    
    # Check registry for CSPro installation
    try:
        import winreg
        registry_paths = [
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\CSPro.exe"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\App Paths\CSPro.exe"),
        ]
        
        for reg_key, reg_path in registry_paths:
            try:
                with winreg.OpenKey(reg_key, reg_path) as key:
                    path, _ = winreg.QueryValueEx(key, "")
                    if os.path.exists(path):
                        return path
            except:
                continue
    except:
        pass
    
    # Check standard installation paths
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # Check PATH environment variable
    for path_dir in os.environ.get("PATH", "").split(os.pathsep):
        cspro_path = os.path.join(path_dir, "CSPro.exe")
        if os.path.exists(cspro_path):
            return cspro_path
    
    # Not found
    return None


def list_csdb_files(directory: Union[str, Path]) -> List[Path]:
    """
    List all CSPro database files in a directory.
    
    Args:
        directory: Directory to search for .csdb files
    
    Returns:
        List of paths to .csdb files
    """
    directory = Path(directory)
    return list(directory.glob("*.csdb"))


def parse_data_filename(filename: str) -> Dict[str, str]:
    """
    Parse a data filename to extract region, district, ward, and settlement codes.
    
    Args:
        filename: Filename to parse (e.g., "M01010101.csdb")
    
    Returns:
        Dictionary with parsed components
    """
    pattern = r"M(\d{2})(\d{2})(\d{2})(\d{2})\.csdb"
    match = re.match(pattern, os.path.basename(filename))
    
    if match:
        return {
            "region": match.group(1),
            "district": match.group(2),
            "ward": match.group(3),
            "settlement": match.group(4),
        }
    
    return {}


def create_timestamped_directory(base_dir: Union[str, Path], prefix: str = "") -> Path:
    """
    Create a timestamped directory.
    
    Args:
        base_dir: Base directory where the timestamped directory will be created
        prefix: Optional prefix for the directory name
    
    Returns:
        Path to the created directory
    """
    base_dir = Path(base_dir)
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    directory_name = f"{prefix}_{timestamp}" if prefix else timestamp
    new_dir = base_dir / directory_name
    
    os.makedirs(new_dir, exist_ok=True)
    return new_dir


def is_cspro_running() -> bool:
    """
    Check if CSPro is currently running.
    
    Returns:
        True if CSPro is running, False otherwise
    """
    try:
        if sys.platform == "win32":
            # Windows
            output = subprocess.check_output("tasklist /fi \"imagename eq CSPro.exe\"", shell=True)
            return b"CSPro.exe" in output
        else:
            # Linux/macOS (CSPro is primarily Windows-based, but for completeness)
            output = subprocess.check_output(["ps", "aux"], universal_newlines=True)
            return "CSPro" in output
    except:
        return False


def check_directory_structure(base_dir: Union[str, Path], required_dirs: List[str]) -> List[str]:
    """
    Check if all required directories exist.
    
    Args:
        base_dir: Base directory of the Social Registry System
        required_dirs: List of required directory names
    
    Returns:
        List of missing directories
    """
    base_dir = Path(base_dir)
    missing = []
    
    for dir_name in required_dirs:
        dir_path = base_dir / dir_name
        if not dir_path.exists() or not dir_path.is_dir():
            missing.append(dir_name)
    
    return missing


def clean_old_backups(backup_dir: Union[str, Path], keep_count: int = 10) -> int:
    """
    Clean old backups, keeping only the specified number of most recent ones.
    
    Args:
        backup_dir: Directory containing backups
        keep_count: Number of most recent backups to keep
    
    Returns:
        Number of backups deleted
    """
    backup_dir = Path(backup_dir)
    if not backup_dir.exists():
        return 0
    
    # Get all backup directories
    backup_folders = [f for f in backup_dir.iterdir() if f.is_dir()]
    
    # Sort by modification time (most recent first)
    backup_folders.sort(key=lambda f: f.stat().st_mtime, reverse=True)
    
    # Keep only the most recent ones
    to_delete = backup_folders[keep_count:]
    
    # Delete old backups
    for folder in to_delete:
        try:
            shutil.rmtree(folder)
            logger.info(f"Deleted old backup: {folder}")
        except Exception as e:
            logger.error(f"Failed to delete backup {folder}: {e}")
    
    return len(to_delete)


def extract_region_stats(data_dir: Union[str, Path]) -> Dict[str, Dict[str, int]]:
    """
    Extract statistics about regions from data files.
    
    Args:
        data_dir: Directory containing data files
    
    Returns:
        Dictionary with region statistics
    """
    data_dir = Path(data_dir)
    stats = {}
    
    for file in data_dir.glob("M*.csdb"):
        parsed = parse_data_filename(file.name)
        
        if parsed and "region" in parsed:
            region = parsed["region"]
            district = parsed["district"]
            
            if region not in stats:
                stats[region] = {
                    "files": 0,
                    "districts": {},
                    "size_bytes": 0,
                }
            
            # Update region stats
            stats[region]["files"] += 1
            stats[region]["size_bytes"] += file.stat().st_size
            
            # Update district stats
            if district not in stats[region]["districts"]:
                stats[region]["districts"][district] = 0
            stats[region]["districts"][district] += 1
    
    return stats


def format_bytes(size_bytes: int) -> str:
    """
    Format bytes to human-readable size.
    
    Args:
        size_bytes: Size in bytes
    
    Returns:
        Human-readable size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ("B", "KB", "MB", "GB", "TB")
    i = int(math.log(size_bytes, 1024))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    
    return f"{s} {size_names[i]}"


def launch_cspro_with_args(cspro_path: str, app_path: str, pff_path: Optional[str] = None) -> bool:
    """
    Launch CSPro with the specified application and optional PFF file.
    
    Args:
        cspro_path: Path to CSPro executable
        app_path: Path to CSPro application (.ent, .bch)
        pff_path: Optional path to PFF file
    
    Returns:
        True if successfully launched, False otherwise
    """
    try:
        cmd = [cspro_path, app_path]
        
        if pff_path:
            cmd.append(pff_path)
        
        subprocess.Popen(cmd)
        return True
    except Exception as e:
        logger.error(f"Failed to launch CSPro: {e}")
        return False


def monitor_cspro_process() -> bool:
    """
    Monitor CSPro process to see if it's running.
    
    Returns:
        True if CSPro is running, False otherwise
    """
    return is_cspro_running()


def create_sample_report(data_dir: Union[str, Path], output_path: Union[str, Path]) -> bool:
    """
    Create a sample report based on data files.
    
    Args:
        data_dir: Directory containing data files
        output_path: Path where the report will be saved
    
    Returns:
        True if report was created successfully, False otherwise
    """
    try:
        data_dir = Path(data_dir)
        output_path = Path(output_path)
        
        # Get region statistics
        stats = extract_region_stats(data_dir)
        
        # Create HTML report
        with open(output_path, 'w') as f:
            # HTML header
            f.write("""<!DOCTYPE html>
<html>
<head>
    <title>Social Registry System - Data Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #2c3e50; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { text-align: left; padding: 8px; }
        th { background-color: #3498db; color: white; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        .summary { background-color: #eaf2f8; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Social Registry System - Data Report</h1>
    <p>Generated on: """ + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """</p>
    
    <div class="summary">
        <h2>Summary</h2>
        <p>Total Regions: """ + str(len(stats)) + """</p>
        <p>Total Files: """ + str(sum(s["files"] for s in stats.values())) + """</p>
    </div>
    
    <h2>Region Details</h2>
    <table>
        <tr>
            <th>Region</th>
            <th>Files</th>
            <th>Districts</th>
            <th>Size</th>
        </tr>
""")
            
            # Add region rows
            for region, data in sorted(stats.items()):
                f.write(f"""
        <tr>
            <td>{region}</td>
            <td>{data['files']}</td>
            <td>{len(data['districts'])}</td>
            <td>{format_bytes(data['size_bytes'])}</td>
        </tr>""")
            
            # Close table and HTML
            f.write("""
    </table>
    
    <h2>District Details</h2>
""")
            
            # Add district details for each region
            for region, data in sorted(stats.items()):
                f.write(f"""
    <h3>Region {region}</h3>
    <table>
        <tr>
            <th>District</th>
            <th>Files</th>
        </tr>""")
                
                # Add district rows
                for district, count in sorted(data["districts"].items()):
                    f.write(f"""
        <tr>
            <td>{district}</td>
            <td>{count}</td>
        </tr>""")
                
                # Close district table
                f.write("""
    </table>""")
            
            # End of HTML
            f.write("""
</body>
</html>""")
        
        return True
    
    except Exception as e:
        logger.error(f"Failed to create sample report: {e}")
        return False


def validate_csdb_file(file_path: Union[str, Path]) -> List[str]:
    """
    Validate a CSDB file for common issues.
    
    Args:
        file_path: Path to the CSDB file
    
    Returns:
        List of issues found (empty if no issues)
    """
    issues = []
    file_path = Path(file_path)
    
    # Check if file exists
    if not file_path.exists():
        issues.append(f"File does not exist: {file_path}")
        return issues
    
    # Check file size
    file_size = file_path.stat().st_size
    if file_size == 0:
        issues.append(f"File is empty: {file_path}")
    elif file_size < 1024:  # Less than 1KB
        issues.append(f"File is suspiciously small: {file_path} ({format_bytes(file_size)})")
    
    # TODO: Add more validation checks specific to CSDB format
    # This would require CSPro-specific libraries or tools
    
    return issues


# Import here to avoid circular imports
import math  # For format_bytes function