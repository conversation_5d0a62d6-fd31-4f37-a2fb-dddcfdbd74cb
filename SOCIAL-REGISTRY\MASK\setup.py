#!/usr/bin/env python3
"""
Social Registry System Orchestration Setup

This script sets up the Python environment for the Social Registry System orchestration.
It installs required packages and configures the environment.
"""

import sys
import subprocess
import os
from pathlib import Path


def check_python_version() -> bool:
    """
    Check if the Python version is compatible.
    
    Returns:
        True if the Python version is compatible, False otherwise
    """
    required_version = (3, 6)  # Minimum required Python version
    current_version = sys.version_info
    
    if current_version.major < required_version[0] or \
       (current_version.major == required_version[0] and current_version.minor < required_version[1]):
        print(f"Error: Python {required_version[0]}.{required_version[1]} or higher is required.")
        print(f"Current Python version: {current_version.major}.{current_version.minor}.{current_version.micro}")
        return False
    
    return True


def install_requirements() -> bool:
    """
    Install required Python packages.
    
    Returns:
        True if installation was successful, False otherwise
    """
    # Define required packages
    requirements = [
        "pathlib",  # For better path handling
        "PyYAML",   # For configuration handling
        "tqdm",     # For progress bars
    ]
    
    print("Installing required packages...")
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install required packages
        for package in requirements:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        
        print("All required packages installed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing packages: {e}")
        return False


def create_config_file() -> bool:
    """
    Create environment-specific configuration file if it doesn't exist.
    
    Returns:
        True if successful, False otherwise
    """
    config_path = Path("orchestration_config_env.py")
    
    if config_path.exists():
        print(f"Environment configuration file already exists at {config_path}")
        return True
    
    try:
        with open(config_path, "w") as f:
            f.write("""\"\"\"
Environment-specific configuration for Social Registry System Orchestration.
This file is automatically generated and can be modified to override base configuration.
\"\"\"

from typing import Dict, List, Any

# Environment-specific configuration
ENV_CONFIG: Dict[str, Any] = {
    # Override any base configuration settings here
    
    # Example: Change CSPro paths for this environment
    # "cspro_paths": [
    #     r"C:\\Custom\\Path\\To\\CSPro.exe",
    # ],
    
    # Example: Modify backup settings
    # "backup": {
    #     "automatic": True,
    #     "interval_hours": 12,  # More frequent backups
    # },
}
""")
        print(f"Created environment configuration file at {config_path}")
        return True
    except Exception as e:
        print(f"Error creating environment configuration file: {e}")
        return False


def setup_logging_directory() -> bool:
    """
    Set up the logging directory.
    
    Returns:
        True if successful, False otherwise
    """
    log_dir = Path("logs")
    
    try:
        if not log_dir.exists():
            log_dir.mkdir(parents=True)
            print(f"Created logging directory at {log_dir}")
        else:
            print(f"Logging directory already exists at {log_dir}")
        
        return True
    except Exception as e:
        print(f"Error setting up logging directory: {e}")
        return False


def create_batch_files() -> bool:
    """
    Create Windows batch files for easy launching.
    
    Returns:
        True if successful, False otherwise
    """
    try:
        # Create batch file for launching menu
        with open("launch_menu.bat", "w") as f:
            f.write(f"@echo off\r\n")
            f.write(f"echo Launching Social Registry System Menu...\r\n")
            f.write(f"python orchestration_cli.py launch-menu\r\n")
            f.write(f"pause\r\n")
        
        # Create batch file for data entry
        with open("launch_data_entry.bat", "w") as f:
            f.write(f"@echo off\r\n")
            f.write(f"echo Launching Social Registry System Data Entry...\r\n")
            f.write(f"python orchestration_cli.py launch-data-entry\r\n")
            f.write(f"pause\r\n")
        
        # Create batch file for sample management
        with open("launch_sample.bat", "w") as f:
            f.write(f"@echo off\r\n")
            f.write(f"echo Launching Social Registry System Sample Management...\r\n")
            f.write(f"python orchestration_cli.py launch-sample\r\n")
            f.write(f"pause\r\n")
        
        # Create batch file for backup
        with open("create_backup.bat", "w") as f:
            f.write(f"@echo off\r\n")
            f.write(f"echo Creating backup of Social Registry System data...\r\n")
            f.write(f"python orchestration_cli.py backup\r\n")
            f.write(f"pause\r\n")
        
        # Create batch file for report generation
        with open("generate_report.bat", "w") as f:
            f.write(f"@echo off\r\n")
            f.write(f"echo Generating Social Registry System report...\r\n")
            f.write(f"python orchestration_cli.py report\r\n")
            f.write(f"pause\r\n")
        
        print("Created batch files for easy launching")
        return True
    except Exception as e:
        print(f"Error creating batch files: {e}")
        return False


def main() -> int:
    """
    Main setup function.
    
    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    print("Social Registry System Orchestration Setup")
    print("=========================================")
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install required packages
    if not install_requirements():
        return 1
    
    # Create environment-specific configuration file
    if not create_config_file():
        return 1
    
    # Set up logging directory
    if not setup_logging_directory():
        return 1
    
    # Create batch files for easy launching
    if not create_batch_files():
        return 1
    
    print("\nSetup completed successfully!")
    print("You can now use the orchestration tools for the Social Registry System.")
    print("\nAvailable batch files:")
    print("  - launch_menu.bat - Launch the menu system")
    print("  - launch_data_entry.bat - Launch the data entry application")
    print("  - launch_sample.bat - Launch the sample management application")
    print("  - create_backup.bat - Create a backup of the data")
    print("  - generate_report.bat - Generate a report of the data")
    print("\nOr use the command-line interface directly:")
    print("  python orchestration_cli.py [command] [options]")
    print("\nFor more information, run:")
    print("  python orchestration_cli.py help")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())